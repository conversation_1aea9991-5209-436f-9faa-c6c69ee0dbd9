# Belinda E-commerce API Specification

This document provides a comprehensive specification for the Belinda E-commerce API, designed to standardize communication between the frontend and backend teams.

## Base URL

All API endpoints are relative to the base URL:

```
/api
```

## Authentication

Most API endpoints require authentication. The application uses Firebase Authentication with JWT tokens.

### Authentication Methods

1. **Session Cookie Authentication**
   - Used for browser-based requests
   - Session cookie named `session` must be present in requests
   - Obtained after successful login

2. **Bearer Token Authentication**
   - Used for programmatic API access
   - Include token in Authorization header: `Authorization: Bearer {token}`

### Authorization

The API uses role-based access control with the following roles:
- `admin`: Full access to all endpoints
- `editor`: Can manage products, categories, and view orders
- `viewer`: Read-only access to admin dashboard data
- `customer`: Access to their own orders and account information

Permissions are enforced using Firebase custom claims.

## Common Response Format

All API responses follow a standard format:

### Success Response

```json
{
  "success": true,
  "data": {
    // Response data specific to the endpoint
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": "Error message",
  "details": {
    // Optional detailed error information
  }
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request succeeded |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server-side error |

## API Endpoints

### Products

#### Get Products

```
GET /api/admin/products
```

Retrieves a list of products with optional filtering and pagination.

**Permissions Required:** `read:products`

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Get a specific product by ID |
| category | string | Filter by category ID |
| status | string | Filter by status (active, draft, archived) |
| featured | boolean | Filter by featured status |
| search | string | Search in product name and description |
| sortBy | string | Field to sort by (name, price, createdAt) |
| sortOrder | string | Sort order (asc, desc) |
| limit | number | Number of results to return (default: 20) |
| startAfter | string | Cursor for pagination |

**Response:**

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "product123",
        "name": "Product Name",
        "description": "Product description",
        "price": 99.99,
        "compareAtPrice": 129.99,
        "images": ["https://example.com/image1.jpg"],
        "category": "Category Name",
        "categoryId": "category123",
        "stock": 10,
        "status": "active",
        "createdAt": "2023-06-15T10:00:00Z",
        "updatedAt": "2023-06-15T10:00:00Z",
        "slug": "product-name",
        "featured": true,
        "ratingAvg": 4.5,
        "ratingCount": 10
      }
    ],
    "total": 100,
    "hasMore": true
  }
}
```

#### Create Product

```
POST /api/admin/products
```

Creates a new product.

**Permissions Required:** `write:products`

**Request Body:**

```json
{
  "name": "Product Name",
  "description": "Product description",
  "price": 99.99,
  "compareAtPrice": 129.99,
  "images": ["https://example.com/image1.jpg"],
  "category": "category123",
  "stock": 10,
  "status": "active",
  "features": ["Feature 1", "Feature 2"],
  "specifications": {
    "color": "Red",
    "size": "Medium"
  },
  "slug": "product-name"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "product123",
    "name": "Product Name",
    "description": "Product description",
    "price": 99.99,
    "compareAtPrice": 129.99,
    "images": ["https://example.com/image1.jpg"],
    "category": "Category Name",
    "categoryId": "category123",
    "stock": 10,
    "status": "active",
    "createdAt": "2023-06-15T10:00:00Z",
    "updatedAt": "2023-06-15T10:00:00Z",
    "slug": "product-name",
    "featured": false,
    "ratingAvg": 0,
    "ratingCount": 0
  }
}
```

#### Update Product

```
PUT /api/admin/products
```

Updates an existing product.

**Permissions Required:** `write:products`

**Request Body:**

```json
{
  "id": "product123",
  "name": "Updated Product Name",
  "description": "Updated product description",
  "price": 89.99,
  "compareAtPrice": 119.99,
  "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
  "category": "category123",
  "stock": 15,
  "status": "active",
  "features": ["Feature 1", "Feature 2", "Feature 3"],
  "specifications": {
    "color": "Blue",
    "size": "Large"
  },
  "slug": "updated-product-name",
  "featured": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "product123",
    "name": "Updated Product Name",
    "description": "Updated product description",
    "price": 89.99,
    "compareAtPrice": 119.99,
    "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
    "category": "Category Name",
    "categoryId": "category123",
    "stock": 15,
    "status": "active",
    "createdAt": "2023-06-15T10:00:00Z",
    "updatedAt": "2023-06-16T11:00:00Z",
    "slug": "updated-product-name",
    "featured": true,
    "ratingAvg": 4.5,
    "ratingCount": 10
  }
}
```

#### Delete Product

```
DELETE /api/admin/products
```

Deletes a product.

**Permissions Required:** `delete:products`

**Request Body:**

```json
{
  "id": "product123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Product deleted successfully"
}
```

### Categories

#### Get Categories

```
GET /api/admin/categories
```

Retrieves a list of categories with optional filtering and pagination.

**Permissions Required:** `read:categories`

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Get a specific category by ID |
| hierarchy | boolean | Return categories in hierarchical structure |
| search | string | Search in category name and description |
| sortBy | string | Field to sort by (name, order) |
| sortOrder | string | Sort order (asc, desc) |
| limit | number | Number of results to return (default: 20) |
| startAfter | string | Cursor for pagination |

**Response:**

```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "category123",
        "name": "Category Name",
        "description": "Category description",
        "image": "https://example.com/category.jpg",
        "slug": "category-name",
        "isActive": true,
        "order": 1
      }
    ],
    "total": 10,
    "hasMore": false
  }
}
```

#### Create Category

```
POST /api/admin/categories
```

Creates a new category.

**Permissions Required:** `write:categories`

**Request Body:**

```json
{
  "name": "Category Name",
  "description": "Category description",
  "image": "https://example.com/category.jpg",
  "slug": "category-name",
  "isActive": true,
  "order": 1
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "category123",
    "name": "Category Name",
    "description": "Category description",
    "image": "https://example.com/category.jpg",
    "slug": "category-name",
    "isActive": true,
    "order": 1
  }
}
```

#### Update Category

```
PUT /api/admin/categories
```

Updates an existing category.

**Permissions Required:** `write:categories`

**Request Body:**

```json
{
  "id": "category123",
  "name": "Updated Category Name",
  "description": "Updated category description",
  "image": "https://example.com/updated-category.jpg",
  "slug": "updated-category-name",
  "isActive": true,
  "order": 2
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "category123",
    "name": "Updated Category Name",
    "description": "Updated category description",
    "image": "https://example.com/updated-category.jpg",
    "slug": "updated-category-name",
    "isActive": true,
    "order": 2
  }
}
```

#### Delete Category

```
DELETE /api/admin/categories
```

Deletes a category.

**Permissions Required:** `delete:categories`

**Request Body:**

```json
{
  "id": "category123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Category deleted successfully"
}
```

### Orders

#### Get Orders

```
GET /api/admin/orders
```

Retrieves a list of orders with optional filtering and pagination.

**Permissions Required:** `read:orders`

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Get a specific order by ID |
| status | string | Filter by order status |
| userId | string | Filter by user ID |
| startDate | string | Filter by start date (ISO format) |
| endDate | string | Filter by end date (ISO format) |
| minTotal | number | Filter by minimum total amount |
| maxTotal | number | Filter by maximum total amount |
| search | string | Search in order details |
| sortBy | string | Field to sort by (createdAt, total) |
| sortOrder | string | Sort order (asc, desc) |
| limit | number | Number of results to return (default: 20) |
| startAfter | string | Cursor for pagination |

**Response:**

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order123",
        "userId": "user123",
        "items": [
          {
            "productId": "product123",
            "name": "Product Name",
            "price": 99.99,
            "quantity": 1,
            "image": "https://example.com/image1.jpg",
            "subtotal": 99.99
          }
        ],
        "status": "pending",
        "shippingAddress": {
          "fullName": "John Doe",
          "streetAddress": "123 Main St",
          "city": "New York",
          "country": "USA",
          "postalCode": "10001",
          "phone": "************",
          "email": "<EMAIL>"
        },
        "billingAddress": {
          "fullName": "John Doe",
          "streetAddress": "123 Main St",
          "city": "New York",
          "country": "USA",
          "postalCode": "10001",
          "phone": "************",
          "email": "<EMAIL>"
        },
        "paymentMethod": "credit_card",
        "paymentStatus": "paid",
        "subtotal": 99.99,
        "shippingCost": 10.00,
        "tax": 8.00,
        "total": 117.99,
        "noteCount": 2,
        "createdAt": "2023-06-15T10:00:00Z",
        "updatedAt": "2023-06-15T10:00:00Z"
      }
    ],
    "total": 50,
    "hasMore": true
  }
}
```

#### Update Order Status

```
PUT /api/admin/orders
```

Updates an order's status.

**Permissions Required:** `write:orders`

**Request Body:**

```json
{
  "id": "order123",
  "status": "shipped",
  "trackingNumber": "TRK123456789"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "order123",
    "status": "shipped",
    "trackingNumber": "TRK123456789",
    "updatedAt": "2023-06-16T11:00:00Z"
  }
}
```

#### Add Order Note

```
POST /api/admin/orders
```

Adds a note to an order.

**Permissions Required:** `write:orders`

**Request Body:**

```json
{
  "orderId": "order123",
  "content": "Note content",
  "isInternal": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "note123"
  }
}
```

#### Delete Order Note

```
DELETE /api/admin/orders
```

Deletes a note from an order.

**Permissions Required:** `write:orders`

**Request Body:**

```json
{
  "orderId": "order123",
  "noteId": "note123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Note deleted successfully"
}
```

### Users

#### Get Users

```
GET /api/admin/users
```

Retrieves a list of users with optional filtering.

**Permissions Required:** `read:users`

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Get a specific user by ID |
| role | string | Filter by user role |
| status | string | Filter by user status (active, disabled) |
| search | string | Search in user details |

**Response:**

```json
{
  "success": true,
  "data": {
    "users": [
      {
        "uid": "user123",
        "email": "<EMAIL>",
        "displayName": "John Doe",
        "photoURL": "https://example.com/profile.jpg",
        "role": "customer",
        "isDisabled": false,
        "createdAt": "2023-06-15T10:00:00Z",
        "lastLoginAt": "2023-06-16T11:00:00Z"
      }
    ],
    "total": 100
  }
}
```

#### Update User Role

```
PUT /api/admin/users
```

Updates a user's role.

**Permissions Required:** `write:users`

**Request Body:**

```json
{
  "uid": "user123",
  "action": "updateRole",
  "role": "admin"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User role updated to admin"
}
```

#### Disable User

```
PUT /api/admin/users
```

Disables a user account.

**Permissions Required:** `write:users`

**Request Body:**

```json
{
  "uid": "user123",
  "action": "disable"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User disabled successfully"
}
```

#### Enable User

```
PUT /api/admin/users
```

Enables a disabled user account.

**Permissions Required:** `write:users`

**Request Body:**

```json
{
  "uid": "user123",
  "action": "enable"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User enabled successfully"
}
```

### File Upload

#### Upload Image

```
POST /api/admin/upload
```

Uploads an image to Firebase Storage.

**Permissions Required:** `write:products`

**Request Body:**

Multipart form data with:
- `file`: The image file to upload
- `path`: The storage path (e.g., 'products', 'categories')

**Response:**

```json
{
  "success": true,
  "data": {
    "url": "https://storage.googleapis.com/bucket/path/filename.jpg",
    "path": "products/filename.jpg"
  }
}
```

## Middleware

The API uses several middleware components:

1. **Authentication Middleware**: Verifies user authentication
2. **Permission Middleware**: Checks user permissions for specific actions
3. **Error Middleware**: Handles and formats error responses
4. **Rate Limit Middleware**: Prevents abuse by limiting request frequency

## Validation

All API endpoints use Zod schemas for request validation. Invalid requests will return a 400 status code with detailed validation errors.
