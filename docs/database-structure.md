# Belinda E-commerce Database Structure

This document provides a comprehensive specification of the Firestore database structure used in the Belinda E-commerce application. It is designed to standardize communication between frontend and backend teams.

## Overview

The application uses Firebase Firestore as its primary database. Firestore is a NoSQL document database that stores data in collections of documents.

## Performance and Cost Optimization

Firestore's pricing model charges per document read, write, and delete operations. To optimize for both performance and cost, especially for complex admin dashboard queries, this database structure implements several strategies:

### Denormalization Strategy

The database structure uses strategic denormalization to reduce the need for multiple queries:
- Product documents contain category names (not just IDs)
- Order items contain product details at time of purchase
- Reviews include user names to avoid additional user lookups

### Aggregation Documents

For admin dashboard analytics that would otherwise require expensive collection scans or complex queries, we use aggregation documents:

```json
// Example: Daily Stats Document in "stats" collection
{
  "id": "daily_2023_06_20",
  "date": "2023-06-20",
  "orders": {
    "count": 45,
    "total": 5678.90,
    "byStatus": {
      "pending": 5,
      "processing": 15,
      "shipped": 20,
      "delivered": 3,
      "cancelled": 2
    }
  },
  "products": {
    "sold": 67,
    "topSelling": [
      {"id": "product123", "name": "Elegant Dress", "count": 12},
      {"id": "product456", "name": "Casual Shirt", "count": 8}
    ]
  },
  "users": {
    "new": 15,
    "active": 120
  },
  "updatedAt": "2023-06-20T23:59:59Z"
}
```

These aggregation documents are updated via Cloud Functions whenever relevant data changes.

### Query Optimization

1. **Pagination**: All list queries use cursor-based pagination with the `startAfter` parameter to limit document reads.

2. **Compound Queries**: Where possible, queries use compound conditions that can be satisfied by a single index.

3. **Caching**: Frequently accessed data is cached client-side to reduce read operations.

4. **Batch Operations**: Updates to multiple documents use batch operations to reduce transaction costs.

## Collections

### Products

Stores information about products available in the store.

**Collection Name:** `products`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| name | string | Product name |
| description | string | Product description |
| price | number | Current price |
| compareAtPrice | number | Original/compare-at price (optional) |
| images | array of strings | Array of image URLs |
| category | string | Category name |
| categoryId | string | Reference to category document ID |
| variants | array of objects | Product variants (optional) |
| stock | number | Available quantity |
| status | string | Product status (active, inactive, draft, archived) |
| createdAt | timestamp | Creation date |
| updatedAt | timestamp | Last update date |
| slug | string | URL-friendly identifier |
| featured | boolean | Whether product is featured |
| ratingAvg | number | Average rating |
| ratingCount | number | Number of ratings |
| features | array of strings | Product features (optional) |
| dimensions | object | Product dimensions (optional) |
| weight | number | Product weight (optional) |
| sku | string | Stock keeping unit (optional) |
| barcode | string | Barcode (optional) |

**Variant Object Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier for the variant |
| name | string | Variant name |
| price | number | Variant price (optional) |
| compareAtPrice | number | Original/compare-at price (optional) |
| stock | number | Available quantity (optional) |
| images | array of strings | Variant-specific images (optional) |
| attributes | object | Key-value pairs of attributes (e.g., {"size": "large", "color": "red"}) |

**Example Document:**

```json
{
  "id": "product123",
  "name": "Elegant Dress",
  "description": "A beautiful elegant dress for special occasions",
  "price": 99.99,
  "compareAtPrice": 129.99,
  "images": [
    "https://storage.googleapis.com/belinda-ecommerce.appspot.com/products/dress1.jpg",
    "https://storage.googleapis.com/belinda-ecommerce.appspot.com/products/dress2.jpg"
  ],
  "category": "Dresses",
  "categoryId": "category456",
  "variants": [
    {
      "id": "variant789",
      "name": "Small Red",
      "attributes": {
        "size": "small",
        "color": "red"
      },
      "stock": 5
    },
    {
      "id": "variant790",
      "name": "Medium Blue",
      "attributes": {
        "size": "medium",
        "color": "blue"
      },
      "stock": 10
    }
  ],
  "stock": 15,
  "status": "active",
  "createdAt": "2023-06-15T10:00:00Z",
  "updatedAt": "2023-06-15T10:00:00Z",
  "slug": "elegant-dress",
  "featured": true,
  "ratingAvg": 4.5,
  "ratingCount": 10,
  "features": [
    "100% cotton",
    "Machine washable",
    "Imported"
  ],
  "dimensions": {
    "length": 40,
    "width": 30,
    "height": 5
  },
  "weight": 0.5,
  "sku": "ED-001",
  "barcode": "123456789012"
}
```

### Categories

Stores product categories.

**Collection Name:** `categories`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| name | string | Category name |
| description | string | Category description |
| image | string | Category image URL |
| slug | string | URL-friendly identifier |
| isActive | boolean | Whether category is active |
| order | number | Display order (for sorting) |

**Example Document:**

```json
{
  "id": "category456",
  "name": "Dresses",
  "description": "Elegant dresses for all occasions",
  "image": "https://storage.googleapis.com/belinda-ecommerce.appspot.com/categories/dresses.jpg",
  "slug": "dresses",
  "isActive": true,
  "order": 1
}
```

### Orders

Stores customer orders.

**Collection Name:** `orders`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| userId | string | Reference to user document ID |
| items | array of objects | Ordered items |
| status | string | Order status (pending, processing, shipped, delivered, cancelled) |
| statusHistory | array of objects | History of status changes (optional) |
| shippingAddress | object | Shipping address |
| billingAddress | object | Billing address |
| paymentMethod | string | Payment method |
| paymentStatus | string | Payment status (pending, paid, failed, refunded) |
| subtotal | number | Subtotal amount |
| shippingCost | number | Shipping cost |
| tax | number | Tax amount |
| discount | number | Discount amount |
| total | number | Total amount |
| notes | string | Order notes |
| createdAt | timestamp | Creation date |
| updatedAt | timestamp | Last update date |
| trackingNumber | string | Shipping tracking number (optional) |
| estimatedDelivery | timestamp | Estimated delivery date (optional) |

**Order Item Object Structure:**

| Field | Type | Description |
|-------|------|-------------|
| productId | string | Reference to product document ID |
| name | string | Product name |
| price | number | Product price at time of order |
| quantity | number | Quantity ordered |
| image | string | Product image URL |
| subtotal | number | Item subtotal (price * quantity) |
| variantId | string | Reference to variant ID (optional) |
| variantName | string | Variant name (optional) |

**Address Object Structure:**

| Field | Type | Description |
|-------|------|-------------|
| fullName | string | Customer's full name |
| streetAddress | string | Street address |
| city | string | City |
| country | string | Country |
| postalCode | string | Postal/ZIP code |
| phone | string | Phone number |
| email | string | Email address |

**Example Document:**

```json
{
  "id": "order789",
  "userId": "user123",
  "items": [
    {
      "productId": "product123",
      "name": "Elegant Dress",
      "price": 99.99,
      "quantity": 1,
      "image": "https://storage.googleapis.com/belinda-ecommerce.appspot.com/products/dress1.jpg",
      "subtotal": 99.99,
      "variantId": "variant789",
      "variantName": "Small Red"
    }
  ],
  "status": "processing",
  "statusHistory": [
    {
      "status": "pending",
      "timestamp": "2023-06-15T10:00:00Z"
    },
    {
      "status": "processing",
      "timestamp": "2023-06-15T11:00:00Z"
    }
  ],
  "shippingAddress": {
    "fullName": "Jane Doe",
    "streetAddress": "123 Main St",
    "city": "New York",
    "country": "USA",
    "postalCode": "10001",
    "phone": "************",
    "email": "<EMAIL>"
  },
  "billingAddress": {
    "fullName": "Jane Doe",
    "streetAddress": "123 Main St",
    "city": "New York",
    "country": "USA",
    "postalCode": "10001",
    "phone": "************",
    "email": "<EMAIL>"
  },
  "paymentMethod": "credit_card",
  "paymentStatus": "paid",
  "subtotal": 99.99,
  "shippingCost": 10.00,
  "tax": 8.00,
  "discount": 0.00,
  "total": 117.99,
  "notes": "Please gift wrap",
  "createdAt": "2023-06-15T10:00:00Z",
  "updatedAt": "2023-06-15T11:00:00Z",
  "trackingNumber": "TRK123456789",
  "estimatedDelivery": "2023-06-20T00:00:00Z"
}
```

#### Order Notes Subcollection

Each order document can have a subcollection of notes.

**Subcollection Name:** `notes`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| orderId | string | Reference to parent order ID |
| userId | string | Reference to user who created the note |
| userName | string | Name of user who created the note |
| content | string | Note content |
| isInternal | boolean | Whether note is internal (not visible to customer) |
| createdAt | timestamp | Creation date |

**Example Document:**

```json
{
  "id": "note456",
  "orderId": "order789",
  "userId": "admin123",
  "userName": "Admin User",
  "content": "Customer requested gift wrapping",
  "isInternal": true,
  "createdAt": "2023-06-15T11:30:00Z"
}
```

### Users

Stores user information.

**Collection Name:** `users`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| uid | string | Unique identifier (matches Firebase Auth UID) |
| email | string | User's email address |
| displayName | string | User's display name |
| photoURL | string | User's profile photo URL (optional) |
| role | string | User role (admin, editor, viewer, customer) |
| isDisabled | boolean | Whether user account is disabled |
| createdAt | timestamp | Account creation date |
| lastLoginAt | timestamp | Last login date (optional) |
| metadata | object | Additional user metadata (optional) |

**Example Document:**

```json
{
  "uid": "user123",
  "email": "<EMAIL>",
  "displayName": "Jane Doe",
  "photoURL": "https://storage.googleapis.com/belinda-ecommerce.appspot.com/users/jane.jpg",
  "role": "customer",
  "isDisabled": false,
  "createdAt": "2023-06-01T10:00:00Z",
  "lastLoginAt": "2023-06-15T10:00:00Z",
  "metadata": {
    "phoneVerified": true,
    "newsletter": true
  }
}
```

### Reviews

Stores product reviews.

**Collection Name:** `reviews`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| productId | string | Reference to product document ID |
| userId | string | Reference to user document ID |
| userName | string | Name of user who wrote the review |
| rating | number | Rating (1-5) |
| title | string | Review title |
| comment | string | Review comment |
| createdAt | timestamp | Creation date |
| updatedAt | timestamp | Last update date (optional) |

**Example Document:**

```json
{
  "id": "review123",
  "productId": "product123",
  "userId": "user123",
  "userName": "Jane Doe",
  "rating": 5,
  "title": "Excellent product!",
  "comment": "I love this dress. The quality is amazing and it fits perfectly.",
  "createdAt": "2023-06-16T10:00:00Z",
  "updatedAt": "2023-06-16T10:00:00Z"
}
```

### Wishlist

Stores user wishlists.

**Collection Name:** `wishlists`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| userId | string | Reference to user document ID |
| productId | string | Reference to product document ID |
| addedAt | timestamp | Date added to wishlist |

**Example Document:**

```json
{
  "id": "wishlist123",
  "userId": "user123",
  "productId": "product123",
  "addedAt": "2023-06-15T10:00:00Z"
}
```

### Recently Viewed

Stores recently viewed products for users.

**Collection Name:** `recentlyViewed`

**Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (document ID) |
| userId | string | Reference to user document ID |
| productId | string | Reference to product document ID |
| viewedAt | timestamp | Date product was viewed |

**Example Document:**

```json
{
  "id": "rv123",
  "userId": "user123",
  "productId": "product123",
  "viewedAt": "2023-06-16T10:00:00Z"
}
```

### Settings

Stores application settings.

**Collection Name:** `settings`

**Document IDs:**
- `general`: General store settings
- `payment`: Payment settings
- `email`: Email settings

**General Settings Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| storeName | string | Store name |
| storeEmail | string | Store email |
| storePhone | string | Store phone |
| storeAddress | string | Store address |
| storeCity | string | Store city |
| storeState | string | Store state/province |
| storeZip | string | Store ZIP/postal code |
| storeCountry | string | Store country |
| updatedAt | timestamp | Last update date |

**Payment Settings Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| enabledMethods | array of strings | Enabled payment methods |
| stripePublicKey | string | Stripe public key |
| stripeSecretKey | string | Stripe secret key (encrypted) |
| paypalClientId | string | PayPal client ID |
| paypalSecretKey | string | PayPal secret key (encrypted) |
| updatedAt | timestamp | Last update date |

**Email Settings Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| fromEmail | string | From email address |
| fromName | string | From name |
| replyToEmail | string | Reply-to email address |
| orderConfirmationTemplate | string | Order confirmation email template |
| shippingConfirmationTemplate | string | Shipping confirmation email template |
| welcomeTemplate | string | Welcome email template |
| passwordResetTemplate | string | Password reset email template |
| updatedAt | timestamp | Last update date |

## Security Rules

The database is secured with Firestore security rules that enforce the following access controls:

### Helper Functions

```
function isAuthenticated() {
  return request.auth != null;
}

function isAdmin() {
  return isAuthenticated() &&
    (request.auth.token.admin == true || request.auth.token.role == 'admin');
}

function isEditor() {
  return isAuthenticated() &&
    (isAdmin() || request.auth.token.role == 'editor');
}

function isOwner(userId) {
  return isAuthenticated() && request.auth.uid == userId;
}
```

### Collection Rules

#### Products

```
match /products/{productId} {
  allow read: if true; // Public read access
  allow create: if isEditor() && isValidProduct();
  allow update: if isEditor() && isValidProduct();
  allow delete: if isAdmin();
}
```

#### Categories

```
match /categories/{categoryId} {
  allow read: if true; // Public read access
  allow create: if isEditor() && isValidCategory();
  allow update: if isEditor() && isValidCategory();
  allow delete: if isAdmin();
}
```

#### Orders

```
match /orders/{orderId} {
  allow read: if isAuthenticated() &&
    (isAdmin() || resource.data.userId == request.auth.uid);
  allow create: if isAuthenticated();
  allow update: if isAdmin() ||
    (isOwner(resource.data.userId) &&
     resource.data.status == 'pending');
  allow delete: if isAdmin();
}
```

#### Users

```
match /users/{userId} {
  allow read: if isAuthenticated() &&
    (isAdmin() || isOwner(userId));
  allow create: if isAdmin();
  allow update: if isAdmin() || isOwner(userId);
  allow delete: if isAdmin();
}
```

#### Settings

```
match /settings/{settingId} {
  allow read: if true; // Public read access for settings
  allow write: if isAdmin();
}
```

## Indexes

The following composite indexes are required for efficient querying:

### Products Collection

- Fields: `status` (ascending), `createdAt` (descending)
- Fields: `categoryId` (ascending), `createdAt` (descending)
- Fields: `featured` (ascending), `createdAt` (descending)
- Fields: `price` (ascending), `createdAt` (descending)
- Fields: `price` (descending), `createdAt` (descending)
- Fields: `categoryId` (ascending), `price` (ascending)
- Fields: `categoryId` (ascending), `price` (descending)
- Fields: `status` (ascending), `categoryId` (ascending), `price` (ascending)

### Orders Collection

- Fields: `userId` (ascending), `createdAt` (descending)
- Fields: `status` (ascending), `createdAt` (descending)
- Fields: `createdAt` (ascending), `total` (descending)
- Fields: `createdAt` (descending), `total` (descending)
- Fields: `status` (ascending), `total` (descending)
- Fields: `paymentStatus` (ascending), `createdAt` (descending)

### Reviews Collection

- Fields: `productId` (ascending), `createdAt` (descending)
- Fields: `userId` (ascending), `createdAt` (descending)
- Fields: `productId` (ascending), `rating` (descending)

## Relationships

The database uses document references to establish relationships between collections:

1. **Products to Categories**: Products reference categories via `categoryId`
2. **Orders to Users**: Orders reference users via `userId`
3. **Orders to Products**: Order items reference products via `productId`
4. **Reviews to Products**: Reviews reference products via `productId`
5. **Reviews to Users**: Reviews reference users via `userId`
6. **Wishlists to Products**: Wishlists reference products via `productId`
7. **Wishlists to Users**: Wishlists reference users via `userId`

## Data Validation

Data validation is performed at multiple levels:

1. **Client-side**: Using Zod schemas in the frontend
2. **API-level**: Using Zod schemas in API endpoints
3. **Database-level**: Using Firestore security rules

## Stats Collection (for Admin Dashboard)

A dedicated collection for pre-aggregated statistics to power the admin dashboard without expensive queries.

**Collection Name:** `stats`

**Document Types:**
- Daily stats (`daily_YYYY_MM_DD`)
- Monthly stats (`monthly_YYYY_MM`)
- Product stats (`product_PRODUCT_ID`)
- Category stats (`category_CATEGORY_ID`)

**Daily Stats Document Structure:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier (e.g., "daily_2023_06_20") |
| date | string | Date in ISO format |
| orders | object | Order statistics for the day |
| products | object | Product statistics for the day |
| users | object | User statistics for the day |
| revenue | object | Revenue statistics for the day |
| updatedAt | timestamp | Last update time |

**Example Daily Stats Document:**

```json
{
  "id": "daily_2023_06_20",
  "date": "2023-06-20",
  "orders": {
    "count": 45,
    "total": 5678.90,
    "byStatus": {
      "pending": 5,
      "processing": 15,
      "shipped": 20,
      "delivered": 3,
      "cancelled": 2
    },
    "byPaymentMethod": {
      "credit_card": 30,
      "paynow": 10,
      "bank_transfer": 5
    }
  },
  "products": {
    "sold": 67,
    "topSelling": [
      {"id": "product123", "name": "Elegant Dress", "count": 12, "revenue": 1199.88},
      {"id": "product456", "name": "Casual Shirt", "count": 8, "revenue": 399.92}
    ],
    "lowStock": [
      {"id": "product789", "name": "Designer Bag", "stock": 2}
    ]
  },
  "users": {
    "new": 15,
    "active": 120
  },
  "revenue": {
    "total": 5678.90,
    "subtotal": 4999.00,
    "shipping": 450.00,
    "tax": 399.90,
    "discount": 170.00
  },
  "updatedAt": "2023-06-20T23:59:59Z"
}
```

## SQL vs. Firestore Considerations

While Firestore offers flexibility and real-time capabilities, SQL databases excel at complex analytical queries. Consider these trade-offs:

### When Firestore Works Well

1. **Real-time dashboards**: When you need live updates without polling
2. **Simple filtering and sorting**: Basic admin views with 1-2 filter conditions
3. **Document-centric operations**: When retrieving complete entities

### When SQL Might Be Better

1. **Complex aggregations**: If you need extensive GROUP BY, COUNT, SUM operations
2. **Joins across multiple tables**: For reports that combine many entity types
3. **Ad-hoc queries**: For unpredictable, complex analytical needs

### Hybrid Approach

For the most demanding analytical needs, consider:
1. Keep Firestore as your operational database
2. Use Cloud Functions to export data to BigQuery on a schedule
3. Run complex analytics in BigQuery
4. Cache results in Firestore for dashboard display

## Data Migration and Versioning

When making changes to the database schema:

1. Always maintain backward compatibility when possible
2. Document schema changes in a changelog
3. Implement migration scripts for major schema changes
4. Consider adding a `schemaVersion` field to documents for tracking schema versions
5. Use Cloud Functions to update denormalized data when source data changes
