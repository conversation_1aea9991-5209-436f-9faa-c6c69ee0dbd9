Okay, this is excellent! Having both endpoints allows for a cohesive strategy. The core idea will be to make your **server the absolute source of truth for all order details, especially pricing**, and to link the payment creation process tightly with the webhook fulfillment process.

Here's a detailed set of advice for transforming both endpoints:

**Overarching Principles for the Transformation:**

1.  **Server-Side Authority:** Your server *must* determine or verify all critical data, especially product prices, shipping costs, and thus the final total amount. Never trust monetary values sent directly from the client for calculation.
2.  **The "Pending Order" or "Order Stub" Strategy:** This is highly recommended.
    *   In your `create-payment-intent` endpoint, *after* validating and calculating everything on the server, you'll create an initial "pending" order document in Firestore.
    *   This pending order will contain all authoritative details.
    *   You'll then pass the unique ID of this pending order in the Stripe PaymentIntent's metadata.
    *   Your webhook will use this ID to retrieve the authoritative order details from Firestore, making it robust and simple.
3.  **Idempotency:** Critical for the webhook to prevent duplicate order processing.
4.  **Clear Data Flow:** Ensure data passed in metadata is minimal and serves as a key to your authoritative data.
5.  **Security & Validation:** Continue using Zod, but layer server-side validation on top of it.

---

**Detailed Advice for Transforming `create-payment-intent/route.ts`**

Your current `create-payment-intent` endpoint does a good job with Zod validation for structure and user auth. The main area to overhaul is price calculation and metadata.

**Steps:**

1.  **Fetch Authoritative Product Prices (CRITICAL):**
    *   **Action:** After `validationResult.success`, iterate through `validationResult.data.items`. For each item:
        *   Use `item.productId` (and `item.variantId` if present) to fetch the product's *current price* from your Firestore 'products' collection (or wherever you store product data).
        *   **Do not use `item.price` from the client request for calculation.**
    *   **Why:** This prevents users from manipulating prices in the client-side request.
    *   **Example Snippet Idea:**
        ```typescript
        // Inside POST function, after Zod validation
        const validatedData = validationResult.data;
        const serverVerifiedItems = [];
        let calculatedSubtotal = 0;

        for (const clientItem of validatedData.items) {
          // const productFromDB = await getProductById(clientItem.productId, clientItem.variantId);
          // if (!productFromDB || productFromDB.price === undefined) {
          //   return NextResponse.json({ error: `Product ${clientItem.productId} not found or price missing` }, { status: 400 });
          // }
          // const actualPrice = productFromDB.price; // This is your authoritative price

          // For demonstration, let's assume you have a function to get product details
          const actualPrice = await getPriceFromDB(clientItem.productId, clientItem.variantId); // Implement this
          if (actualPrice === null) {
              return NextResponse.json({ error: `Invalid product or variant: ${clientItem.productId}`}, { status: 400 });
          }

          serverVerifiedItems.push({
            ...clientItem, // Keep other details like name, image if needed for pending order
            price: actualPrice, // Use the SERVER-FETCHED PRICE
            subtotal: actualPrice * clientItem.quantity,
          });
          calculatedSubtotal += actualPrice * clientItem.quantity;
        }
        ```
    *   You'll need to implement `getPriceFromDB(productId, variantId)` that fetches from Firestore.

2.  **Fetch Authoritative Shipping Cost (CRITICAL):**
    *   **Action:** Use `validatedData.shippingMethod.id` to fetch the shipping method's *current price* from your Firestore 'shippingMethods' collection.
        *   **Do not use `validatedData.shippingMethod.price` from the client request for calculation.**
    *   **Why:** Same as product prices – prevents manipulation.
    *   **Example Snippet Idea:**
        ```typescript
        // const shippingOptionFromDB = await getShippingMethodById(validatedData.shippingMethod.id);
        // if (!shippingOptionFromDB || shippingOptionFromDB.price === undefined) {
        //   return NextResponse.json({ error: `Shipping method ${validatedData.shippingMethod.id} not found or price missing` }, { status: 400 });
        // }
        // const actualShippingCost = shippingOptionFromDB.price;

        // For demonstration:
        const actualShippingCost = await getShippingCostFromDB(validatedData.shippingMethod.id); // Implement this
        if (actualShippingCost === null) {
            return NextResponse.json({ error: `Invalid shipping method: ${validatedData.shippingMethod.id}`}, { status: 400 });
        }
        ```

3.  **Calculate Final Total Server-Side:**
    *   **Action:** `const total = calculatedSubtotal + actualShippingCost;`
    *   **Why:** Ensures the Stripe PaymentIntent is created with the correct, server-verified amount.

4.  **Create a "Pending Order" Document in Firestore (The "Order Stub" Strategy):**
    *   **Action:** Before creating the Stripe PaymentIntent, create a new document in your Firestore `orders` collection (or a dedicated `pendingOrders` collection).
    *   This document should include:
        *   `userId`
        *   `email`
        *   `items`: Store `serverVerifiedItems` (which now include server-fetched prices and calculated subtotals).
        *   `shippingAddress`: From `validatedData.shippingAddress`.
        *   `billingAddress`: From `validatedData.billingAddress` (or `shippingAddress` if `sameAsShipping`).
        *   `shippingMethodDetails`: Store the details of the chosen shipping method, including its *server-verified price*.
        *   `orderSubtotal`: The `calculatedSubtotal`.
        *   `shippingCostTotal`: The `actualShippingCost`.
        *   `grandTotal`: The `total`.
        *   `status`: `'pending_payment'` (or similar).
        *   `createdAt`: `FieldValue.serverTimestamp()`.
        *   `currency`: `paymentSettings.currencyCode`.
    *   **Why:** This document becomes the single source of truth for the order if the payment is successful. The webhook will refer to it.
    *   **Example Snippet Idea:**
        ```typescript
        // const pendingOrderData = {
        //   userId,
        //   email: validatedData.email,
        //   items: serverVerifiedItems,
        //   shippingAddress: validatedData.shippingAddress,
        //   billingAddress: validatedData.sameAsShipping ? validatedData.shippingAddress : validatedData.billingAddress,
        //   shippingMethodDetails: { ...validatedData.shippingMethod, price: actualShippingCost },
        //   orderSubtotal: calculatedSubtotal,
        //   shippingCostTotal: actualShippingCost,
        //   grandTotal: total,
        //   status: 'pending_payment',
        //   currency: paymentSettings.currencyCode.toLowerCase(),
        //   createdAt: admin.firestore.FieldValue.serverTimestamp(), // If using Firebase Admin SDK
        //   // updatedAd: admin.firestore.FieldValue.serverTimestamp(),
        // };
        // const pendingOrderRef = await db.collection('orders').add(pendingOrderData); // Or your createOrder service
        // const internalOrderId = pendingOrderRef.id;

        // Using your existing createOrder, but it needs to be adapted to handle this "pending" state
        // and return the ID. For now, let's assume it stores this data and returns an ID.
        // This might involve creating a NEW service function like `createPendingOrder`.
        const internalOrderId = await createPendingOrderInDB({ /* ... all the data above ... */ });
        if (!internalOrderId) {
            return NextResponse.json({ error: 'Failed to create pending order record' }, { status: 500 });
        }
        ```
        You'll need a `createPendingOrderInDB` function that saves this data and returns the new document's ID.

5.  **Modify PaymentIntent Metadata:**
    *   **Action:** Change the metadata to be minimal and focused on linking to your pending order.
    *   **Why:** Keeps metadata clean and ensures the webhook uses your database as the source of truth.
    *   **Example:**
        ```typescript
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(total * 100), // Use SERVER-CALCULATED total
          currency: paymentSettings.currencyCode.toLowerCase(),
          metadata: {
            internalOrderId: internalOrderId, // CRITICAL: Link to your Firestore pending order
            userId, // Still useful
            // email: validatedData.email, // Optional here, as Stripe has receipt_email
          },
          receipt_email: validatedData.email, // For Stripe to send its receipt
          shipping: { // Provide shipping details to Stripe
            name: `${validatedData.shippingAddress.firstName} ${validatedData.shippingAddress.lastName}`,
            address: {
              line1: validatedData.shippingAddress.streetAddress,
              line2: validatedData.shippingAddress.apartment,
              city: validatedData.shippingAddress.city,
              state: validatedData.shippingAddress.state,
              postal_code: validatedData.shippingAddress.zipCode,
              country: validatedData.shippingAddress.country,
            },
            phone: validatedData.shippingAddress.phone,
          },
          // You can also add payment_method_options for things like requesting 3D Secure
        });
        ```

6.  **Return `internalOrderId` (Optional but Recommended):**
    *   **Action:** Along with `clientSecret` and `paymentIntentId`, consider returning `internalOrderId`.
    *   **Why:** The client might use this if it needs to refer to the pending order before payment completion (e.g., for an order summary page that re-fetches if the user navigates away and comes back).

---

**Detailed Advice for Transforming the Webhook Handler (`/api/payment/webhook/route.ts`)**

This endpoint will become simpler and more robust with the "pending order" strategy.

**Steps:**

1.  **Idempotency Check (CRITICAL):**
    *   **Action:** In `handlePaymentIntentSucceeded`:
        1.  At the very beginning, use `paymentIntent.id` (the Stripe PaymentIntent ID).
        2.  Query your `orders` collection in Firestore to see if an order already has this `paymentIntentId` field set *and* its status is already 'paid' (or your success status).
        3.  If yes, log that it's a duplicate event and `return;` (or `return Promise.resolve();`) immediately to send a 200 OK to Stripe.
    *   **Why:** Prevents processing the same successful payment multiple times if Stripe resends the webhook.
    *   **Example Snippet Idea (within `handlePaymentIntentSucceeded`):**
        ```typescript
        // async function getOrderByStripePaymentIntentId(stripePI_ID: string) { /* ... query Firestore ... */ }
        // const existingOrder = await getOrderByStripePaymentIntentId(paymentIntent.id);
        // if (existingOrder && existingOrder.status === 'paid') { // Or your successful status
        //   console.log(`Webhook: Order for PaymentIntent ${paymentIntent.id} already processed.`);
        //   return;
        // }
        ```

2.  **Retrieve the `internalOrderId` from Metadata:**
    *   **Action:** In `handlePaymentIntentSucceeded`, get `internalOrderId` from `paymentIntent.metadata`.
    *   **Why:** This is your key to the authoritative order data.
    *   **Example:**
        ```typescript
        const internalOrderId = paymentIntent.metadata.internalOrderId;
        if (!internalOrderId) {
          console.error(`Webhook Error: Missing internalOrderId in PaymentIntent metadata for ${paymentIntent.id}`);
          // This is a critical error. Decide if it's a 400 (bad request data from Stripe's perspective)
          // or if you simply can't process it (and thus return 200 to stop retries for this malformed event).
          // A 400 might be appropriate if metadata is expected.
          throw new Error('Missing internalOrderId in metadata'); // This will result in a 500 from main handler
        }
        ```

3.  **Fetch Authoritative Order Details from Firestore:**
    *   **Action:** Use `internalOrderId` to retrieve the *full pending order document* you created in Step 4 of the `create-payment-intent` endpoint.
    *   **Why:** This is your source of truth. You are no longer parsing `itemsJson` or `shippingJson` from metadata.
    *   **Example Snippet Idea (within `handlePaymentIntentSucceeded`):**
        ```typescript
        // const orderRef = db.collection('orders').doc(internalOrderId);
        // const orderSnapshot = await orderRef.get();
        // if (!orderSnapshot.exists) {
        //   console.error(`Webhook Error: Order with internalOrderId ${internalOrderId} not found for PaymentIntent ${paymentIntent.id}`);
        //   throw new Error('Pending order not found'); // Will result in 500, Stripe retries
        // }
        // const pendingOrderData = orderSnapshot.data();
        ```

4.  **Update Order Status (Instead of `createOrder` from scratch):**
    *   **Action:** You likely won't call `createOrder` in the same way. Instead, you'll *update* the existing pending order document fetched in the previous step.
        *   Set its `status` to `'paid'` (or `'processing'`, `'completed'`, etc.).
        *   Store the `paymentIntent.id` from Stripe on this order document (for idempotency and reference).
        *   Add any other payment-specific details from `paymentIntent` (e.g., `paymentIntent.charges.data.receipt_url`).
    *   **Why:** You're fulfilling the order that was already outlined.
    *   **Example Snippet Idea (within `handlePaymentIntentSucceeded`):**
        ```typescript
        // await orderRef.update({
        //   status: 'paid', // Or your successful status
        //   stripePaymentIntentId: paymentIntent.id,
        //   stripeChargeId: paymentIntent.latest_charge, // If you need it
        //   receiptUrl: paymentIntent.charges?.data[0]?.receipt_url, // Check if charges are expanded
        //   paidAt: admin.firestore.FieldValue.serverTimestamp(),
        //   // ... any other updates
        // });
        // console.log(`Webhook: Order ${internalOrderId} successfully updated to paid for PaymentIntent ${paymentIntent.id}`);
        ```
        Your `createOrder` function from `order-service.ts` might need to be refactored into `updateOrderAfterPayment(internalOrderId, paymentIntentDetails)` or similar.

5.  **Error Handling & Retry Logic:**
    *   **Action:** If any step within `handlePaymentIntentSucceeded` (like fetching from DB or updating DB) fails due to a potentially temporary issue (e.g., database connectivity), `throw error;`. The main `POST` function's `catch` block should then return a `5xx` status to Stripe, prompting a retry.
    *   If it's a non-retryable error (e.g., `internalOrderId` missing after you've already handled idempotency, or data integrity issue you can't fix), log it and consider returning a 200 to stop Stripe from retrying a bad event.
    *   **Why:** Ensures robust processing and allows Stripe to retry when appropriate.

6.  **`handlePaymentIntentFailed`:**
    *   **Action:**
        *   Retrieve `internalOrderId` from metadata if present.
        *   If found, update the corresponding order in Firestore to `status: 'payment_failed'`.
        *   Log details, especially `paymentIntent.last_payment_error`.
    *   **Why:** Keeps your order statuses accurate.

---

**General Recommendations for Both Endpoints:**

*   **API Version (`apiVersion`):** Re-evaluate if `"2025-04-30.basil"` is the correct/intended Stripe API version. Usually, you'd use a date like `"2024-04-10"` or the latest stable version documented by Stripe. Update your Stripe SDK and types (`stripe`, `@types/stripe`) if necessary to use a standard version.
*   **Firebase Service Functions:**
    *   You'll need functions like:
        *   `getProductById(productId, variantId)`: Returns product details including price.
        *   `getShippingMethodById(shippingId)`: Returns shipping details including price.
        *   `createPendingOrderInDB(orderData)`: Saves the initial pending order and returns its ID.
        *   `updateOrderAfterPayment(internalOrderId, paymentDetails)`: Updates the order status, adds Stripe IDs.
        *   `getOrderByStripePaymentIntentId(stripePiId)`: For the idempotency check.
        *   `getOrderByInternalOrderId(internalOrderId)`: To fetch the pending order.
*   **Logging:** Enhance logging with more context (e.g., `internalOrderId`, `paymentIntentId`) in both endpoints.
*   **Testing:** Thoroughly test the entire flow with the Stripe CLI and test card numbers:
    *   Successful payment.
    *   Duplicate successful payment events.
    *   Payment failure.
    *   Cases where product prices might have changed between cart addition and checkout.
    *   Invalid product IDs or shipping IDs sent from client.

By implementing these changes, your payment system will be significantly more secure, robust, and easier to maintain. The "pending order" strategy is a cornerstone of reliable payment processing.