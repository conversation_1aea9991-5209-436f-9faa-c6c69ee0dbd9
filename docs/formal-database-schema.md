# Belinda E-commerce Formal Database Schema

This document provides a formal specification of the Firestore database schema used in the Belinda E-commerce application. It serves as the definitive reference for both backend and frontend teams.

## Schema Conventions

### Collection and Document Naming

- Collection names use plural nouns: `products`, `categories`, `orders`
- Document IDs use auto-generated Firebase IDs unless otherwise specified
- Subcollection names follow the same conventions as top-level collections

### Data Types

| Type | Description | Example |
|------|-------------|---------|
| string | Text data | `"Product name"` |
| number | Numeric data (float or integer) | `99.99` |
| boolean | True/false value | `true` |
| timestamp | Firestore timestamp | `Timestamp(seconds, nanoseconds)` |
| map | Nested object | `{ "key": "value" }` |
| array | Ordered list | `["item1", "item2"]` |
| reference | Reference to another document | `db.doc("products/productId")` |
| geopoint | Geographic coordinates | `GeoPoint(latitude, longitude)` |
| null | Absence of a value | `null` |

### Required vs Optional Fields

- **Required fields** must always be present in a document
- **Optional fields** may be omitted
- In the schema tables below:
  - Required fields are marked with ✓
  - Optional fields are marked with ○

### Timestamps

All timestamps are stored as Firestore Timestamp objects and follow these conventions:
- `createdAt`: When the document was created (never updated)
- `updatedAt`: When the document was last modified
- Date-specific fields use descriptive names (e.g., `orderDate`, `publishedAt`)

## Collections Schema

### Products Collection

**Collection Path:** `/products`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| name | string | ✓ | ✓ | Product name |
| description | string | ✓ | ○ | Product description |
| price | number | ✓ | ✓ | Current price |
| compareAtPrice | number | ○ | ○ | Original/compare-at price |
| images | array of strings | ✓ | ○ | Array of image URLs |
| category | string | ✓ | ✓ | Category name (denormalized) |
| categoryId | string | ✓ | ✓ | Reference to category document ID |
| variants | array of maps | ○ | ○ | Product variants (see structure below) |
| stock | number | ✓ | ✓ | Available quantity |
| status | string | ✓ | ✓ | Product status: "active", "inactive", "draft", or "archived" |
| createdAt | timestamp | ✓ | ✓ | Creation date |
| updatedAt | timestamp | ✓ | ✓ | Last update date |
| slug | string | ✓ | ✓ | URL-friendly identifier |
| featured | boolean | ✓ | ✓ | Whether product is featured |
| ratingAvg | number | ✓ | ✓ | Average rating (0-5) |
| ratingCount | number | ✓ | ✓ | Number of ratings |
| features | array of strings | ○ | ○ | Product features |
| dimensions | map | ○ | ○ | Product dimensions (length, width, height) |
| weight | number | ○ | ○ | Product weight |
| sku | string | ○ | ✓ | Stock keeping unit |
| barcode | string | ○ | ✓ | Barcode |

**Variant Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| id | string | ✓ | Unique identifier for the variant |
| name | string | ✓ | Variant name |
| price | number | ○ | Variant price (if different from product) |
| compareAtPrice | number | ○ | Original/compare-at price |
| stock | number | ○ | Available quantity (if tracked separately) |
| images | array of strings | ○ | Variant-specific images |
| attributes | map | ✓ | Key-value pairs of attributes (e.g., {"size": "large", "color": "red"}) |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| status, createdAt | ASC, DESC |
| categoryId, createdAt | ASC, DESC |
| featured, createdAt | ASC, DESC |
| price, createdAt | ASC, DESC |
| price, createdAt | DESC, DESC |
| categoryId, price | ASC, ASC |
| categoryId, price | ASC, DESC |
| status, categoryId, price | ASC, ASC, ASC |

### Categories Collection

**Collection Path:** `/categories`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| name | string | ✓ | ✓ | Category name |
| description | string | ✓ | ○ | Category description |
| image | string | ○ | ○ | Category image URL |
| slug | string | ✓ | ✓ | URL-friendly identifier |
| isActive | boolean | ✓ | ✓ | Whether category is active |
| order | number | ✓ | ✓ | Display order (for sorting) |
| parentId | string | ○ | ✓ | Parent category ID (for hierarchical categories) |
| createdAt | timestamp | ✓ | ✓ | Creation date |
| updatedAt | timestamp | ✓ | ✓ | Last update date |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| isActive, order | ASC, ASC |
| parentId, order | ASC, ASC |

### Orders Collection

**Collection Path:** `/orders`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| userId | string | ✓ | ✓ | Reference to user document ID |
| items | array of maps | ✓ | ○ | Ordered items (see structure below) |
| status | string | ✓ | ✓ | Order status: "pending", "processing", "shipped", "delivered", or "cancelled" |
| statusHistory | array of maps | ○ | ○ | History of status changes |
| shippingAddress | map | ✓ | ○ | Shipping address (see structure below) |
| billingAddress | map | ✓ | ○ | Billing address (see structure below) |
| paymentMethod | string | ✓ | ✓ | Payment method: "credit_card", "paynow", "bank_transfer", or "cash_on_delivery" |
| paymentStatus | string | ✓ | ✓ | Payment status: "pending", "paid", "failed", or "refunded" |
| subtotal | number | ✓ | ○ | Subtotal amount |
| shippingCost | number | ✓ | ○ | Shipping cost |
| tax | number | ○ | ○ | Tax amount |
| discount | number | ○ | ○ | Discount amount |
| total | number | ✓ | ✓ | Total amount |
| notes | string | ○ | ○ | Order notes |
| noteCount | number | ○ | ○ | Count of notes in subcollection |
| createdAt | timestamp | ✓ | ✓ | Creation date |
| updatedAt | timestamp | ✓ | ✓ | Last update date |
| trackingNumber | string | ○ | ✓ | Shipping tracking number |
| estimatedDelivery | timestamp | ○ | ○ | Estimated delivery date |

**Order Item Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| productId | string | ✓ | Reference to product document ID |
| name | string | ✓ | Product name at time of order |
| price | number | ✓ | Product price at time of order |
| quantity | number | ✓ | Quantity ordered |
| image | string | ✓ | Product image URL |
| subtotal | number | ✓ | Item subtotal (price * quantity) |
| variantId | string | ○ | Reference to variant ID |
| variantName | string | ○ | Variant name |
| slug | string | ○ | Product slug at time of order |

**Address Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| fullName | string | ✓ | Customer's full name |
| streetAddress | string | ✓ | Street address |
| city | string | ✓ | City |
| country | string | ✓ | Country |
| postalCode | string | ✓ | Postal/ZIP code |
| phone | string | ✓ | Phone number |
| email | string | ○ | Email address |

**Status History Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| status | string | ✓ | Order status |
| timestamp | timestamp | ✓ | When status was changed |
| userId | string | ○ | User who changed the status |
| note | string | ○ | Note about the status change |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| userId, createdAt | ASC, DESC |
| status, createdAt | ASC, DESC |
| createdAt, total | ASC, DESC |
| createdAt, total | DESC, DESC |
| status, total | ASC, DESC |
| paymentStatus, createdAt | ASC, DESC |

#### Order Notes Subcollection

**Collection Path:** `/orders/{orderId}/notes`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| orderId | string | ✓ | ✓ | Reference to parent order ID |
| userId | string | ✓ | ✓ | Reference to user who created the note |
| userName | string | ✓ | ○ | Name of user who created the note |
| content | string | ✓ | ○ | Note content |
| isInternal | boolean | ✓ | ✓ | Whether note is internal (not visible to customer) |
| createdAt | timestamp | ✓ | ✓ | Creation date |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| isInternal, createdAt | ASC, DESC |

### Users Collection

**Collection Path:** `/users`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| uid | string | ✓ | ✓ | Document ID (matches Firebase Auth UID) |
| email | string | ✓ | ✓ | User's email address |
| displayName | string | ✓ | ✓ | User's display name |
| photoURL | string | ○ | ○ | User's profile photo URL |
| role | string | ✓ | ✓ | User role: "admin", "editor", "viewer", or "customer" |
| isDisabled | boolean | ✓ | ✓ | Whether user account is disabled |
| createdAt | timestamp | ✓ | ✓ | Account creation date |
| lastLoginAt | timestamp | ○ | ✓ | Last login date |
| metadata | map | ○ | ○ | Additional user metadata |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| role, createdAt | ASC, DESC |
| isDisabled, createdAt | ASC, DESC |

### Reviews Collection

**Collection Path:** `/reviews`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| productId | string | ✓ | ✓ | Reference to product document ID |
| userId | string | ✓ | ✓ | Reference to user document ID |
| userName | string | ✓ | ○ | Name of user who wrote the review |
| rating | number | ✓ | ✓ | Rating (1-5) |
| title | string | ✓ | ○ | Review title |
| comment | string | ✓ | ○ | Review comment |
| createdAt | timestamp | ✓ | ✓ | Creation date |
| updatedAt | timestamp | ○ | ✓ | Last update date |
| isVerified | boolean | ○ | ✓ | Whether reviewer is a verified purchaser |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| productId, createdAt | ASC, DESC |
| userId, createdAt | ASC, DESC |
| productId, rating | ASC, DESC |
| isVerified, createdAt | ASC, DESC |

### Wishlist Collection

**Collection Path:** `/wishlists`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| userId | string | ✓ | ✓ | Reference to user document ID |
| productId | string | ✓ | ✓ | Reference to product document ID |
| addedAt | timestamp | ✓ | ✓ | Date added to wishlist |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| userId, addedAt | ASC, DESC |

### Recently Viewed Collection

**Collection Path:** `/recentlyViewed`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (auto-generated) |
| userId | string | ✓ | ✓ | Reference to user document ID |
| productId | string | ✓ | ✓ | Reference to product document ID |
| viewedAt | timestamp | ✓ | ✓ | Date product was viewed |

**Composite Indexes:**

| Fields | Order |
|--------|-------|
| userId, viewedAt | ASC, DESC |

### Settings Collection

**Collection Path:** `/settings`

This collection uses predefined document IDs rather than auto-generated ones.

#### General Settings Document

**Document Path:** `/settings/general`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| storeName | string | ✓ | ○ | Store name |
| storeEmail | string | ✓ | ○ | Store email |
| storePhone | string | ✓ | ○ | Store phone |
| storeAddress | string | ✓ | ○ | Store address |
| storeCity | string | ✓ | ○ | Store city |
| storeState | string | ✓ | ○ | Store state/province |
| storeZip | string | ✓ | ○ | Store ZIP/postal code |
| storeCountry | string | ✓ | ○ | Store country |
| logoUrl | string | ○ | ○ | Store logo URL |
| faviconUrl | string | ○ | ○ | Store favicon URL |
| updatedAt | timestamp | ✓ | ○ | Last update date |

#### Payment Settings Document

**Document Path:** `/settings/payment`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| enabledMethods | array of strings | ✓ | ○ | Enabled payment methods |
| stripePublicKey | string | ○ | ○ | Stripe public key |
| stripeSecretKey | string | ○ | ○ | Stripe secret key (encrypted) |
| paypalClientId | string | ○ | ○ | PayPal client ID |
| paypalSecretKey | string | ○ | ○ | PayPal secret key (encrypted) |
| updatedAt | timestamp | ✓ | ○ | Last update date |

#### Email Settings Document

**Document Path:** `/settings/email`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| fromEmail | string | ✓ | ○ | From email address |
| fromName | string | ✓ | ○ | From name |
| replyToEmail | string | ○ | ○ | Reply-to email address |
| orderConfirmationTemplate | string | ○ | ○ | Order confirmation email template |
| shippingConfirmationTemplate | string | ○ | ○ | Shipping confirmation email template |
| welcomeTemplate | string | ○ | ○ | Welcome email template |
| passwordResetTemplate | string | ○ | ○ | Password reset email template |
| updatedAt | timestamp | ✓ | ○ | Last update date |

### Stats Collection

**Collection Path:** `/stats`

This collection uses predefined document IDs based on the type of statistics.

#### Daily Stats Document

**Document Path:** `/stats/daily_YYYY_MM_DD`

| Field | Type | Required | Indexed | Description |
|-------|------|:--------:|:-------:|-------------|
| id | string | ✓ | ✓ | Document ID (e.g., "daily_2023_06_20") |
| date | string | ✓ | ✓ | Date in ISO format |
| orders | map | ✓ | ○ | Order statistics for the day |
| products | map | ✓ | ○ | Product statistics for the day |
| users | map | ✓ | ○ | User statistics for the day |
| revenue | map | ✓ | ○ | Revenue statistics for the day |
| updatedAt | timestamp | ✓ | ✓ | Last update time |

**Orders Map Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| count | number | ✓ | Total number of orders |
| total | number | ✓ | Total order value |
| byStatus | map | ✓ | Count of orders by status |
| byPaymentMethod | map | ✓ | Count of orders by payment method |

**Products Map Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| sold | number | ✓ | Total number of products sold |
| topSelling | array of maps | ✓ | Top selling products |
| lowStock | array of maps | ○ | Products with low stock |

**Users Map Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| new | number | ✓ | Number of new users |
| active | number | ✓ | Number of active users |

**Revenue Map Structure:**

| Field | Type | Required | Description |
|-------|------|:--------:|-------------|
| total | number | ✓ | Total revenue |
| subtotal | number | ✓ | Subtotal amount |
| shipping | number | ✓ | Shipping revenue |
| tax | number | ✓ | Tax collected |
| discount | number | ✓ | Total discounts |

## Security Rules

The following security rules govern access to the database:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        (request.auth.token.admin == true || request.auth.token.role == 'admin');
    }
    
    function isEditor() {
      return isAuthenticated() && 
        (isAdmin() || request.auth.token.role == 'editor');
    }
    
    function isViewer() {
      return isAuthenticated() && 
        (isEditor() || request.auth.token.role == 'viewer');
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Validation functions
    function isValidProduct() {
      let requiredFields = ['name', 'description', 'price', 'images', 'category', 
                           'categoryId', 'stock', 'status', 'slug'];
      return requiredFields.every(field => request.resource.data[field] != null);
    }
    
    function isValidCategory() {
      let requiredFields = ['name', 'description', 'slug', 'isActive', 'order'];
      return requiredFields.every(field => request.resource.data[field] != null);
    }
    
    // Products collection
    match /products/{productId} {
      allow read: if true; // Public read access
      allow create: if isEditor() && isValidProduct();
      allow update: if isEditor() && isValidProduct();
      allow delete: if isAdmin();
    }
    
    // Categories collection
    match /categories/{categoryId} {
      allow read: if true; // Public read access
      allow create: if isEditor() && isValidCategory();
      allow update: if isEditor() && isValidCategory();
      allow delete: if isAdmin();
    }
    
    // Orders collection
    match /orders/{orderId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow create: if isAuthenticated();
      allow update: if isAdmin() || 
        (isOwner(resource.data.userId) && 
         resource.data.status == 'pending');
      allow delete: if isAdmin();
      
      // Order notes subcollection
      match /notes/{noteId} {
        allow read: if isAuthenticated() && 
          (isAdmin() || 
           (resource.data.isInternal == false && 
            get(/databases/$(database)/documents/orders/$(orderId)).data.userId == request.auth.uid));
        allow create: if isAuthenticated() && 
          (isAdmin() || 
           get(/databases/$(database)/documents/orders/$(orderId)).data.userId == request.auth.uid);
        allow update, delete: if isAdmin();
      }
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || isOwner(userId));
      allow create: if isAdmin();
      allow update: if isAdmin() || isOwner(userId);
      allow delete: if isAdmin();
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if true; // Public read access
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow delete: if isAdmin() || 
        (isAuthenticated() && resource.data.userId == request.auth.uid);
    }
    
    // Wishlist collection
    match /wishlists/{wishlistId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
    }
    
    // Recently viewed collection
    match /recentlyViewed/{itemId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
    }
    
    // Settings collection
    match /settings/{settingId} {
      allow read: if true; // Public read access for settings
      allow write: if isAdmin();
    }
    
    // Stats collection
    match /stats/{statId} {
      allow read: if isViewer();
      allow write: if isAdmin();
    }
  }
}
```

## Data Consistency and Integrity

### Denormalization Strategy

The following data is denormalized to improve query performance:

1. **Products Collection**:
   - Category name is stored in product documents
   - Rating average and count are stored in product documents

2. **Orders Collection**:
   - Product details (name, price, image) are stored in order items
   - User details are not stored (retrieved from users collection when needed)

3. **Reviews Collection**:
   - User name is stored in review documents

### Maintaining Denormalized Data

Cloud Functions are used to maintain consistency of denormalized data:

1. **Category Updates**:
   - When a category name changes, update all products with that categoryId

2. **Product Rating Updates**:
   - When a review is added/updated/deleted, recalculate product rating average and count

3. **Order Status Updates**:
   - When order status changes, update the statusHistory array

### Transactions

Firestore transactions are used for operations that require atomic updates:

1. **Inventory Management**:
   - Decreasing product stock when an order is placed
   - Restoring stock when an order is cancelled

2. **User Role Changes**:
   - Updating both Firestore user document and Firebase Auth custom claims

## Schema Versioning

Each document includes an implicit schema version through its structure. When making significant schema changes:

1. Add a `schemaVersion` field to affected collections
2. Use Cloud Functions to migrate documents to the new schema
3. Update application code to handle both old and new schema versions during the migration period

## Performance Optimization

### Aggregation Documents

The `stats` collection contains pre-aggregated data to avoid expensive queries:

1. **Daily Stats**: Updated at the end of each day
2. **Monthly Stats**: Updated at the end of each month
3. **Product Stats**: Updated when related data changes
4. **Category Stats**: Updated when related data changes

### Query Patterns

Common query patterns are optimized with appropriate indexes:

1. **Product Listing**:
   - Filter by status, category, featured status
   - Sort by price, creation date
   - Paginate results

2. **Order Management**:
   - Filter by status, user, date range
   - Sort by date, total amount
   - Paginate results

3. **User Management**:
   - Filter by role, disabled status
   - Sort by creation date
   - Search by email or name

## Implementation Guidelines

### Backend Implementation

1. Use the `firebase-admin` SDK for server-side operations
2. Validate data against this schema before writing to Firestore
3. Use batch operations for multiple document updates
4. Implement Cloud Functions for maintaining denormalized data

### Frontend Implementation

1. Use the Firebase Web SDK for client-side operations
2. Implement client-side validation matching the schema
3. Use the appropriate security rules for each operation
4. Cache frequently accessed data to reduce read operations
