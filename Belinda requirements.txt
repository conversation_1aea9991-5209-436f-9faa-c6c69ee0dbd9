Okay, let's break down the client's requirement for an e-commerce website focused on interior materials (curtains, wall designs) for the Zimbabwean market (and potentially wider), with specific payment needs.

We'll define the main user flows, the screens involved in those flows, and the functionalities (UI & Business Logic) required for each. We'll use Mermaid syntax for the flows.

**Core Concepts:**

1.  **User Roles:**
    *   **Customer:** Browses, orders, pays.
    *   **Admin (Client):** Manages products, orders, content, views reports.
2.  **Key Entities:** Products, Categories, Orders, Users, Payments, Addresses.
3.  **Payment Challenge:** Accepting online payments (likely in USD, given the receiving accounts) and routing them to either a Mastercard or a specific Chinese USD account. This heavily depends on the chosen **Payment Gateway**. The USD-to-RMB conversion typically happens *within* the Chinese bank account system after receiving USD, not directly controlled by the website during the transaction.

---

## 1. High-Level Application Flows (Mermaid)

Here are the primary user journeys:

**a) Customer: Browsing and Ordering Flow**

```mermaid
graph TD
    A[User Visits Website] --> B{Browsing Options};
    B --> C[View Homepage / Featured Items];
    B --> D[Browse by Category];
    B --> E[Search for Products];
    C --> F[Select Product];
    D --> G[View Category Product List];
    E --> H[View Search Results Product List];
    G --> F;
    H --> F;
    F --> I[View Product Details Page (Images, Desc, Price)];
    I --> J[Add to Cart];
    J --> K{Continue Shopping or View Cart?};
    K -- Continue Shopping --> B;
    K -- View Cart --> L[View Shopping Cart];
    L --> M[Proceed to Checkout];
    M --> N{User Logged In?};
    N -- Yes --> O[Enter/Confirm Shipping Address];
    N -- No --> P[Login / Register / Guest Checkout];
    P -- Login/Register --> O;
    P -- Guest Checkout --> O;
    O --> Q[Select Payment Method];
    Q --> R[Review Order Summary];
    R --> S[Place Order & Initiate Payment];
    S --> T{Payment Gateway Interaction};
    T -- Payment Success --> U[Order Confirmation Page];
    T -- Payment Failed --> V[Payment Failed Page / Retry Option];
    U --> W[End Flow / View Order History];
    V --> Q; # Allow retry payment method or review cart
```

**b) Customer: Account Management Flow**

```mermaid
graph TD
    A[User Clicks 'Account'/'Login'] --> B{User Logged In?};
    B -- No --> C[Login Page];
    B -- Yes --> D[Account Dashboard];
    C --> E{Enter Credentials};
    E -- Valid --> D;
    E -- Invalid --> C;
    C --> F[Forgot Password?];
    C --> G[Register Option];
    G --> H[Registration Page];
    H --> I{Enter Details & Submit};
    I -- Success --> D;
    I -- Failure --> H;
    D --> J[View Order History];
    D --> K[Manage Addresses];
    D --> L[Edit Profile Details];
    J --> M[View Specific Order Details];
    K --> N[Add/Edit/Delete Address];
    L --> O[Update Profile Info & Save];
```

**c) Admin: Product and Order Management Flow**

```mermaid
graph TD
    A[Admin Visits Admin Login URL] --> B[Admin Login Page];
    B --> C{Enter Credentials};
    C -- Valid --> D[Admin Dashboard];
    C -- Invalid --> B;
    D --> E[Manage Products];
    D --> F[Manage Orders];
    D --> G[Manage Categories];
    D --> H[Manage Users (Optional)];
    D --> I[Settings (Payment Gateway Config?)]
    E --> J[View Product List];
    J --> K[Add New Product];
    J --> L[Edit Existing Product];
    J --> M[Delete Product];
    K --> N[Product Form (Details, Images, Price, Category)];
    L --> N;
    F --> O[View Order List (Filter/Sort)];
    O --> P[View Specific Order Details];
    P --> Q[Update Order Status (e.g., Processing, Shipped)];
    G --> R[View Category List];
    R --> S[Add/Edit/Delete Category];
```

---

## 2. Key Screens and Functionalities

Based on the flows, here are the essential screens and their required functionalities:

**A. Public Facing Screens (Customer)**

1.  **Homepage**
    *   **UI:** Logo, Navigation (Categories, Cart, Account), Hero Banner/Slider (featuring products/promotions), Featured Products grid, Category links/images, Footer (About, Contact, T&Cs, Payment Logos).
    *   **Business Logic:** Fetch and display featured products/categories dynamically. Ensure navigation links work.

2.  **Category/Product Listing Page**
    *   **UI:** Breadcrumbs (e.g., Home > Curtains), Category Title/Description, Optional Filters (e.g., color, price range, style), Sorting options (e.g., Price Low-High), Product Grid/List (Image, Name, Price, "Add to Cart" button or "View Details" link). Pagination if many products.
    *   **Business Logic:** Fetch products belonging to the selected category or matching search criteria. Implement filtering and sorting logic. Handle clicks on products (navigate to detail) or "Add to Cart".

3.  **Product Detail Page**
    *   **UI:** Product Name, High-resolution Images (multiple views, zoom capability, potentially video), Detailed Description, Price, Variant Selection (e.g., Size for curtains, Color), Quantity Selector, "Add to Cart" button, Category/Tags, Breadcrumbs.
    *   **Business Logic:** Fetch detailed information for a specific product. Handle variant selection (update price/availability if necessary). Validate quantity. Add the selected product/variant/quantity to the user's shopping cart session.

4.  **Shopping Cart Page**
    *   **UI:** List of items in cart (Image, Name, Variant, Unit Price, Quantity, Subtotal), Ability to update quantity, Ability to remove items, Cart Subtotal, Estimated Shipping (optional, calculated later), Total Price, "Proceed to Checkout" button, "Continue Shopping" link.
    *   **Business Logic:** Retrieve cart contents from user session/database. Calculate line totals and cart total. Handle quantity updates and item removals, updating the cart state and totals accordingly.

5.  **Checkout Page (Can be single or multi-step)**
    *   **UI:**
        *   *Login/Register/Guest Option:* If not logged in.
        *   *Shipping Address:* Form to enter/select shipping address (Name, Street, City, Country - especially Zimbabwe, Phone).
        *   *Billing Address:* Option to use shipping address or enter a different one.
        *   *Shipping Method:* (If applicable, e.g., Standard, Express - may not be needed initially). Display cost if any.
        *   *Payment Method Selection:* Radio buttons/icons for available methods (e.g., "Pay with Card"). Display accepted card logos (Mastercard mandatory).
        *   *Order Summary:* Recap of items, subtotal, shipping, total.
        *   *Payment Area:* Secure form (often an iframe provided by the payment gateway) to enter card details (Number, Expiry, CVV).
        *   *"Place Order" / "Pay Now" Button.*
    *   **Business Logic:**
        *   Address validation (basic format checks).
        *   Store selected addresses.
        *   Calculate final total including any shipping.
        *   **CRITICAL:** Integrate with the chosen **Payment Gateway** API.
            *   On "Place Order", create an order record in the database (status: pending payment).
            *   Initiate payment transaction with the gateway, passing order details (amount, currency: **USD**).
            *   Handle the response from the gateway (success/failure). Securely handle sensitive card data (PCI compliance - usually offloaded to the gateway's secure environment).
            *   Update order status based on payment success/failure.

6.  **Order Confirmation Page**
    *   **UI:** Success message ("Thank you for your order!"), Order Number/ID, Summary of order placed (items, total, shipping address), Contact information for support.
    *   **Business Logic:** Display details of the successfully processed order. Trigger confirmation email to the customer and potentially an alert to the admin.

7.  **Payment Failed Page**
    *   **UI:** Failure message ("Payment Failed"), Reason for failure (if provided by gateway), Option to retry payment (perhaps choosing a different method or re-entering details), Link back to cart or contact support.
    *   **Business Logic:** Provide clear feedback and options to the user upon payment failure.

8.  **Login Page**
    *   **UI:** Email/Username field, Password field, "Login" button, "Forgot Password?" link, "Register" link.
    *   **Business Logic:** Authenticate user credentials against the database. Manage user sessions. Handle password recovery initiation.

9.  **Registration Page**
    *   **UI:** Name, Email, Password, Confirm Password fields, "Register" button, Link to Login.
    *   **Business Logic:** Validate input data (e.g., email format, password strength, password match). Check if email already exists. Create new user record in the database. Log the user in automatically after registration.

10. **Account Dashboard Page**
    *   **UI:** Welcome message, Links to Order History, Manage Addresses, Edit Profile, Logout. Maybe a summary of recent orders.
    *   **Business Logic:** Central navigation hub for logged-in user account management.

11. **Order History Page**
    *   **UI:** List of past orders (Order ID, Date, Total Amount, Status - e.g., Processing, Shipped, Delivered), Link to view details for each order.
    *   **Business Logic:** Fetch and display the logged-in user's order history from the database.

12. **Order Detail Page (User View)**
    *   **UI:** All details from Confirmation Page plus current Order Status, Tracking information (if applicable).
    *   **Business Logic:** Fetch and display complete details for a specific past order.

13. **Manage Addresses Page**
    *   **UI:** List of saved shipping/billing addresses, Option to add new address, Edit/Delete existing addresses. Mark one as default.
    *   **Business Logic:** CRUD operations for the user's saved addresses.

14. **Edit Profile Page**
    *   **UI:** Form to update Name, Email, Password.
    *   **Business Logic:** Update user record in the database. Handle password change securely (require current password).

**B. Admin Screens**

1.  **Admin Login Page**
    *   **UI:** Similar to user login, but typically at a different URL (e.g., `/admin/login`).
    *   **Business Logic:** Authenticate admin credentials.

2.  **Admin Dashboard**
    *   **UI:** Overview stats (e.g., recent orders, total sales, new users), Navigation menu for admin sections (Products, Orders, Categories, etc.).
    *   **Business Logic:** Fetch and display summary data. Provide access to management sections.

3.  **Admin Product List Page**
    *   **UI:** Table/List of all products (Thumbnail, Name, SKU, Price, Stock Status, Category), Options to Add New, Edit, Delete. Search/Filter capabilities.
    *   **Business Logic:** Fetch and display all products. Handle actions (navigate to edit form, trigger deletion).

4.  **Admin Product Edit/Add Page**
    *   **UI:** Form with fields for Product Name, Description (rich text editor recommended), SKU, Price, Sale Price (optional), Categories (multi-select or dropdown), Image Uploader (for multiple images), Stock quantity (optional), Product status (Active/Inactive).
    *   **Business Logic:** For Add: Create new product record. For Edit: Fetch existing product data and update the record. Handle image uploads and association with the product. Input validation.

5.  **Admin Order List Page**
    *   **UI:** Table/List of all orders (Order ID, Customer Name, Date, Total Amount, Order Status), Filtering (by status, date range), Sorting. Link to view details.
    *   **Business Logic:** Fetch and display orders. Implement filtering/sorting.

6.  **Admin Order Detail Page**
    *   **UI:** Complete order details (Customer info, Billing/Shipping addresses, Items ordered, Prices, Totals, Payment method, Transaction ID if available), **Dropdown/Buttons to update Order Status** (e.g., Pending -> Processing -> Shipped -> Delivered/Completed -> Cancelled). Field to add Tracking Number (optional). Button to resend confirmation email (optional).
    *   **Business Logic:** Fetch specific order details. Update order status in the database. Optionally trigger status update notifications to the customer.

7.  **Admin Category Management Page**
    *   **UI:** List of categories, Add/Edit/Delete category options. Form for category name and description.
    *   **Business Logic:** Basic CRUD operations for product categories.

---

## 3. Payment Integration Considerations (Crucial)

*   **Payment Gateway Choice:** This is the most critical technical decision.
    *   **Needs:** Must support online Mastercard payments, must process transactions in **USD**, and crucially, must allow **settlement/payout** to either the client's Mastercard or their specified Chinese USD bank account.
    *   **Zimbabwean Options:** Look into gateways popular in Zimbabwe like **Paynow Zimbabwe**, **DPO Paygate (Direct Pay Online)**. Verify their international settlement capabilities (USD payout to Mastercard/Chinese bank).
    *   **International Options:** **Stripe Atlas** (if client can form a US entity) or other international gateways might offer settlement to international accounts, but check their support for Zimbabwe-issued cards and the specific payout destinations. **2Checkout (now Verifone)** is another possibility.
    *   **Direct Mastercard Integration:** Directly integrating with Mastercard is complex and usually requires significant PCI DSS compliance overhead. Using a gateway is standard practice.
    *   **Chinese Account:** Receiving USD into a Chinese account is common. The gateway needs to support sending USD to that specific bank via SWIFT/wire transfer or another mechanism. The *conversion to RMB* happens within the Chinese banking system *after* the USD funds land, managed by the account holder, not the website payment flow.
*   **Implementation:**
    *   The website backend will communicate with the chosen gateway's API.
    *   The frontend will often use the gateway's provided JavaScript library or iframe to securely collect card details, minimizing PCI scope for your website.
    *   **Webhooks:** Use gateway webhooks (server-to-server notifications) to reliably confirm payment success/failure and update order status, rather than relying solely on the customer being redirected back to the site.


Note: We will implement the payment Integration last. Before integration we will implement a simple class that mimic popular payment flows or methods
---

## 4. Technology Stack Suggestions (Examples)

*   **Frontend:** NextJs
*   **Backend:** Firebase, firebase data connect
*   **Hosting:** Vercel

---
