{"name": "belinda-ecommerce", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "node scripts/create-first-admin.js", "set-admin": "node scripts/set-admin-claims.js", "set-admin-by-uid": "node scripts/set-admin-by-uid.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@stripe/react-stripe-js": "^3.6.0", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-devtools": "^5.72.2", "@types/nodemailer": "^6.4.17", "@types/stripe": "^8.0.417", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "lucide-react": "^0.487.0", "next": "15.3.0", "next-swagger-doc": "^0.4.1", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "openapi-types": "7.0.1", "pdfkit": "^0.17.0", "react": "^19.0.0", "react-day-picker": "^9.6.6", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sharp": "^0.34.1", "sonner": "^2.0.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-react": "^5.21.0", "tailwind-merge": "^3.2.0", "tls": "^0.0.1", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pdfkit": "^0.13.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4", "typescript": "^5"}}