# Belinda Interior E-commerce

A modern e-commerce platform for Belinda Interior, built with Next.js, Firebase, and Stripe.

## Features

- Product catalog with categories and search
- Shopping cart and checkout flow
- User authentication and account management
- Admin dashboard for product and order management
- Stripe payment integration
- Responsive design for all devices

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn
- Firebase account
- Stripe account

### Environment Setup

1. Clone the repository
2. Copy the example environment file:

```bash
cp .env.local.example .env.local
```

3. Fill in your Firebase and Stripe credentials in `.env.local`

### Installation

Install dependencies:

```bash
npm install
# or
yarn install
```

Run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Stripe Integration

This project uses Stripe for payment processing. To set up Stripe:

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe Dashboard
3. Add your Stripe publishable key to `.env.local` as `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
4. Add your Stripe secret key to `.env.local` as `STRIPE_SECRET_KEY`
5. Set up a webhook in the Stripe Dashboard:
   - Endpoint URL: `https://your-domain.com/api/payment/webhook`
   - Events to listen for: `payment_intent.succeeded`, `payment_intent.payment_failed`
6. Add your webhook secret to `.env.local` as `STRIPE_WEBHOOK_SECRET`

### Testing Payments

For testing, use Stripe's test card numbers:
- Card number: `4242 4242 4242 4242`
- Expiration: Any future date
- CVC: Any 3 digits
- ZIP: Any 5 digits

## Firebase Setup

1. Create a Firebase project
2. Enable Authentication, Firestore, and Storage
3. Add your Firebase configuration to `.env.local`
4. To create the first admin user, run:

```bash
npm run create-admin
```

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Stripe Documentation](https://stripe.com/docs)

## Deployment

The easiest way to deploy this app is to use [Vercel](https://vercel.com/new) from the creators of Next.js.

For the Stripe webhook to work in production, make sure to:
1. Update the webhook URL in your Stripe Dashboard
2. Add the production environment variables to your hosting platform
