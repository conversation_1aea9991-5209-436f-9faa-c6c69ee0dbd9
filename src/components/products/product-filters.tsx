'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Filter, X, ChevronDown, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Category } from '@/types';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
} from '@/components/ui/sheet';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ProductFiltersProps {
  categories: Category[];
  minPrice: number;
  maxPrice: number;
}

export function ProductFilters({ categories, minPrice, maxPrice }: ProductFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current filter values from URL
  const currentCategory = searchParams.get('category') || '';
  const currentMinPrice = searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : minPrice;
  const currentMaxPrice = searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : maxPrice;
  const currentSort = searchParams.get('sort') || 'newest';
  
  // Local state for mobile filters
  const [mobileFilters, setMobileFilters] = useState({
    category: currentCategory,
    priceRange: [currentMinPrice, currentMaxPrice] as [number, number],
  });
  
  // Local state for desktop price range
  const [desktopPriceRange, setDesktopPriceRange] = useState<[number, number]>([
    currentMinPrice, 
    currentMaxPrice
  ]);
  
  // Apply filters
  const applyFilters = (
    category: string, 
    priceRange: [number, number], 
    sort: string = currentSort
  ) => {
    const params = new URLSearchParams();
    
    if (category) {
      params.set('category', category);
    }
    
    if (priceRange[0] > minPrice) {
      params.set('minPrice', priceRange[0].toString());
    }
    
    if (priceRange[1] < maxPrice) {
      params.set('maxPrice', priceRange[1].toString());
    }
    
    if (sort) {
      params.set('sort', sort);
    }
    
    router.push(`/products?${params.toString()}`);
  };
  
  // Handle sort change
  const handleSortChange = (value: string) => {
    applyFilters(currentCategory, [currentMinPrice, currentMaxPrice], value);
  };
  
  // Handle desktop category click
  const handleCategoryClick = (category: string) => {
    applyFilters(
      category === currentCategory ? '' : category, 
      desktopPriceRange
    );
  };
  
  // Handle desktop price change
  const handleDesktopPriceChange = (value: [number, number]) => {
    setDesktopPriceRange(value);
  };
  
  // Apply desktop price filter (on mouse up)
  const applyDesktopPriceFilter = () => {
    applyFilters(currentCategory, desktopPriceRange);
  };
  
  // Handle mobile filter changes
  const handleMobileFilterChange = (
    key: 'category' | 'priceRange', 
    value: string | [number, number]
  ) => {
    setMobileFilters({
      ...mobileFilters,
      [key]: value,
    });
  };
  
  // Apply mobile filters
  const applyMobileFilters = () => {
    applyFilters(
      mobileFilters.category, 
      mobileFilters.priceRange
    );
  };
  
  // Reset all filters
  const resetFilters = () => {
    router.push('/products');
    setDesktopPriceRange([minPrice, maxPrice]);
    setMobileFilters({
      category: '',
      priceRange: [minPrice, maxPrice],
    });
  };
  
  // Check if any filters are active
  const hasActiveFilters = 
    currentCategory || 
    currentMinPrice > minPrice || 
    currentMaxPrice < maxPrice;
  
  // Format price for display
  const formatPrice = (price: number) => `$${price.toFixed(0)}`;

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          {/* Mobile filter button */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="sm:hidden">
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-full sm:max-w-md">
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
              </SheetHeader>
              
              <div className="py-6 space-y-6">
                <Accordion type="single" collapsible defaultValue="category">
                  {/* Categories */}
                  <AccordionItem value="category">
                    <AccordionTrigger>Categories</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 pt-1">
                        <div 
                          className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer ${
                            mobileFilters.category === '' ? 'bg-primary/10 text-primary' : 'hover:bg-muted'
                          }`}
                          onClick={() => handleMobileFilterChange('category', '')}
                        >
                          <span>All Categories</span>
                          {mobileFilters.category === '' && (
                            <Check className="h-4 w-4" />
                          )}
                        </div>
                        
                        {categories.map((category) => (
                          <div 
                            key={category.id}
                            className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer ${
                              mobileFilters.category === category.id ? 'bg-primary/10 text-primary' : 'hover:bg-muted'
                            }`}
                            onClick={() => handleMobileFilterChange('category', category.id)}
                          >
                            <span>{category.name}</span>
                            {mobileFilters.category === category.id && (
                              <Check className="h-4 w-4" />
                            )}
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  
                  {/* Price Range */}
                  <AccordionItem value="price">
                    <AccordionTrigger>Price Range</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 px-1 pt-1">
                        <div className="flex justify-between">
                          <span>{formatPrice(mobileFilters.priceRange[0])}</span>
                          <span>{formatPrice(mobileFilters.priceRange[1])}</span>
                        </div>
                        
                        <Slider
                          value={mobileFilters.priceRange}
                          min={minPrice}
                          max={maxPrice}
                          step={5}
                          onValueChange={(value) => 
                            handleMobileFilterChange('priceRange', value as [number, number])
                          }
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
              
              <SheetFooter className="flex-row gap-3 sm:gap-0">
                <Button 
                  variant="outline" 
                  className="flex-1" 
                  onClick={resetFilters}
                >
                  Reset
                </Button>
                <Button 
                  className="flex-1" 
                  onClick={applyMobileFilters}
                >
                  Apply Filters
                </Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
          
          {/* Active filters indicator */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Active Filters:</span>
              
              {currentCategory && (
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="h-7 gap-1 text-xs"
                  onClick={() => applyFilters('', [currentMinPrice, currentMaxPrice])}
                >
                  {categories.find(c => c.id === currentCategory)?.name || 'Category'}
                  <X className="h-3 w-3" />
                </Button>
              )}
              
              {(currentMinPrice > minPrice || currentMaxPrice < maxPrice) && (
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="h-7 gap-1 text-xs"
                  onClick={() => {
                    applyFilters(currentCategory, [minPrice, maxPrice]);
                    setDesktopPriceRange([minPrice, maxPrice]);
                  }}
                >
                  {formatPrice(currentMinPrice)} - {formatPrice(currentMaxPrice)}
                  <X className="h-3 w-3" />
                </Button>
              )}
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 px-2 text-xs"
                onClick={resetFilters}
              >
                Clear All
              </Button>
            </div>
          )}
        </div>
        
        {/* Sort dropdown */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground hidden sm:inline">Sort by:</span>
          <Select value={currentSort} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[160px] h-9">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest Arrivals</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="name-asc">Name: A to Z</SelectItem>
              <SelectItem value="name-desc">Name: Z to A</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Desktop filters */}
      <div className="hidden sm:grid sm:grid-cols-4 gap-6">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Categories</h3>
            <div className="space-y-2">
              <div 
                className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer ${
                  currentCategory === '' ? 'bg-primary/10 text-primary' : 'hover:bg-muted'
                }`}
                onClick={() => handleCategoryClick('')}
              >
                <span>All Categories</span>
                {currentCategory === '' && (
                  <Check className="h-4 w-4" />
                )}
              </div>
              
              {categories.map((category) => (
                <div 
                  key={category.id}
                  className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer ${
                    currentCategory === category.id ? 'bg-primary/10 text-primary' : 'hover:bg-muted'
                  }`}
                  onClick={() => handleCategoryClick(category.id)}
                >
                  <span>{category.name}</span>
                  {currentCategory === category.id && (
                    <Check className="h-4 w-4" />
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <Separator />
          
          <div>
            <h3 className="font-medium mb-4">Price Range</h3>
            <div className="space-y-4 px-1">
              <div className="flex justify-between">
                <span>{formatPrice(desktopPriceRange[0])}</span>
                <span>{formatPrice(desktopPriceRange[1])}</span>
              </div>
              
              <Slider
                value={desktopPriceRange}
                min={minPrice}
                max={maxPrice}
                step={5}
                onValueChange={handleDesktopPriceChange}
                onValueCommit={applyDesktopPriceFilter}
              />
            </div>
          </div>
        </div>
        
        {/* Product grid will be placed here in the parent component */}
        <div className="col-span-3">
          {/* This is just a placeholder, the actual product grid is rendered by the parent */}
        </div>
      </div>
    </div>
  );
}
