'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ShoppingCart, Heart, Eye, Star } from 'lucide-react';
import { Product } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/contexts/cart-context';

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const { addToCart } = useCart();
  
  // Calculate discount percentage if compareAtPrice exists
  const discountPercentage = product.compareAtPrice 
    ? Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100) 
    : 0;
  
  // Check if product is on sale
  const isOnSale = product.compareAtPrice && product.compareAtPrice > product.price;
  
  // Check if product is new (less than 14 days old)
  const isNew = product.createdAt && 
    (new Date().getTime() - product.createdAt.getTime()) < 14 * 24 * 60 * 60 * 1000;
  
  // Handle add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
  };

  return (
    <div className="group relative">
      <Link href={`/products/${product.slug}`} className="block">
        <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
          {/* Product image */}
          {product.images && product.images.length > 0 ? (
            <Image
              src={product.images[0]}
              alt={product.name}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              unoptimized
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gray-200">
              <ShoppingCart className="h-12 w-12 text-gray-400" />
            </div>
          )}
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {isOnSale && (
              <Badge className="bg-red-500 hover:bg-red-600">
                {discountPercentage}% OFF
              </Badge>
            )}
            {isNew && (
              <Badge className="bg-blue-500 hover:bg-blue-600">
                NEW
              </Badge>
            )}
            {product.featured && (
              <Badge className="bg-purple-500 hover:bg-purple-600">
                FEATURED
              </Badge>
            )}
          </div>
          
          {/* Quick action buttons */}
          <div className="absolute bottom-2 right-2 flex flex-col gap-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
            <Button 
              size="icon" 
              variant="secondary" 
              className="h-8 w-8 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
              onClick={handleAddToCart}
              aria-label="Add to cart"
            >
              <ShoppingCart className="h-4 w-4" />
            </Button>
            <Button 
              size="icon" 
              variant="secondary" 
              className="h-8 w-8 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
              aria-label="Add to wishlist"
            >
              <Heart className="h-4 w-4" />
            </Button>
            <Button 
              size="icon" 
              variant="secondary" 
              className="h-8 w-8 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
              aria-label="Quick view"
              asChild
            >
              <Link href={`/products/${product.slug}`}>
                <Eye className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
        
        {/* Product info */}
        <div className="mt-3 space-y-1 text-left">
          <h3 className="font-medium text-gray-900 group-hover:text-primary">
            {product.name}
          </h3>
          
          {/* Category */}
          <p className="text-sm text-gray-500">
            {product.category}
          </p>
          
          {/* Rating */}
          {product.ratingAvg && (
            <div className="flex items-center gap-1">
              <Star className="h-3.5 w-3.5 fill-amber-400 text-amber-400" />
              <span className="text-sm font-medium">{product.ratingAvg.toFixed(1)}</span>
              {product.ratingCount && (
                <span className="text-xs text-gray-500">
                  ({product.ratingCount})
                </span>
              )}
            </div>
          )}
          
          {/* Price */}
          <div className="flex items-center gap-2">
            <p className="font-medium text-gray-900">
              ${product.price.toFixed(2)}
            </p>
            {isOnSale && (
              <p className="text-sm text-gray-500 line-through">
                ${product.compareAtPrice?.toFixed(2)}
              </p>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
}
