'use client';

import { useState } from 'react';
import { Mail, X, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function EmailVerificationBanner() {
  const { user, sendVerificationEmail, isEmailVerified } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Don't show banner if user is not logged in, email is verified, or banner was dismissed
  if (!user || user.emailVerified || isDismissed) {
    return null;
  }

  const handleSendVerification = async () => {
    try {
      setIsLoading(true);
      await sendVerificationEmail();
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000); // Hide success message after 5 seconds
    } catch (error) {
      console.error('Error sending verification email:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-primary/10 py-2 px-4">
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Mail className="h-4 w-4 text-primary" />
            <p className="text-sm">
              Please verify your email address to access all features.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {showSuccess ? (
              <Alert variant="default" className="py-1 px-2 bg-green-100 border-green-200">
                <AlertCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-xs text-green-600">
                  Verification email sent!
                </AlertDescription>
              </Alert>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSendVerification}
                disabled={isLoading}
                className="text-xs h-8"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Resend verification email'
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsDismissed(true)}
              className="h-6 w-6"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Dismiss</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
