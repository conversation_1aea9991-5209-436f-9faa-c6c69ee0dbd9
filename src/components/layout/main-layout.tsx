'use client';

import { Head<PERSON> } from './header';
import { Footer } from './footer';
import { EmailVerificationBanner } from '@/components/auth/email-verification-banner';

export function MainLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <EmailVerificationBanner />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
}