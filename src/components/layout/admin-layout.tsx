'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Layers,
  Tag,
  Image as ImageIcon,
  Truck,
  CreditCard
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { AdminProtectedRoute } from '@/components/auth/admin-protected-route';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ElementType;
  submenu?: { title: string; href: string }[];
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { user, signOut } = useAuth();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

  const sidebarItems: SidebarItem[] = [
    {
      title: 'Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
    },
    {
      title: 'Products',
      href: '/admin/products',
      icon: Package,
      submenu: [
        { title: 'All Products', href: '/admin/products' },
        { title: 'Add Product', href: '/admin/products/new' },
      ],
    },
    {
      title: 'Categories',
      href: '/admin/categories',
      icon: Layers,
      submenu: [
        { title: 'All Categories', href: '/admin/categories' },
        { title: 'Add Category', href: '/admin/categories/new' },
      ],
    },
    {
      title: 'Orders',
      href: '/admin/orders',
      icon: ShoppingCart,
    },
    {
      title: 'Payments',
      href: '/admin/payments',
      icon: CreditCard,
    },
    {
      title: 'Users',
      href: '/admin/users',
      icon: Users,
    },
    {
      title: 'Content',
      href: '/admin/carousel',
      icon: ImageIcon,
      submenu: [
        { title: 'Carousel Slides', href: '/admin/carousel' },
        { title: 'Special Offers', href: '/admin/special-offers' },
        { title: 'New Arrivals', href: '/admin/new-arrivals' },
      ],
    },
    {
      title: 'Shipping',
      href: '/admin/shipping',
      icon: Truck,
      submenu: [
        { title: 'Shipping Methods', href: '/admin/shipping/methods' },
        { title: 'Add Shipping Method', href: '/admin/shipping/methods/new' },
      ],
    },
    {
      title: 'Settings',
      href: '/admin/settings',
      icon: Settings,
    },
  ];

  const toggleSubmenu = (title: string) => {
    if (openSubmenu === title) {
      setOpenSubmenu(null);
    } else {
      setOpenSubmenu(title);
    }
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };

  return (
    <AdminProtectedRoute>
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
        {/* Mobile sidebar toggle */}
        <div className="lg:hidden fixed top-0 left-0 right-0 z-30 bg-white dark:bg-gray-800 border-b p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <Menu className="h-6 w-6" />
            </Button>
            <Link href="/admin" className="font-bold text-xl">
              Belinda Admin
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Avatar className="h-8 w-8">
                    {user?.photoURL ? (
                      <AvatarImage src={user.photoURL} alt={user.displayName} />
                    ) : (
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {user?.displayName?.charAt(0) ?? user?.email?.charAt(0) ?? 'A'}
                      </AvatarFallback>
                    )}
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/">View Store</Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => signOut()}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Sidebar */}
        <div
          className={cn(
            "fixed inset-y-0 left-0 z-20 w-64 transform bg-white dark:bg-gray-800 border-r transition-transform duration-200 ease-in-out lg:translate-x-0",
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          )}
        >
          <div className="flex flex-col h-full">
            {/* Sidebar header */}
            <div className="flex items-center justify-between p-4 border-b">
              <Link href="/admin" className="font-bold text-xl">
                Belinda Admin
              </Link>
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setIsSidebarOpen(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Sidebar content */}
            <div className="flex-1 overflow-y-auto py-4">
              <nav className="px-2 space-y-1">
                {sidebarItems.map((item) => (
                  <div key={item.title}>
                    {item.submenu ? (
                      <>
                        <button
                          onClick={() => toggleSubmenu(item.title)}
                          className={cn(
                            "flex items-center w-full px-3 py-2 text-sm font-medium rounded-md group",
                            isActive(item.href)
                              ? "bg-primary/10 text-primary"
                              : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                          )}
                        >
                          <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                          <span className="flex-1 text-left">{item.title}</span>
                          <ChevronDown
                            className={cn(
                              "h-4 w-4 transition-transform",
                              openSubmenu === item.title && "rotate-180"
                            )}
                          />
                        </button>
                        {openSubmenu === item.title && (
                          <div className="mt-1 pl-10 space-y-1">
                            {item.submenu.map((subitem) => (
                              <Link
                                key={subitem.href}
                                href={subitem.href}
                                className={cn(
                                  "block px-3 py-2 text-sm font-medium rounded-md",
                                  isActive(subitem.href)
                                    ? "bg-primary/10 text-primary"
                                    : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                                )}
                              >
                                {subitem.title}
                              </Link>
                            ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-sm font-medium rounded-md",
                          isActive(item.href)
                            ? "bg-primary/10 text-primary"
                            : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                        )}
                      >
                        <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                        {item.title}
                      </Link>
                    )}
                  </div>
                ))}
              </nav>
            </div>

            {/* Sidebar footer */}
            <div className="p-4 border-t">
              <div className="flex items-center">
                <Avatar className="h-8 w-8 mr-3">
                  {user?.photoURL ? (
                    <AvatarImage src={user.photoURL} alt={user.displayName} />
                  ) : (
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {user?.displayName?.charAt(0) ?? user?.email?.charAt(0) ?? 'A'}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user?.displayName ?? user?.email ?? 'Admin'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    Admin
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => signOut()}
                  className="ml-2"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div
          className={cn(
            "transition-all duration-200 ease-in-out",
            isSidebarOpen ? "lg:ml-64" : "lg:ml-0",
            "pt-16 lg:pt-0" // Add padding top on mobile
          )}
        >
          <main className="min-h-screen p-4 md:p-6 lg:p-8">
            {children}
          </main>
        </div>
      </div>
    </AdminProtectedRoute>
  );
}
