'use client';

import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { LayoutDashboard } from 'lucide-react';
import Link from 'next/link';

export function AdminLink() {
  const { user, isAdmin } = useAuth();

  if (!user || !isAdmin) return null;

  return (
    <Button
      asChild
      variant="ghost"
      size="sm"
      className="flex items-center"
    >
      <Link href="/admin">
        <LayoutDashboard className="mr-2 h-4 w-4" />
        Admin
      </Link>
    </Button>
  );
}
