'use client';

import { useState, useEffect } from 'react';
import { Loader2, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Permission } from '@/lib/firebase/admin-services/role-service';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface UserPermissionsProps {
  userId: string;
  userEmail: string;
}

// Group permissions by category
const permissionGroups = {
  'Products': [
    'read:products',
    'write:products',
    'delete:products',
  ],
  'Categories': [
    'read:categories',
    'write:categories',
    'delete:categories',
  ],
  'Orders': [
    'read:orders',
    'write:orders',
    'delete:orders',
  ],
  'Users': [
    'read:users',
    'write:users',
    'delete:users',
  ],
  'Settings': [
    'read:settings',
    'write:settings',
  ],
  'Dashboard': [
    'read:dashboard',
    'read:reports',
  ],
  'Developer': [
    'read:api-docs',
    'developer',
  ],
  'System': [
    'admin',
  ],
};

// Permission descriptions
const permissionDescriptions: Record<string, string> = {
  'read:products': 'View product information',
  'write:products': 'Create and edit products',
  'delete:products': 'Delete products',
  'read:categories': 'View category information',
  'write:categories': 'Create and edit categories',
  'delete:categories': 'Delete categories',
  'read:orders': 'View order information',
  'write:orders': 'Update order status and information',
  'delete:orders': 'Delete orders',
  'read:users': 'View user information',
  'write:users': 'Edit user information',
  'delete:users': 'Delete users',
  'read:settings': 'View application settings',
  'write:settings': 'Edit application settings',
  'read:dashboard': 'View dashboard statistics',
  'read:reports': 'View sales and analytics reports',
  'read:api-docs': 'Access API documentation',
  'developer': 'Access developer features',
  'admin': 'Full system access (all permissions)',
};

export function UserPermissions({ userId, userEmail }: UserPermissionsProps) {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [originalPermissions, setOriginalPermissions] = useState<Permission[]>([]);
  const { toast } = useToast();

  // Fetch user permissions
  useEffect(() => {
    const fetchUserPermissions = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/users/${userId}/permissions`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user permissions');
        }
        
        const data = await response.json();
        
        if (data.success && data.data) {
          setUserPermissions(data.data);
          setOriginalPermissions(data.data);
        } else {
          throw new Error(data.error || 'Failed to fetch user permissions');
        }
      } catch (error) {
        console.error('Error fetching user permissions:', error);
        toast({
          title: "Error",
          description: "Failed to load user permissions. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserPermissions();
  }, [userId, toast]);

  // Check if a permission is selected
  const isPermissionSelected = (permission: string) => {
    return userPermissions.includes(permission as Permission);
  };

  // Toggle a permission
  const togglePermission = (permission: string) => {
    if (permission === 'admin') {
      // If toggling admin, either add all permissions or remove admin
      if (isPermissionSelected('admin')) {
        // Remove admin permission
        setUserPermissions(userPermissions.filter(p => p !== 'admin'));
      } else {
        // Add all permissions
        const allPermissions = Object.values(permissionGroups).flat() as Permission[];
        setUserPermissions(allPermissions);
      }
    } else {
      // For other permissions
      if (isPermissionSelected(permission)) {
        // Remove permission
        setUserPermissions(userPermissions.filter(p => p !== permission));
        
        // Also remove admin if any permission is removed
        if (isPermissionSelected('admin')) {
          setUserPermissions(prev => prev.filter(p => p !== 'admin'));
        }
      } else {
        // Add permission
        setUserPermissions([...userPermissions, permission as Permission]);
        
        // Check if all permissions are now selected
        const allPermissions = Object.values(permissionGroups).flat() as Permission[];
        const willHaveAllPermissions = allPermissions.every(p => 
          p === permission || isPermissionSelected(p)
        );
        
        if (willHaveAllPermissions) {
          // Add admin permission if all others are selected
          setUserPermissions([...userPermissions, permission as Permission, 'admin']);
        }
      }
    }
  };

  // Save permissions
  const savePermissions = async () => {
    try {
      setSaving(true);
      
      const response = await fetch(`/api/admin/users/${userId}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          permissions: userPermissions,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update user permissions');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setOriginalPermissions(userPermissions);
        toast({
          title: "Success",
          description: "User permissions updated successfully.",
          variant: "default",
        });
      } else {
        throw new Error(data.error || 'Failed to update user permissions');
      }
    } catch (error) {
      console.error('Error updating user permissions:', error);
      toast({
        title: "Error",
        description: "Failed to update user permissions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset permissions to original state
  const resetPermissions = () => {
    setUserPermissions(originalPermissions);
  };

  // Check if permissions have changed
  const hasChanges = () => {
    if (userPermissions.length !== originalPermissions.length) return true;
    return !userPermissions.every(p => originalPermissions.includes(p));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Permissions</CardTitle>
        <CardDescription>
          Manage permissions for {userEmail}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Accordion type="multiple" className="w-full">
          {Object.entries(permissionGroups).map(([group, permissions]) => (
            <AccordionItem key={group} value={group}>
              <AccordionTrigger className="text-base font-medium">
                {group}
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 pt-2">
                  {permissions.map((permission) => (
                    <div key={permission} className="flex items-center space-x-2">
                      <Checkbox
                        id={permission}
                        checked={isPermissionSelected(permission)}
                        onCheckedChange={() => togglePermission(permission)}
                      />
                      <div className="grid gap-1.5 leading-none">
                        <label
                          htmlFor={permission}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {permission}
                        </label>
                        {permissionDescriptions[permission] && (
                          <p className="text-sm text-muted-foreground">
                            {permissionDescriptions[permission]}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={resetPermissions}
          disabled={!hasChanges() || saving}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button
          onClick={savePermissions}
          disabled={!hasChanges() || saving}
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
