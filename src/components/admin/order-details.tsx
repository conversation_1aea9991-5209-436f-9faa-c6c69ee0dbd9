'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import {
  ChevronDown,
  ChevronUp,
  Download,
  FileText,
  Printer,
  Send,
  Truck,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { OrderNote } from '@/lib/firebase/admin-services/order-service';
import { Order, OrderStatus, PaymentStatus } from '@/types';
import { addOrderNote, getOrderNotes, updateOrder } from '@/lib/firebase/services/order-service';

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Helper function to format date
const formatDate = (date: Date | string) => {
  if (!date) return 'N/A';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'MMM d, yyyy h:mm a');
};

// Status badge component
const StatusBadge = ({ status }: { status: OrderStatus }) => {
  const statusConfig = {
    pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
    processing: { color: 'bg-blue-100 text-blue-800', label: 'Processing' },
    shipped: { color: 'bg-purple-100 text-purple-800', label: 'Shipped' },
    delivered: { color: 'bg-green-100 text-green-800', label: 'Delivered' },
    cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
    pending_payment: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending Payment' },
  };

  const config = statusConfig[status] || statusConfig.pending;

  return (
    <Badge variant="outline" className={`${config.color} border-0`}>
      {config.label}
    </Badge>
  );
};

// Payment status badge component
const PaymentStatusBadge = ({ status }: { status: PaymentStatus }) => {
  const statusConfig = {
    pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
    paid: { color: 'bg-green-100 text-green-800', label: 'Paid' },
    failed: { color: 'bg-red-100 text-red-800', label: 'Failed' },
    refunded: { color: 'bg-gray-100 text-gray-800', label: 'Refunded' },
  };

  const config = statusConfig[status] || statusConfig.pending;

  return (
    <Badge variant="outline" className={`${config.color} border-0`}>
      {config.label}
    </Badge>
  );
};

// Order note component
const OrderNoteItem = ({ note }: { note: OrderNote }) => {
  return (
    <div className="border rounded-md p-4 mb-4">
      <div className="flex justify-between items-start mb-2">
        <div>
          <p className="font-medium">{note.userName}</p>
          <p className="text-sm text-muted-foreground">
            {formatDate(note.createdAt)}
          </p>
        </div>
        {note.isInternal && (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-0">
            Internal
          </Badge>
        )}
      </div>
      <p className="mt-2 whitespace-pre-wrap">{note.content}</p>
    </div>
  );
};

interface OrderDetailsProps {
  order: Order;
  initialNotes?: OrderNote[];
}

export function OrderDetails({ order, initialNotes = [] }: OrderDetailsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [notes, setNotes] = useState<OrderNote[]>(initialNotes);
  const [newNote, setNewNote] = useState('');
  const [isInternal, setIsInternal] = useState(true);
  const [trackingNumber, setTrackingNumber] = useState(order.trackingNumber ?? '');
  const [estimatedDelivery, setEstimatedDelivery] = useState(
    order.estimatedDelivery
      ? format(new Date(order.estimatedDelivery), 'yyyy-MM-dd')
      : ''
  );
  const [status, setStatus] = useState<OrderStatus>(order.status);
  const [showItems, setShowItems] = useState(true);

  // Handle status change
  const handleStatusChange = async (newStatus: OrderStatus) => {
    setIsLoading(true);
    try {
      await updateOrder(order.id, { status: newStatus });
      setStatus(newStatus);
      toast.success('Order status updated successfully');
      router.refresh();
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Failed to update order status');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle tracking info update
  const handleTrackingUpdate = async () => {
    setIsLoading(true);
    try {
      const updateData: Partial<Order> = {};
      
      if (trackingNumber) {
        updateData.trackingNumber = trackingNumber;
      }
      
      if (estimatedDelivery) {
        updateData.estimatedDelivery = new Date(estimatedDelivery);
      }
      
      if (Object.keys(updateData).length > 0) {
        await updateOrder(order.id, updateData);
        toast.success('Tracking information updated successfully');
        router.refresh();
      }
    } catch (error) {
      console.error('Error updating tracking information:', error);
      toast.error('Failed to update tracking information');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle adding a note
  const handleAddNote = async () => {
    if (!newNote.trim()) return;
    
    setIsLoading(true);
    try {
      const noteId = await addOrderNote(order.id, newNote, isInternal);
      
      // Refresh notes
      const updatedNotes = await getOrderNotes(order.id);
      setNotes(updatedNotes);
      
      // Clear form
      setNewNote('');
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle generating invoice
  const handleGenerateInvoice = async () => {
    try {
      const response = await fetch(`/api/admin/orders/${order.id}/invoice`, {
        method: 'GET',
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate invoice');
      }
      
      // Get the PDF as a blob
      const blob = await response.blob();
      
      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a link and click it to download the file
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${order.id}.pdf`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Invoice generated successfully');
    } catch (error) {
      console.error('Error generating invoice:', error);
      toast.error('Failed to generate invoice');
    }
  };

  // Handle exporting order to CSV
  const handleExportCSV = async () => {
    try {
      const response = await fetch(`/api/admin/orders/${order.id}/export?format=csv`, {
        method: 'GET',
      });
      
      if (!response.ok) {
        throw new Error('Failed to export order');
      }
      
      // Get the CSV as text
      const text = await response.text();
      
      // Create a blob with the CSV data
      const blob = new Blob([text], { type: 'text/csv' });
      
      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a link and click it to download the file
      const a = document.createElement('a');
      a.href = url;
      a.download = `order-${order.id}.csv`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Order exported successfully');
    } catch (error) {
      console.error('Error exporting order:', error);
      toast.error('Failed to export order');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Order #{order.id}</h1>
          <p className="text-muted-foreground">
            Placed on {formatDate(order.createdAt)}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportCSV}
            disabled={isLoading}
          >
            <FileText className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateInvoice}
            disabled={isLoading}
          >
            <Download className="mr-2 h-4 w-4" />
            Download Invoice
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.print()}
            disabled={isLoading}
          >
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Order Summary */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
            <div className="flex items-center gap-2">
              <StatusBadge status={status} />
              <Select value={status} onValueChange={(value: OrderStatus) => handleStatusChange(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Change status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="pending_payment">Pending Payment</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between mb-4">
              <div>
                <p className="text-sm font-medium">Payment Status</p>
                <PaymentStatusBadge status={order.paymentStatus ?? 'pending'} />
              </div>
              <div>
                <p className="text-sm font-medium">Payment Method</p>
                <p>{order.paymentMethod}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Total</p>
                <p className="font-bold">{formatCurrency(order.total)}</p>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Order Items</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowItems(!showItems)}
                >
                  {showItems ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {showItems && (
                <div className="mt-2 space-y-4">
                  {order.items.map((item, index) => (
                    <div key={`${item.productId}-${item.variantId ?? index}`} className="flex justify-between border-b pb-2">
                      <div className="flex-1">
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {item.variantName ?? 'Standard'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p>
                          {item.quantity} × {formatCurrency(item.price)}
                        </p>
                        <p className="font-medium">
                          {formatCurrency(item.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div className="pt-2 space-y-1">
                    <div className="flex justify-between">
                      <p>Subtotal</p>
                      <p>{formatCurrency(order.subtotal)}</p>
                    </div>
                    {order.shipping && (
                      <div className="flex justify-between">
                        <p>Shipping</p>
                        <p>{formatCurrency(order.shipping)}</p>
                      </div>
                    )}
                    {order.tax && (
                      <div className="flex justify-between">
                        <p>Tax</p>
                        <p>{formatCurrency(order.tax)}</p>
                      </div>
                    )}
                    {order.discount && (
                      <div className="flex justify-between">
                        <p>Discount</p>
                        <p>-{formatCurrency(order.discount)}</p>
                      </div>
                    )}
                    <Separator className="my-2" />
                    <div className="flex justify-between font-bold">
                      <p>Total</p>
                      <p>{formatCurrency(order.total)}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-1">Contact Information</h3>
              <p>
                {order.shippingAddress.firstName} {order.shippingAddress.lastName}
              </p>
              <p>{order.shippingAddress.email}</p>
              <p>{order.shippingAddress.phone}</p>
            </div>
            <div>
              <h3 className="font-medium mb-1">Shipping Address</h3>
              <p>{order.shippingAddress.streetAddress}</p>
              {order.shippingAddress.apartment && (
                <p>{order.shippingAddress.apartment}</p>
              )}
              <p>
                {order.shippingAddress.city}, {order.shippingAddress.state}{' '}
                {order.shippingAddress.postalCode}
              </p>
              <p>{order.shippingAddress.country}</p>
            </div>
            {order.billingAddress && (
              <div>
                <h3 className="font-medium mb-1">Billing Address</h3>
                <p>{order.billingAddress.streetAddress}</p>
                {order.billingAddress.apartment && (
                  <p>{order.billingAddress.apartment}</p>
                )}
                <p>
                  {order.billingAddress.city}, {order.billingAddress.state}{' '}
                  {order.billingAddress.postalCode}
                </p>
                <p>{order.billingAddress.country}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Fulfillment and Notes */}
      <Tabs defaultValue="fulfillment">
        <TabsList>
          <TabsTrigger value="fulfillment">Fulfillment</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>
        
        {/* Fulfillment Tab */}
        <TabsContent value="fulfillment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
              <CardDescription>
                Update tracking information for this order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tracking">Tracking Number</Label>
                  <Input
                    id="tracking"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    placeholder="Enter tracking number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="delivery-date">Estimated Delivery</Label>
                  <Input
                    id="delivery-date"
                    type="date"
                    value={estimatedDelivery}
                    onChange={(e) => setEstimatedDelivery(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleTrackingUpdate}
                disabled={isLoading}
              >
                <Truck className="mr-2 h-4 w-4" />
                Update Shipping Info
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Notes Tab */}
        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Notes</CardTitle>
              <CardDescription>
                Add notes to this order for internal reference or customer communication
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="note">Add a Note</Label>
                <Textarea
                  id="note"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="Enter note content..."
                  className="min-h-32"
                />
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="internal"
                    checked={isInternal}
                    onChange={(e) => setIsInternal(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="internal">Internal note (not visible to customer)</Label>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => {
                  setIsInternal(false);
                  handleAddNote();
                }}
                disabled={isLoading || !newNote.trim()}
              >
                <Send className="mr-2 h-4 w-4" />
                Send to Customer
              </Button>
              <Button
                onClick={handleAddNote}
                disabled={isLoading || !newNote.trim()}
              >
                Add Note
              </Button>
            </CardFooter>
          </Card>
          
          {notes.length > 0 ? (
            <div className="space-y-4">
              <h3 className="font-medium">Previous Notes</h3>
              {notes.map((note) => (
                <OrderNoteItem key={note.id} note={note} />
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-4">
              No notes have been added to this order yet.
            </p>
          )}
        </TabsContent>
        
        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
              <CardDescription>
                Timeline of events for this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              {order.statusHistory && order.statusHistory.length > 0 ? (
                <div className="space-y-4">
                  {order.statusHistory.map((event, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="w-2 h-2 mt-2 rounded-full bg-primary" />
                      <div>
                        <p className="font-medium">
                          Status changed to <StatusBadge status={event.status} />
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(event.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-4">
                  No history available for this order.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
