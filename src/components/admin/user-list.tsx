'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import {
  Check,
  ChevronDown,
  Edit,
  Eye,
  Lock,
  MoreHorizontal,
  Search,
  Shield,
  Unlock,
  User as UserIcon,
  X,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';

import { User, UserFilterOptions, getUsers } from '@/lib/firebase/services/user-service';
import { UserRole } from '@/lib/firebase/admin-services/role-service';

// Role badge component
const RoleBadge = ({ role }: { role: UserRole }) => {
  const roleConfig = {
    admin: { color: 'bg-red-100 text-red-800', label: 'Admin' },
    editor: { color: 'bg-blue-100 text-blue-800', label: 'Editor' },
    viewer: { color: 'bg-green-100 text-green-800', label: 'Viewer' },
    customer: { color: 'bg-gray-100 text-gray-800', label: 'Customer' },
  };

  const config = roleConfig[role] || roleConfig.customer;

  return (
    <Badge variant="outline" className={`${config.color} border-0`}>
      {config.label}
    </Badge>
  );
};

// Status badge component
const StatusBadge = ({ isDisabled }: { isDisabled: boolean }) => {
  return isDisabled ? (
    <Badge variant="outline" className="bg-red-100 text-red-800 border-0">
      Disabled
    </Badge>
  ) : (
    <Badge variant="outline" className="bg-green-100 text-green-800 border-0">
      Active
    </Badge>
  );
};

// User avatar component
const UserAvatar = ({ user }: { user: User }) => {
  const initials = user.displayName
    ? user.displayName
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
    : user.email.substring(0, 2).toUpperCase();

  return (
    <Avatar>
      <AvatarImage src={user.photoURL || ''} alt={user.displayName || user.email} />
      <AvatarFallback>{initials}</AvatarFallback>
    </Avatar>
  );
};

interface UserListProps {
  initialUsers?: User[];
  onViewUser?: (user: User) => void;
  onEditUser?: (user: User) => void;
  onUpdateRole?: (user: User, role: UserRole) => void;
  onToggleStatus?: (user: User) => void;
}

export function UserList({
  initialUsers = [],
  onViewUser,
  onEditUser,
  onUpdateRole,
  onToggleStatus,
}: UserListProps) {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [isLoading, setIsLoading] = useState(initialUsers.length === 0);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'active' | 'disabled' | 'all'>('all');
  const [hasMore, setHasMore] = useState(false);
  const [cursor, setCursor] = useState<any>(null);

  // Load users
  const loadUsers = async (options: UserFilterOptions = {}) => {
    setIsLoading(true);
    try {
      const result = await getUsers({
        ...options,
        search: searchQuery,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        isDisabled: statusFilter === 'disabled' ? true : statusFilter === 'active' ? false : undefined,
        limit: 10,
      });
      
      setUsers(result.users);
      setHasMore(result.hasMore);
      setCursor(result.cursor);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  // Load more users
  const loadMoreUsers = async () => {
    if (!cursor || !hasMore) return;
    
    setIsLoading(true);
    try {
      const result = await getUsers({
        search: searchQuery,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        isDisabled: statusFilter === 'disabled' ? true : statusFilter === 'active' ? false : undefined,
        startAfter: cursor,
        limit: 10,
      });
      
      setUsers([...users, ...result.users]);
      setHasMore(result.hasMore);
      setCursor(result.cursor);
    } catch (error) {
      console.error('Error loading more users:', error);
      toast.error('Failed to load more users');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    loadUsers();
  };

  // Handle role filter change
  const handleRoleFilterChange = (value: string) => {
    setRoleFilter(value as UserRole | 'all');
    loadUsers({
      role: value !== 'all' ? (value as UserRole) : undefined,
      isDisabled: statusFilter === 'disabled' ? true : statusFilter === 'active' ? false : undefined,
    });
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value as 'active' | 'disabled' | 'all');
    loadUsers({
      role: roleFilter !== 'all' ? roleFilter : undefined,
      isDisabled: value === 'disabled' ? true : value === 'active' ? false : undefined,
    });
  };

  // Handle view user
  const handleViewUser = (user: User) => {
    if (onViewUser) {
      onViewUser(user);
    } else {
      router.push(`/admin/users/${user.uid}`);
    }
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    if (onEditUser) {
      onEditUser(user);
    } else {
      router.push(`/admin/users/${user.uid}/edit`);
    }
  };

  // Handle update role
  const handleUpdateRole = (user: User, role: UserRole) => {
    if (onUpdateRole) {
      onUpdateRole(user, role);
    } else {
      toast.info('Role update functionality not implemented');
    }
  };

  // Handle toggle status
  const handleToggleStatus = (user: User) => {
    if (onToggleStatus) {
      onToggleStatus(user);
    } else {
      toast.info('Status toggle functionality not implemented');
    }
  };

  // Load users on mount
  useEffect(() => {
    if (initialUsers.length === 0) {
      loadUsers();
    }
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-end sm:items-center justify-between">
        <div className="flex flex-1 gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <Button variant="outline" onClick={handleSearch}>
            Search
          </Button>
        </div>
        <div className="flex gap-2">
          <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="editor">Editor</SelectItem>
              <SelectItem value="viewer">Viewer</SelectItem>
              <SelectItem value="customer">Customer</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="disabled">Disabled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading && users.length === 0 ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`loading-${index}`}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-8 w-8 rounded-md ml-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : users.length === 0 ? (
              // Empty state
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No users found.
                </TableCell>
              </TableRow>
            ) : (
              // Users list
              users.map((user) => (
                <TableRow key={user.uid}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <UserAvatar user={user} />
                      <div>
                        <div className="font-medium">
                          {user.displayName || 'No Name'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <RoleBadge role={user.role} />
                  </TableCell>
                  <TableCell>
                    <StatusBadge isDisabled={user.isDisabled} />
                  </TableCell>
                  <TableCell>
                    {format(user.createdAt, 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell>
                    {user.lastLoginAt
                      ? format(user.lastLoginAt, 'MMM d, yyyy')
                      : 'Never'}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewUser(user)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditUser(user)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit User
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => handleUpdateRole(user, 'admin')}
                          disabled={user.role === 'admin'}
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          Make Admin
                          {user.role === 'admin' && (
                            <Check className="ml-auto h-4 w-4" />
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleUpdateRole(user, 'editor')}
                          disabled={user.role === 'editor'}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Make Editor
                          {user.role === 'editor' && (
                            <Check className="ml-auto h-4 w-4" />
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleUpdateRole(user, 'viewer')}
                          disabled={user.role === 'viewer'}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Make Viewer
                          {user.role === 'viewer' && (
                            <Check className="ml-auto h-4 w-4" />
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleUpdateRole(user, 'customer')}
                          disabled={user.role === 'customer'}
                        >
                          <UserIcon className="mr-2 h-4 w-4" />
                          Make Customer
                          {user.role === 'customer' && (
                            <Check className="ml-auto h-4 w-4" />
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleToggleStatus(user)}
                          className={user.isDisabled ? 'text-green-600' : 'text-red-600'}
                        >
                          {user.isDisabled ? (
                            <>
                              <Unlock className="mr-2 h-4 w-4" />
                              Enable User
                            </>
                          ) : (
                            <>
                              <Lock className="mr-2 h-4 w-4" />
                              Disable User
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={loadMoreUsers}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}
