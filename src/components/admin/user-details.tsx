'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { toast } from 'sonner';
import {
  Clock,
  Edit,
  Lock,
  Mail,
  Shield,
  Unlock,
  User as UserIcon,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';

import { User, UserActivity, getUserActivities } from '@/lib/firebase/services/user-service';
import { UserRole } from '@/lib/firebase/admin-services/role-service';
import { UserPermissions } from './user-permissions';

// Role badge component
const RoleBadge = ({ role }: { role: UserRole }) => {
  const roleConfig = {
    admin: { color: 'bg-red-100 text-red-800', label: 'Admin' },
    editor: { color: 'bg-blue-100 text-blue-800', label: 'Editor' },
    viewer: { color: 'bg-green-100 text-green-800', label: 'Viewer' },
    customer: { color: 'bg-gray-100 text-gray-800', label: 'Customer' },
  };

  const config = roleConfig[role] || roleConfig.customer;

  return (
    <Badge variant="outline" className={`${config.color} border-0`}>
      {config.label}
    </Badge>
  );
};

// Status badge component
const StatusBadge = ({ isDisabled }: { isDisabled: boolean }) => {
  return isDisabled ? (
    <Badge variant="outline" className="bg-red-100 text-red-800 border-0">
      Disabled
    </Badge>
  ) : (
    <Badge variant="outline" className="bg-green-100 text-green-800 border-0">
      Active
    </Badge>
  );
};

interface UserDetailsProps {
  user: User;
  activities?: UserActivity[];
  onUpdateRole?: (role: UserRole) => void;
  onToggleStatus?: () => void;
  onEdit?: () => void;
}

export function UserDetails({
  user,
  activities: initialActivities = [],
  onUpdateRole,
  onToggleStatus,
  onEdit,
}: UserDetailsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activities, setActivities] = useState<UserActivity[]>(initialActivities);
  const [activeTab, setActiveTab] = useState('profile');

  // Handle role change
  const handleRoleChange = async (role: UserRole) => {
    if (role === user.role) return;

    setIsLoading(true);
    try {
      if (onUpdateRole) {
        await onUpdateRole(role);
        toast.success(`User role updated to ${role}`);
      } else {
        toast.info('Role update functionality not implemented');
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle toggle status
  const handleToggleStatus = async () => {
    setIsLoading(true);
    try {
      if (onToggleStatus) {
        await onToggleStatus();
        toast.success(user.isDisabled ? 'User enabled successfully' : 'User disabled successfully');
      } else {
        toast.info('Status toggle functionality not implemented');
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      toast.error('Failed to update user status');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit
  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      router.push(`/admin/users/${user.uid}/edit`);
    }
  };

  // Load user activities
  const loadActivities = async () => {
    if (activities.length > 0 || activeTab !== 'activity') return;

    setIsLoading(true);
    try {
      const userActivities = await getUserActivities(user.uid);
      setActivities(userActivities);
    } catch (error) {
      console.error('Error loading user activities:', error);
      toast.error('Failed to load user activities');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === 'activity') {
      loadActivities();
    }
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return format(date, 'MMM d, yyyy h:mm a');
  };

  // Get user initials
  const getUserInitials = () => {
    if (user.displayName) {
      return user.displayName
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase();
    }
    return user.email.substring(0, 2).toUpperCase();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">User Details</h1>
          <p className="text-muted-foreground">
            View and manage user information
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleEdit}
            disabled={isLoading}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit User
          </Button>
          <Button
            variant={user.isDisabled ? 'default' : 'destructive'}
            onClick={handleToggleStatus}
            disabled={isLoading}
          >
            {user.isDisabled ? (
              <>
                <Unlock className="mr-2 h-4 w-4" />
                Enable User
              </>
            ) : (
              <>
                <Lock className="mr-2 h-4 w-4" />
                Disable User
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.photoURL || ''} alt={user.displayName || user.email} />
                <AvatarFallback className="text-2xl">{getUserInitials()}</AvatarFallback>
              </Avatar>
            </div>
            <CardTitle>{user.displayName || 'No Name'}</CardTitle>
            <CardDescription>{user.email}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Role:</span>
              <RoleBadge role={user.role} />
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Status:</span>
              <StatusBadge isDisabled={user.isDisabled} />
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Created:</span>
              <span>{format(user.createdAt, 'MMM d, yyyy')}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Last Login:</span>
              <span>{user.lastLoginAt ? format(user.lastLoginAt, 'MMM d, yyyy') : 'Never'}</span>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full space-y-2">
              <label className="text-sm font-medium">Change Role</label>
              <Select
                value={user.role}
                onValueChange={(value) => handleRoleChange(value as UserRole)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="editor">Editor</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="customer">Customer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardFooter>
        </Card>

        {/* User Details Tabs */}
        <div className="md:col-span-2">
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="activity">Activity Log</TabsTrigger>
            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Display Name</h3>
                      <p>{user.displayName || 'Not set'}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Email</h3>
                      <p>{user.email}</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Account Created</h3>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <p>{formatDate(user.createdAt)}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Login</h3>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <p>{user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}</p>
                    </div>
                  </div>

                  {user.metadata && Object.keys(user.metadata).length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">Additional Information</h3>
                        <div className="space-y-2">
                          {Object.entries(user.metadata).map(([key, value]) => (
                            <div key={key} className="grid grid-cols-2">
                              <span className="font-medium">{key}</span>
                              <span>{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Role</CardTitle>
                  <CardDescription>
                    User role information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Current Role</h3>
                      <div className="flex items-center">
                        {user.role === 'admin' && <Shield className="mr-2 h-4 w-4 text-red-500" />}
                        {user.role === 'editor' && <Edit className="mr-2 h-4 w-4 text-blue-500" />}
                        {user.role === 'viewer' && <UserIcon className="mr-2 h-4 w-4 text-green-500" />}
                        {user.role === 'customer' && <UserIcon className="mr-2 h-4 w-4 text-gray-500" />}
                        <RoleBadge role={user.role} />
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Role Description</h3>
                      <div className="space-y-2">
                        {user.role === 'admin' && (
                          <p>Administrators have full access to all features and can manage users, products, orders, and settings.</p>
                        )}
                        {user.role === 'editor' && (
                          <p>Editors can manage products, categories, and orders, but cannot manage users or system settings.</p>
                        )}
                        {user.role === 'viewer' && (
                          <p>Viewers have read-only access to products, categories, and orders, but cannot make changes.</p>
                        )}
                        {user.role === 'customer' && (
                          <p>Customers have access only to their own orders and account information.</p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>


            </TabsContent>

            {/* Permissions Tab */}
            <TabsContent value="permissions">
              <UserPermissions userId={user.uid} userEmail={user.email} />
            </TabsContent>

            {/* Activity Tab */}
            <TabsContent value="activity">
              <Card>
                <CardHeader>
                  <CardTitle>Activity Log</CardTitle>
                  <CardDescription>
                    Recent user activity
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading && activities.length === 0 ? (
                    <div className="text-center py-4">Loading activity log...</div>
                  ) : activities.length === 0 ? (
                    <div className="text-center py-4">No activity recorded for this user.</div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Action</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Details</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {activities.map((activity) => (
                          <TableRow key={activity.id}>
                            <TableCell className="font-medium">{activity.action}</TableCell>
                            <TableCell>{formatDate(activity.timestamp)}</TableCell>
                            <TableCell>
                              {activity.details ? (
                                typeof activity.details === 'object' ? (
                                  <pre className="text-xs whitespace-pre-wrap">
                                    {JSON.stringify(activity.details, null, 2)}
                                  </pre>
                                ) : (
                                  String(activity.details)
                                )
                              ) : (
                                'No details'
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
