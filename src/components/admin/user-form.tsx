'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageUpload } from '@/components/ui/image-upload';
import { updateUserProfile, User } from '@/lib/firebase/services/user-service';
import { UserRole } from '@/lib/firebase/admin-services/role-service';

// Form schema
const userFormSchema = z.object({
  displayName: z.string().min(1, 'Display name is required'),
  photoURL: z.string().url().optional().or(z.literal('')),
  role: z.enum(['admin', 'editor', 'viewer', 'customer']),
  isDisabled: z.boolean(),
});

interface UserFormValues {
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'editor' | 'viewer' | 'customer';
  isDisabled: boolean;
}

interface UserFormProps {
  user: User;
  onUpdateRole?: (role: UserRole) => Promise<void>;
  onToggleStatus?: () => Promise<void>;
}

export function UserForm({ user, onUpdateRole, onToggleStatus }: UserFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with user data
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      displayName: user.displayName ?? '',
      photoURL: user.photoURL ?? '',
      role: user.role,
      isDisabled: user.isDisabled ?? false,
    },
  });

  // Handle form submission
  const onSubmit = async (data: UserFormValues) => {
    setIsLoading(true);
    try {
      // Update profile (display name and photo URL)
      await updateUserProfile(user.uid, {
        displayName: data.displayName,
        photoURL: data.photoURL ?? '',
      });

      // Update role if changed and handler provided
      if (data.role !== user.role && onUpdateRole) {
        await onUpdateRole(data.role);
      }

      // Update status if changed and handler provided
      if (data.isDisabled !== user.isDisabled && onToggleStatus) {
        await onToggleStatus();
      }

      toast.success('User updated successfully');
      router.refresh();
      router.push(`/admin/users/${user.uid}`);
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Edit User</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="displayName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Display name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="viewer">Viewer</SelectItem>
                          <SelectItem value="customer">Customer</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The user's role determines their permissions
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isDisabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Account Status</FormLabel>
                        <FormDescription>
                          Disabled accounts cannot log in
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="photoURL"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Profile Photo</FormLabel>
                      <FormControl>
                        <ImageUpload
                          value={field.value ?? ''}
                          onChange={field.onChange}
                          path="users"
                        />
                      </FormControl>
                      <FormDescription>
                        Upload a profile photo for this user
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
