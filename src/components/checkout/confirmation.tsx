'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import Link from 'next/link';

export function Confirmation() {
  return (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <CheckCircle className="w-16 h-16 text-primary" />
      </div>
      <h2 className="text-2xl font-semibold">Thank you for your order!</h2>
      <p className="text-muted-foreground">
        We&apos;ll send you a confirmation email with your order details.
      </p>
      <Link href="/products">
        <Button>Continue Shopping</Button>
      </Link>
    </div>
  );
}