'use client';

import { useCartStore } from '@/store/cart';
import Image from 'next/image';

export function CheckoutSummary() {
  const { items, total } = useCartStore();
  const shipping = 5.99; // This should be dynamic based on selected shipping method

  return (
    <div className="bg-muted p-6 rounded-lg space-y-6">
      <h2 className="text-xl font-semibold">Order Summary</h2>
      
      <div className="space-y-4">
        {items.map((item) => (
          <div key={item.id} className="flex gap-4">
            <div className="relative w-16 h-16">
              <Image
                src={item.image}
                alt={item.name}
                fill
                className="object-cover rounded"
              />
              <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {item.quantity}
              </div>
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{item.name}</h3>
              {item.variantName && (
                <p className="text-sm text-muted-foreground">
                  Size: {item.variantName}
                </p>
              )}
            </div>
            <div className="font-medium">
              ${(item.price * item.quantity).toFixed(2)}
            </div>
          </div>
        ))}
      </div>

      <div className="border-t pt-4 space-y-2">
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>${total.toFixed(2)}</span>
        </div>
        <div className="flex justify-between">
          <span>Shipping</span>
          <span>${shipping.toFixed(2)}</span>
        </div>
        <div className="flex justify-between font-semibold text-lg border-t pt-2">
          <span>Total</span>
          <span>${(total + shipping).toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}