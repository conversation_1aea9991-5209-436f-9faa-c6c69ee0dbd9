'use client';

import { useCheckoutStore } from '@/store/checkout';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

const steps = [
  { id: 'information', label: 'Information' },
  { id: 'shipping', label: 'Shipping' },
  { id: 'payment', label: 'Payment' },
] as const;

export function CheckoutSteps() {
  const currentStep = useCheckoutStore((state) => state.step);

  return (
    <div className="flex items-center justify-between mb-8">
      {steps.map((step, index) => {
        const isActive = currentStep === step.id;
        const isCompleted = steps.findIndex((s) => s.id === currentStep) > index;

        return (
          <div
            key={step.id}
            className="flex items-center"
          >
            <div className="flex items-center">
              <div
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center',
                  isCompleted ? 'bg-primary text-primary-foreground' : 
                  isActive ? 'bg-primary text-primary-foreground' : 
                  'bg-muted text-muted-foreground'
                )}
              >
                {isCompleted ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              <span className="ml-2 font-medium">{step.label}</span>
            </div>
            {index < steps.length - 1 && (
              <div className="w-24 h-px bg-muted mx-4" />
            )}
          </div>
        );
      })}
    </div>
  );
}