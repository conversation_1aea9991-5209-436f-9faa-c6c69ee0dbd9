'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useCheckoutStore } from '@/store/checkout';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { ShippingMethod } from '@/types/checkout';
import { toast } from 'sonner';

export function Shipping() {
  const { setStep, setShippingMethod, shippingMethod } = useCheckoutStore();
  const [shippingMethods, setShippingMethods] = useState<ShippingMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMethodId, setSelectedMethodId] = useState<string>(shippingMethod?.id ?? '');

  // Fetch shipping methods from the database
  useEffect(() => {
    const fetchShippingMethods = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/shipping/methods');

        if (!response.ok) {
          throw new Error('Failed to fetch shipping methods');
        }

        const data = await response.json();
        setShippingMethods(data.methods);

        // Set default selection if we have methods and none is selected
        if (data.methods.length > 0 && !selectedMethodId) {
          setSelectedMethodId(data.methods[0].id);
        }
      } catch (error) {
        console.error('Error fetching shipping methods:', error);
        toast.error('Failed to load shipping methods. Using default options.');

        // Fallback to default shipping methods
        const defaultMethods = [
          {
            id: 'standard',
            name: 'Standard Shipping',
            price: 5.99,
            description: '5-7 business days',
          },
          {
            id: 'express',
            name: 'Express Shipping',
            price: 15.99,
            description: '2-3 business days',
          },
        ];

        setShippingMethods(defaultMethods);
        if (!selectedMethodId) {
          setSelectedMethodId(defaultMethods[0].id);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchShippingMethods();
  }, [selectedMethodId, shippingMethod]);

  const handleContinue = () => {
    // Find the selected shipping method
    const method = shippingMethods.find(m => m.id === selectedMethodId);
    if (method) {
      setShippingMethod(method);
      setStep('payment');
    } else {
      toast.error('Please select a shipping method');
    }
  };

  const handleShippingMethodChange = (value: string) => {
    setSelectedMethodId(value);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Shipping Method</h2>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <RadioGroup
          value={selectedMethodId}
          onValueChange={handleShippingMethodChange}
          className="space-y-4"
        >
          {shippingMethods.map((method) => (
            <div key={method.id} className="flex items-center space-x-3">
              <RadioGroupItem value={method.id} id={method.id} />
              <Label htmlFor={method.id} className="flex-1">
                <div className="flex justify-between">
                  <div>
                    <div className="font-medium">{method.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {method.description}
                    </div>
                  </div>
                  <div className="font-medium">${method.price.toFixed(2)}</div>
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>
      )}

      <div className="flex justify-between pt-6">
        <Button
          variant="outline"
          onClick={() => setStep('information')}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={loading || !selectedMethodId}
        >
          Continue to Payment
        </Button>
      </div>
    </div>
  );
}