'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCheckoutStore } from '@/store/checkout';

const schema = z.object({
  email: z.string().email(),
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  streetAddress: z.string().min(5),
  apartment: z.string().optional(),
  city: z.string().min(2),
  country: z.string().min(2),
  state: z.string().min(2),
  zipCode: z.string().min(4),
  phone: z.string().min(10),
});

export function Information() {
  const { setStep, setEmail, setShippingAddress } = useCheckoutStore();
  
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(schema),
  });

  const onSubmit = (data: z.infer<typeof schema>) => {
    const { email, ...address } = data;
    setEmail(email);
    setShippingAddress(address);
    setStep('shipping');
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-4">Contact Information</h2>
        <Input
          {...register('email')}
          placeholder="Email"
          className="w-full"
        />
        {errors.email && (
          <p className="text-destructive text-sm mt-1">{errors.email.message}</p>
        )}
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Shipping Address</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            {...register('firstName')}
            placeholder="First name"
          />
          <Input
            {...register('lastName')}
            placeholder="Last name"
          />
          <Input
            {...register('streetAddress')}
            placeholder="Street address"
            className="md:col-span-2"
          />
          <Input
            {...register('apartment')}
            placeholder="Apartment, suite, etc. (optional)"
            className="md:col-span-2"
          />
          <Input
            {...register('city')}
            placeholder="City"
          />
          <Input
            {...register('country')}
            placeholder="Country"
          />
          <Input
            {...register('state')}
            placeholder="State"
          />
          <Input
            {...register('zipCode')}
            placeholder="ZIP code"
          />
          <Input
            {...register('phone')}
            placeholder="Phone"
            className="md:col-span-2"
          />
        </div>
      </div>

      <Button type="submit" className="w-full">
        Continue to Shipping
      </Button>
    </form>
  );
}