'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useCheckoutStore } from '@/store/checkout';
import { useCartStore } from '@/store/cart';
import { toast } from 'sonner';
import { Loader2, Copy, CheckCircle, CreditCard, Building2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { PaymentInstructions, BankAccountDetails } from '@/types/checkout';

// Manual Payment Instructions Component
function ManualPaymentInstructions({ paymentInstructions }: { paymentInstructions: PaymentInstructions }) {
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const { setStep, clearCart } = useCheckoutStore();

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast.success(`${fieldName} copied to clipboard`);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleCompleteOrder = () => {
    clearCart();
    setStep('confirmation');
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Bank Transfer Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800 font-medium mb-2">
              Please transfer the exact amount to the following bank account:
            </p>
            <div className="text-2xl font-bold text-blue-900">
              {formatCurrency(paymentInstructions.amount, paymentInstructions.currency)}
            </div>
          </div>

          <div className="grid gap-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Bank Name</p>
                <p className="font-medium">{paymentInstructions.bankAccountDetails.bankName}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.bankName, 'Bank Name')}
              >
                {copiedField === 'Bank Name' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Account Name</p>
                <p className="font-medium">{paymentInstructions.bankAccountDetails.accountName}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.accountName, 'Account Name')}
              >
                {copiedField === 'Account Name' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Account Number</p>
                <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.accountNumber}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.accountNumber, 'Account Number')}
              >
                {copiedField === 'Account Number' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            {paymentInstructions.bankAccountDetails.routingNumber && (
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">Routing Number</p>
                  <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.routingNumber}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.routingNumber!, 'Routing Number')}
                >
                  {copiedField === 'Routing Number' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            )}

            {paymentInstructions.bankAccountDetails.swiftCode && (
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">SWIFT Code</p>
                  <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.swiftCode}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.swiftCode!, 'SWIFT Code')}
                >
                  {copiedField === 'SWIFT Code' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            )}
          </div>

          <Separator />

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm font-medium text-yellow-800 mb-2">
              Important: Order Reference
            </p>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-yellow-700">
                  Please include this reference in your transfer:
                </p>
                <p className="font-bold text-lg text-yellow-900 font-mono">
                  {paymentInstructions.orderReference}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.orderReference, 'Order Reference')}
              >
                {copiedField === 'Order Reference' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="text-sm text-gray-600 space-y-2">
            <p>{paymentInstructions.instructions}</p>
            {paymentInstructions.dueDate && (
              <p>
                <strong>Payment Due:</strong> {new Date(paymentInstructions.dueDate).toLocaleDateString()}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => setStep('shipping')}
        >
          Back
        </Button>
        <Button onClick={handleCompleteOrder}>
          Complete Order
        </Button>
      </div>
    </div>
  );
}

// Main Payment Component
export function Payment() {
  const [paymentInstructions, setPaymentInstructions] = useState<PaymentInstructions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const {
    shippingAddress,
    billingAddress,
    sameAsShipping,
    email,
    shippingMethod,
    setInternalOrderId,
    setPaymentInstructions: setCheckoutPaymentInstructions
  } = useCheckoutStore();
  const { items, total } = useCartStore();
  const { user, getIdToken } = useAuth();

  useEffect(() => {
    // Create manual payment order as soon as the page loads
    const createManualPayment = async () => {
      try {
        setIsLoading(true);
        setErrorMessage(null);

        // Prepare the request payload
        const payload = {
          items,
          shippingAddress,
          // Use shipping address as billing if sameAsShipping is true
          billingAddress: sameAsShipping ? shippingAddress : billingAddress,
          sameAsShipping,
          email,
          shippingMethod,
        };

        // Get authorization token if user is logged in
        let headers: HeadersInit = {
          'Content-Type': 'application/json',
        };

        const token = await getIdToken();
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Use manual payment API
        const response = await fetch('/api/payment/create-manual-payment', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error ?? 'Failed to create payment order');
        }

        const data = await response.json();

        // Store the payment instructions and order ID
        if (data.paymentInstructions) {
          setPaymentInstructions(data.paymentInstructions);
          setCheckoutPaymentInstructions(data.paymentInstructions);
        }

        if (data.internalOrderId) {
          setInternalOrderId(data.internalOrderId);
        }

      } catch (error) {
        console.error('Error creating manual payment:', error);
        setErrorMessage(error instanceof Error ? error.message : 'Failed to initialize payment. Please try again.');
        toast.error('Failed to initialize payment. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    if (shippingAddress && shippingMethod) {
      createManualPayment();
    }
  }, [items, shippingAddress, billingAddress, sameAsShipping, email, user, shippingMethod, setInternalOrderId, setCheckoutPaymentInstructions]);

  // Ensure we have a shipping method
  if (!shippingMethod) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Payment Method</h2>
        <div className="border rounded-lg p-4 text-center">
          <p className="text-muted-foreground">
            Please select a shipping method before proceeding to payment.
          </p>
          <Button
            onClick={() => useCheckoutStore.getState().setStep('shipping')}
            className="mt-4"
          >
            Go Back to Shipping
          </Button>
        </div>
      </div>
    );
  }

  // Determine what content to show based on loading and payment instructions state
  let content;
  if (isLoading) {
    content = (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-muted-foreground">Preparing your payment instructions...</p>
      </div>
    );
  } else if (errorMessage) {
    content = (
      <div className="border rounded-lg p-4 text-center">
        <p className="text-destructive mb-4">{errorMessage}</p>
        <Button
          onClick={() => {
            setErrorMessage(null);
            setIsLoading(true);
            // Trigger useEffect to retry
            window.location.reload();
          }}
          className="mt-4"
        >
          Retry
        </Button>
      </div>
    );
  } else if (paymentInstructions) {
    content = <ManualPaymentInstructions paymentInstructions={paymentInstructions} />;
  } else {
    content = (
      <div className="border rounded-lg p-4 text-center">
        <p className="text-muted-foreground">
          Unable to generate payment instructions. Please check your information and try again.
        </p>
        <Button
          onClick={() => {
            setIsLoading(true);
            window.location.reload();
          }}
          className="mt-4"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <CreditCard className="h-5 w-5" />
        <h2 className="text-xl font-semibold">Payment Instructions</h2>
      </div>
      {content}
    </div>
  );
}
