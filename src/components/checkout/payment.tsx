'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useCheckoutStore } from '@/store/checkout';
import { useCartStore } from '@/store/cart';
import { toast } from 'sonner';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? '');

// Stripe Payment Form Component
function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { setStep } = useCheckoutStore();
  const { items, total, clearCart } = useCartStore();
  const { user } = useAuth();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't loaded yet
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    // Confirm the payment
    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/checkout/success`,
      },
    });

    if (error) {
      // Show error to your customer
      setErrorMessage(error.message ?? 'An unexpected error occurred.');
      setIsLoading(false);
    } else {
      // Payment succeeded, handled by the return_url redirect
      clearCart();
      setStep('confirmation');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />

      {errorMessage && (
        <div className="text-destructive text-sm mt-2">{errorMessage}</div>
      )}

      <div className="flex justify-between pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => setStep('shipping')}
          disabled={isLoading}
        >
          Back
        </Button>
        <Button
          type="submit"
          disabled={!stripe || isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            'Pay Now'
          )}
        </Button>
      </div>
    </form>
  );
}

// Main Payment Component
export function Payment() {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const {
    shippingAddress,
    billingAddress,
    sameAsShipping,
    email,
    shippingMethod,
    setInternalOrderId,
    setPaymentIntentId
  } = useCheckoutStore();
  const { items, total } = useCartStore();
  const { user, getIdToken } = useAuth();

  useEffect(() => {
    // Create a PaymentIntent as soon as the page loads
    const createPaymentIntent = async () => {
      try {
        setIsLoading(true);

        // Prepare the request payload
        const payload = {
          items,
          shippingAddress,
          // Use shipping address as billing if sameAsShipping is true
          billingAddress: sameAsShipping ? shippingAddress : billingAddress,
          sameAsShipping,
          email,
          shippingMethod,
        };

        // Get authorization token if user is logged in
        let headers: HeadersInit = {
          'Content-Type': 'application/json',
        };

        const token = await getIdToken();
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Option 1: Use existing Payment Intent API (current)
        const response = await fetch('/api/payment/create-payment-intent', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload),
        });

        // Option 2: Use new Checkout Session API (recommended)
        // Uncomment below and comment above to switch to Checkout Sessions
        /*
        const response = await fetch('/api/payment/create-checkout-session', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload),
        });
        */

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error ?? 'Failed to create payment intent');
        }

        const data = await response.json();

        // Store the client secret for Stripe
        setClientSecret(data.clientSecret);

        // Store the order ID and payment intent ID in the checkout store
        if (data.internalOrderId) {
          setInternalOrderId(data.internalOrderId);
        }

        if (data.paymentIntentId) {
          setPaymentIntentId(data.paymentIntentId);
        }
      } catch (error) {
        console.error('Error creating payment intent:', error);
        toast.error('Failed to initialize payment. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    if (shippingAddress && shippingMethod) {
      createPaymentIntent();
    }
  }, [items, shippingAddress, billingAddress, sameAsShipping, email, user, shippingMethod, setInternalOrderId, setPaymentIntentId]);

  // Ensure we have a shipping method
  if (!shippingMethod) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Payment Method</h2>
        <div className="border rounded-lg p-4 text-center">
          <p className="text-muted-foreground">
            Please select a shipping method before proceeding to payment.
          </p>
          <Button
            onClick={() => useCheckoutStore.getState().setStep('shipping')}
            className="mt-4"
          >
            Go Back to Shipping
          </Button>
        </div>
      </div>
    );
  }

  const options: StripeElementsOptions = {
    clientSecret: clientSecret ?? undefined,
    appearance: {
      theme: 'stripe',
    },
  };

  // Determine what content to show based on loading and client secret state
  let content;
  if (isLoading) {
    content = (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  } else if (clientSecret) {
    content = (
      <div className="border rounded-lg p-6">
        {clientSecret && (
          <Elements stripe={stripePromise} options={{ clientSecret, appearance: options.appearance }}>
            <CheckoutForm />
          </Elements>
        )}
      </div>
    );
  } else {
    content = (
      <div className="border rounded-lg p-4 text-center">
        <p className="text-muted-foreground">
          Unable to initialize payment. Please check your shipping information and try again.
        </p>
        <Button
          onClick={() => setIsLoading(true)}
          className="mt-4"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Payment Method</h2>
      {content}
    </div>
  );
}
