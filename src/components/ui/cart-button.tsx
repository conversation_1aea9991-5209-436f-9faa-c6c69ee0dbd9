'use client';

import { ShoppingCart } from 'lucide-react';
import { Button } from './button';
import { useCartStore } from '@/store/cart';
import Link from 'next/link';

export function CartButton() {
  const items = useCartStore((state) => state.items);
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <Link href="/cart">
      <Button variant="outline" size="icon" className="relative">
        <ShoppingCart className="h-4 w-4" />
        {itemCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full w-5 h-5 text-xs flex items-center justify-center">
            {itemCount}
          </span>
        )}
      </Button>
    </Link>
  );
}