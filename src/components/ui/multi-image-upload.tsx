'use client';

import { useState, useRef, ChangeEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2, Upload, Loader2, MoveUp, MoveDown } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';
import { validateImageFile, uploadMultipleImages } from '@/lib/firebase/services/storage-service';

interface MultiImageUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  path?: string;
  className?: string;
  maxSizeMB?: number;
  maxImages?: number;
}

export function MultiImageUpload({
  value = [],
  onChange,
  path = 'products',
  className = '',
  maxSizeMB = 5,
  maxImages = 10
}: MultiImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the maximum
    if (value.length + files.length > maxImages) {
      toast.error(`You can only upload a maximum of ${maxImages} images`);
      return;
    }

    setIsUploading(true);
    const newUrls: string[] = [...value];
    const filesToUpload: File[] = [];

    try {
      // Validate files first
      for (const file of Array.from(files)) {
        const validation = validateImageFile(file, maxSizeMB);
        if (!validation.valid) {
          toast.error(`${file.name}: ${validation.error}`);
          continue;
        }
        filesToUpload.push(file);
      }

      if (filesToUpload.length > 0) {
        // Upload all valid files directly to Firebase Storage
        const uploadedUrls = await uploadMultipleImages(filesToUpload, path);

        // Add URLs to the array
        newUrls.push(...uploadedUrls);

        // Call onChange with the updated URLs
        onChange(newUrls);
        toast.success('Images uploaded successfully');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error('Failed to upload one or more images');
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemove = (index: number) => {
    const newUrls = [...value];
    newUrls.splice(index, 1);
    onChange(newUrls);
  };

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newUrls = [...value];
    const temp = newUrls[index];
    newUrls[index] = newUrls[index - 1];
    newUrls[index - 1] = temp;
    onChange(newUrls);
  };

  const handleMoveDown = (index: number) => {
    if (index === value.length - 1) return;
    const newUrls = [...value];
    const temp = newUrls[index];
    newUrls[index] = newUrls[index + 1];
    newUrls[index + 1] = temp;
    onChange(newUrls);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/jpeg,image/png,image/webp,image/gif"
        className="hidden"
        multiple
      />

      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
        {value.map((url, index) => (
          <div key={url} className="relative aspect-square overflow-hidden rounded-md border">
            <Image
              src={url}
              alt={`Product image ${index + 1}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 50vw, 25vw"
            />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity hover:bg-black/40 hover:opacity-100">
              <div className="flex gap-1">
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleRemove(index)}
                  disabled={isUploading}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
                {index > 0 && (
                  <Button
                    type="button"
                    variant="secondary"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleMoveUp(index)}
                    disabled={isUploading}
                  >
                    <MoveUp className="h-4 w-4" />
                  </Button>
                )}
                {index < value.length - 1 && (
                  <Button
                    type="button"
                    variant="secondary"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleMoveDown(index)}
                    disabled={isUploading}
                  >
                    <MoveDown className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            {index === 0 && (
              <div className="absolute left-2 top-2 rounded-md bg-primary px-2 py-1 text-xs font-medium text-primary-foreground">
                Main
              </div>
            )}
          </div>
        ))}

        {value.length < maxImages && (
          <Button
            type="button"
            variant="outline"
            className="flex aspect-square h-auto w-full flex-col items-center justify-center gap-2 rounded-md border border-dashed"
            onClick={handleClick}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="text-xs">Uploading...</span>
              </>
            ) : (
              <>
                <Upload className="h-6 w-6" />
                <span className="text-xs">Add Images</span>
              </>
            )}
          </Button>
        )}
      </div>
      <p className="text-xs text-muted-foreground">
        Upload up to {maxImages} images. JPEG, PNG, WebP or GIF. Max {maxSizeMB}MB each.
        The first image will be used as the main product image.
      </p>
    </div>
  );
}
