'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import {
  ArrowLeft,
  Loader2,
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Download,
  ShoppingCart
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { format } from 'date-fns';
import { Order, OrderStatus } from '@/types';

import { getOrderById } from '@/lib/firebase/services/order-service';
import { useAuth } from '@/contexts/auth-context';

// Real order service using Firestore
const useOrder = (orderId: string) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchOrder = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Get order from Firestore
        const orderData = await getOrderById(orderId);

        // Verify that the order belongs to the current user
        if (!orderData || orderData.userId !== user.uid) {
          setOrder(null);
          setLoading(false);
          return;
        }

        // Add default values for fields that might be missing
        const processedOrder: Order = {
          ...orderData,
          tax: orderData.tax || 0,
          shipping: orderData.shipping || orderData.shippingCost || 0,
          discount: orderData.discount || 0,
          paymentStatus: orderData.paymentStatus || 'pending',
          trackingNumber: orderData.trackingNumber || '',
          estimatedDelivery: orderData.estimatedDelivery ? new Date(orderData.estimatedDelivery) : undefined,
          notes: orderData.notes || '',
        };

        // Ensure all items have an image
        if (processedOrder.items) {
          processedOrder.items = processedOrder.items.map(item => ({
            ...item,
            image: item.image || '/images/placeholder.png'
          }));
        }

        setOrder(processedOrder);
      } catch (error: unknown) {
        console.error('Error fetching order:', error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load order details. Please try again.";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId, toast, user]);

  return { order, loading };
};

// Status icon mapping
const StatusIcon = ({ status }: { status: OrderStatus }) => {
  switch (status) {
    case 'pending':
      return <Clock className="h-6 w-6 text-yellow-500" />;
    case 'processing':
      return <Package className="h-6 w-6 text-blue-500" />;
    case 'shipped':
      return <Truck className="h-6 w-6 text-purple-500" />;
    case 'delivered':
      return <CheckCircle className="h-6 w-6 text-green-500" />;
    case 'cancelled':
      return <XCircle className="h-6 w-6 text-red-500" />;
    default:
      return <AlertCircle className="h-6 w-6 text-gray-500" />;
  }
};

// Status text mapping
const getStatusText = (status: OrderStatus) => {
  switch (status) {
    case 'pending':
      return 'Your order has been received and is awaiting processing.';
    case 'processing':
      return 'Your order is being prepared for shipping.';
    case 'shipped':
      return 'Your order has been shipped and is on its way to you.';
    case 'delivered':
      return 'Your order has been delivered. Enjoy your purchase!';
    case 'cancelled':
      return 'Your order has been cancelled.';
    default:
      return 'Order status unknown.';
  }
};

// Helper function to get status badge color
const getStatusBadgeStyle = (status: OrderStatus): string => {
  switch (status) {
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'shipped':
      return 'bg-purple-100 text-purple-800';
    case 'processing':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
    default:
      return 'bg-red-100 text-red-800';
  }
};

// Helper function to capitalize first letter
const capitalizeFirstLetter = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

// Loading component
const LoadingState = () => (
  <div className="flex justify-center items-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

// Order not found component
const OrderNotFound = () => (
  <div className="text-center py-16 bg-muted/30 rounded-lg">
    <AlertCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
    <h2 className="text-2xl font-semibold mb-2">Order not found</h2>
    <p className="text-muted-foreground mb-6">
      We couldn&apos;t find the order you&apos;re looking for.
    </p>
    <Button asChild>
      <Link href="/account/orders">Back to Orders</Link>
    </Button>
  </div>
);

// Order summary component
const OrderSummary = ({ order, onDownloadInvoice }: { order: Order; onDownloadInvoice: () => void }) => (
  <Card>
    <CardHeader className="pb-3">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <CardTitle className="text-xl">Order #{order.id.slice(0, 8)}</CardTitle>
          <p className="text-sm text-muted-foreground">
            Placed on {format(order.createdAt, 'PPP')}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onDownloadInvoice}>
            <Download className="mr-2 h-4 w-4" />
            Invoice
          </Button>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusBadgeStyle(order.status)}`}>
            {capitalizeFirstLetter(order.status)}
          </div>
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <div className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
        <StatusIcon status={order.status} />
        <div>
          <h3 className="font-medium">
            {capitalizeFirstLetter(order.status)}
          </h3>
          <p className="text-sm text-muted-foreground">
            {getStatusText(order.status)}
          </p>
          {order.trackingNumber && order.status === 'shipped' && (
            <p className="text-sm mt-1">
              Tracking Number: <span className="font-medium">{order.trackingNumber}</span>
            </p>
          )}
          {order.estimatedDelivery && (order.status === 'shipped' || order.status === 'processing') && (
            <p className="text-sm mt-1">
              Estimated Delivery: <span className="font-medium">{format(order.estimatedDelivery, 'PPP')}</span>
            </p>
          )}
        </div>
      </div>
    </CardContent>
  </Card>
);

// Order items component
const OrderItems = ({ order }: { order: Order }) => (
  <Card>
    <CardHeader>
      <CardTitle>Order Items</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-6">
        {order.items.map((item) => (
          <div key={`${item.productId}-${item.variantId || ''}`} className="flex gap-4">
            <div className="relative h-20 w-20 rounded-md overflow-hidden bg-muted flex-shrink-0">
              <Image
                src={item.image}
                alt={item.name}
                fill
                className="object-cover"
                unoptimized
              />
            </div>

            <div className="flex-1">
              <div className="flex justify-between">
                <div>
                  <Link href={`/products/${item.slug || item.productId}`} className="font-medium hover:text-primary transition-colors">
                    {item.name}
                  </Link>
                  {item.variantName && (
                    <p className="text-sm text-muted-foreground">
                      {item.variantName}
                    </p>
                  )}
                  <p className="text-sm">
                    Quantity: {item.quantity}
                  </p>
                </div>
                <p className="font-medium">${(item.price * item.quantity).toFixed(2)}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <Separator className="my-6" />

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Subtotal</span>
          <span>${order.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Shipping</span>
          <span>${(order.shipping || order.shippingCost || 0).toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Tax</span>
          <span>${(order.tax || 0).toFixed(2)}</span>
        </div>
        {order.discount && order.discount > 0 && (
          <div className="flex justify-between text-sm text-green-600">
            <span>Discount</span>
            <span>-${order.discount.toFixed(2)}</span>
          </div>
        )}
        <Separator className="my-2" />
        <div className="flex justify-between font-semibold">
          <span>Total</span>
          <span>${order.total.toFixed(2)}</span>
        </div>
      </div>
    </CardContent>
  </Card>
);

// Address card component
const AddressCard = ({
  title,
  address
}: {
  title: string;
  address: Order['shippingAddress'] | Order['billingAddress'];
}) => (
  <Card>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <address className="not-italic">
        <p className="font-medium">
          {address.firstName} {address.lastName}
        </p>
        <p>{address.streetAddress}</p>
        {address.apartment && (
          <p>{address.apartment}</p>
        )}
        <p>
          {address.city}, {address.state} {address.zipCode}
        </p>
        <p>{address.country}</p>
        <p className="mt-2">{address.phone}</p>
      </address>
    </CardContent>
  </Card>
);

// Order actions component
const OrderActions = ({ orderStatus }: { orderStatus: OrderStatus }) => (
  <div className="flex flex-col sm:flex-row gap-4">
    <Button variant="outline" asChild className="sm:flex-1">
      <Link href="/account/orders">
        Back to Orders
      </Link>
    </Button>

    {orderStatus === 'delivered' && (
      <Button asChild className="sm:flex-1">
        <Link href="/products">
          <ShoppingCart className="mr-2 h-4 w-4" />
          Shop Again
        </Link>
      </Button>
    )}
  </div>
);

// Order details content component
const OrderDetailsContent = ({ order, onDownloadInvoice }: { order: Order; onDownloadInvoice: () => void }) => (
  <div className="space-y-8">
    <OrderSummary order={order} onDownloadInvoice={onDownloadInvoice} />
    <OrderItems order={order} />

    {/* Shipping & Billing Information */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <AddressCard title="Shipping Information" address={order.shippingAddress} />
      <AddressCard title="Billing Information" address={order.billingAddress} />
    </div>

    <OrderActions orderStatus={order.status} />
  </div>
);

export default function OrderDetailPage() {
  const params = useParams();
  const orderId = params.id as string;
  const { order, loading } = useOrder(orderId);
  const { toast } = useToast();

  const handleDownloadInvoice = () => {
    toast({
      title: "Invoice Download",
      description: "Your invoice is being generated and will download shortly.",
      variant: "default",
    });

    // Implement actual invoice download functionality
  };

  // Determine which content to render based on loading state and order availability
  const renderContent = () => {
    if (loading) {
      return <LoadingState />;
    }

    if (!order) {
      return <OrderNotFound />;
    }

    return <OrderDetailsContent order={order} onDownloadInvoice={handleDownloadInvoice} />;
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container max-w-5xl mx-auto px-4 py-16">
          <div className="space-y-8 animate-fadeIn">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/account/orders">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <h1 className="text-3xl font-bold">Order Details</h1>
            </div>

            {renderContent()}
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
