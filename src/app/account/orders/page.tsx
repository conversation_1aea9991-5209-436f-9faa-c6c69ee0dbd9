'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/auth-context';
import { MainLayout } from '@/components/layout/main-layout';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { getUserOrders } from '@/lib/firebase/services/order-service';
import { Order, OrderStatus } from '@/types';

const statusColors: Record<OrderStatus, string> = {
  pending: 'bg-yellow-500',
  pending_payment: 'bg-orange-500',
  processing: 'bg-blue-500',
  shipped: 'bg-purple-500',
  delivered: 'bg-green-500',
  cancelled: 'bg-red-500',
};

// Helper function to format status text
const formatStatusText = (status: OrderStatus): string => {
  if (status === 'pending_payment') {
    return 'Pending Payment';
  }
  return status.charAt(0).toUpperCase() + status.slice(1);
};

// Loading component
const LoadingState = () => (
  <div className="flex justify-center items-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

// Empty orders component
const EmptyOrdersState = () => (
  <div className="text-center py-12 bg-muted/30 rounded-lg">
    <h3 className="text-xl font-semibold mb-2">No orders yet</h3>
    <p className="text-muted-foreground mb-6">
      You haven&apos;t placed any orders yet.
    </p>
    <Button asChild>
      <Link href="/products">Start Shopping</Link>
    </Button>
  </div>
);

// Order item component
const OrderItem = ({ item }: { item: Order['items'][0] }) => (
  <div className="flex items-center gap-4">
    <div className="relative w-16 h-16 rounded overflow-hidden">
      <Image
        src={item.image}
        alt={item.name}
        fill
        className="object-cover"
        unoptimized
      />
    </div>
    <div className="flex-1">
      <p className="font-medium">{item.name}</p>
      {item.variantName && (
        <p className="text-sm text-muted-foreground">
          Variant: {item.variantName}
        </p>
      )}
      <p className="text-sm">
        ${item.price.toFixed(2)} x {item.quantity}
      </p>
    </div>
  </div>
);

// Order card component
const OrderCard = ({ order }: { order: Order }) => (
  <Card className="overflow-hidden">
    <CardHeader className="bg-muted/50">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <CardTitle className="text-lg">Order #{order.id.slice(0, 8)}</CardTitle>
          <p className="text-sm text-muted-foreground">
            Placed on {format(order.createdAt, 'PPP')}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge className={statusColors[order.status]}>
            {formatStatusText(order.status)}
          </Badge>
          <p className="font-semibold">${order.total.toFixed(2)}</p>
        </div>
      </div>
    </CardHeader>
    <CardContent className="p-6">
      <div className="space-y-4">
        <div className="grid gap-4">
          {order.items.map((item) => (
            <OrderItem key={`${item.productId}-${item.variantId ?? ''}`} item={item} />
          ))}
        </div>
        <div className="flex justify-end">
          <Button asChild size="sm">
            <Link href={`/account/orders/${order.id}`}>
              View Details
            </Link>
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

// Orders list component
const OrdersList = ({ orders }: { orders: Order[] }) => (
  <div className="space-y-6">
    {orders.map((order) => (
      <OrderCard key={order.id} order={order} />
    ))}
  </div>
);

export default function OrdersPage() {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrders = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const userOrders = await getUserOrders(user.uid);
        setOrders(userOrders);
      } catch (error: unknown) {
        console.error('Error fetching orders:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        console.error('Error details:', errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user]);

  // Determine which content to render based on loading state and orders availability
  const renderContent = () => {
    if (loading) {
      return <LoadingState />;
    }

    if (orders.length === 0) {
      return <EmptyOrdersState />;
    }

    return <OrdersList orders={orders} />;
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container mx-auto px-4 py-16">
          <div className="space-y-6 animate-fadeIn">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">Your Orders</h1>
              <p className="text-muted-foreground">
                View and track your orders
              </p>
            </div>

            {renderContent()}
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
