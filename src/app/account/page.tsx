'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  User, 
  Package, 
  ShoppingBag, 
  Heart, 
  Settings, 
  LogOut,
  Clock
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function AccountPage() {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const accountLinks = [
    {
      title: 'Personal Information',
      description: 'Manage your personal details',
      icon: User,
      href: '/account/profile',
    },
    {
      title: 'Orders',
      description: 'View and track your orders',
      icon: Package,
      href: '/account/orders',
    },
    {
      title: 'Recently Viewed',
      description: 'Products you recently viewed',
      icon: Clock,
      href: '/account/recently-viewed',
    },
    {
      title: 'Wishlist',
      description: 'Products you saved for later',
      icon: Heart,
      href: '/account/wishlist',
    },
    {
      title: 'Settings',
      description: 'Manage account settings',
      icon: Settings,
      href: '/account/settings',
    },
  ];

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container max-w-5xl mx-auto px-4 py-16">
          <div className="space-y-8 animate-fadeIn">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  {user?.photoURL ? (
                    <AvatarImage src={user.photoURL} alt={user.displayName || ''} />
                  ) : (
                    <AvatarFallback className="bg-primary text-primary-foreground text-xl">
                      {user?.displayName?.charAt(0) ?? user?.email?.charAt(0)}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <h1 className="text-3xl font-bold">
                    Welcome, {user?.displayName ?? 'there'}!
                  </h1>
                  <p className="text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </div>
              <Button variant="outline" onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {accountLinks.map((link) => (
                <Card key={link.href} className="hover:shadow-md transition-shadow">
                  <Link href={link.href} className="block h-full">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <link.icon className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{link.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription>{link.description}</CardDescription>
                    </CardContent>
                  </Link>
                </Card>
              ))}

              <Card className="bg-primary/5 border-primary/20">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">Start Shopping</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4">
                    Explore our latest products and collections
                  </CardDescription>
                  <Button asChild size="sm">
                    <Link href="/products">Shop Now</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
