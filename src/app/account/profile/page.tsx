'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Mail, CheckCircle, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth-context';
import { MainLayout } from '@/components/layout/main-layout';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { updateUserProfile } from '@/lib/firebase/services/auth-service';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

const profileSchema = z.object({
  displayName: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  phoneNumber: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const { user, sendVerificationEmail } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const { toast } = useToast();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      displayName: user?.displayName ?? '',
      phoneNumber: user?.phoneNumber ?? '',
    },
  });

  // Reset verification sent state after 5 seconds
  useEffect(() => {
    if (verificationSent) {
      const timer = setTimeout(() => {
        setVerificationSent(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [verificationSent]);

  const handleSendVerification = async () => {
    try {
      setVerificationLoading(true);
      await sendVerificationEmail();
      setVerificationSent(true);
      toast({
        title: "Success",
        description: "Verification email has been sent. Please check your inbox.",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Send verification email error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setVerificationLoading(false);
    }
  };

  const renderVerificationButtonContent = () => {
    if (verificationLoading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    if (verificationSent) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return 'Verify';
  };

  const onSubmit = async (data: ProfileFormValues) => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    try {
      setIsLoading(true);
      await updateUserProfile(user.uid, {
        displayName: data.displayName,
        phoneNumber: data.phoneNumber,
      });

      toast({
        title: "Success",
        description: "Your profile has been updated successfully.",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Profile update error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update profile. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
      <div className="container max-w-md mx-auto px-4 py-16">
        <div className="space-y-6 animate-fadeIn">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Your Profile</h1>
            <p className="text-muted-foreground">
              Update your personal information
            </p>
          </div>

          {/* Email Verification Status */}
          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">{user?.email}</p>
                  <div className="flex items-center mt-1">
                    {user?.emailVerified ? (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        <span className="text-xs text-green-500">Verified</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                        <span className="text-xs text-amber-500">Not verified</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              {!user?.emailVerified && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSendVerification}
                  disabled={verificationLoading || verificationSent}
                >
                  {renderVerificationButtonContent()}
                </Button>
              )}
            </div>
            {verificationSent && (
              <Alert className="mt-2 bg-green-50 border-green-100">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-xs text-green-600">
                  Verification email sent! Please check your inbox.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="John Doe"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="+1234567890"
                        disabled={isLoading}
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="pt-4">
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Profile'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </MainLayout>
    </ProtectedRoute>
  );
}
