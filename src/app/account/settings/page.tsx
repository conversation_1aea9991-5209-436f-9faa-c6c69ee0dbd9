'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, Mail, Shield, LogOut, AlertCircle, CheckCircle, LucideIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { MainLayout } from '@/components/layout/main-layout';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

// Provider icon components
const GoogleIcon = () => (
  <svg className="h-4 w-4" viewBox="0 0 24 24">
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
    />
    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
    />
    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
    />
    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
    />
  </svg>
);

const FacebookIcon = () => (
  <svg className="h-4 w-4" viewBox="0 0 24 24">
    <path
      d="M9.198 21.5h4v-8.01h3.604l.396-3.98h-4V7.5a1 1 0 0 1 1-1h3v-4h-3a5 5 0 0 0-5 5v2.01h-2l-.396 3.98h2.396v8.01Z"
      fill="#1877F2"
    />
  </svg>
);

// Type for auth provider
type AuthProvider = {
  id: string;
  name: string;
  icon: LucideIcon | React.FC;
};

export default function SettingsPage() {
  const { user, signOut, sendVerificationEmail } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSendVerification = async () => {
    try {
      setVerificationLoading(true);
      await sendVerificationEmail();
      setVerificationSent(true);
      toast({
        title: "Success",
        description: "Verification email has been sent. Please check your inbox.",
        variant: "default",
      });
      setTimeout(() => {
        setVerificationSent(false);
      }, 5000);
    } catch (error: unknown) {
      console.error('Send verification email error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      router.push('/auth/login');
    } catch (error: unknown) {
      console.error('Sign out error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to sign out. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      setIsSigningOut(false);
    }
  };

  // Get authentication providers
  const getAuthProviders = (): AuthProvider[] => {
    if (!user?.providerData) return [];

    return user.providerData.map(provider => {
      const providerId = provider.providerId;

      if (providerId === 'password') {
        return { id: 'password', name: 'Email/Password', icon: Mail };
      } else if (providerId === 'google.com') {
        return { id: 'google.com', name: 'Google', icon: GoogleIcon };
      } else if (providerId === 'facebook.com') {
        return { id: 'facebook.com', name: 'Facebook', icon: FacebookIcon };
      } else {
        return { id: providerId, name: providerId, icon: Shield };
      }
    });
  };

  const providers = getAuthProviders();

  // Render provider status badge
  const renderProviderStatus = (providerId: string) => {
    if (providerId === 'password' && user?.emailVerified) {
      return (
        <span className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">
          Verified
        </span>
      );
    } else if (providerId === 'password' && !user?.emailVerified) {
      return (
        <span className="text-xs bg-amber-100 text-amber-600 px-2 py-0.5 rounded-full">
          Not verified
        </span>
      );
    } else {
      return (
        <span className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">
          Connected
        </span>
      );
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container max-w-2xl mx-auto px-4 py-16">
          <div className="space-y-8 animate-fadeIn">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">Account Settings</h1>
              <p className="text-muted-foreground">
                Manage your account settings and authentication methods
              </p>
            </div>

            {/* Email Verification */}
            {!user?.emailVerified && user?.email && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Email Verification</CardTitle>
                  <CardDescription>
                    Verify your email address to secure your account
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                    <p className="text-sm">
                      Your email address <span className="font-medium">{user.email}</span> is not verified.
                    </p>
                  </div>
                  {verificationSent && (
                    <Alert className="mt-3 bg-green-50 border-green-100">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <AlertDescription className="text-xs text-green-600">
                        Verification email sent! Please check your inbox.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSendVerification}
                    disabled={verificationLoading || verificationSent}
                  >
                    {verificationLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Verification Email'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Authentication Providers */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Authentication Methods</CardTitle>
                <CardDescription>
                  Manage how you sign in to your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {providers.map((provider) => (
                    <div key={provider.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {React.createElement(provider.icon, { className: "h-5 w-5" })}
                        <div>
                          <p className="font-medium">{provider.name}</p>
                          {provider.id === 'password' && (
                            <p className="text-xs text-muted-foreground">{user?.email}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center">
                        {renderProviderStatus(provider.id)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Account Actions */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Account Actions</CardTitle>
                <CardDescription>
                  Manage your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button
                    variant="destructive"
                    onClick={handleSignOut}
                    disabled={isSigningOut}
                    className="w-full sm:w-auto"
                  >
                    {isSigningOut ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing out...
                      </>
                    ) : (
                      <>
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign Out
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
