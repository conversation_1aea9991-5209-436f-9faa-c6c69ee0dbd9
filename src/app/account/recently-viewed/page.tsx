'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { addToWishlist, isProductInWishlist } from '@/lib/firebase/services/wishlist-service';
import Link from 'next/link';
import Image from 'next/image';
import {
  Clock,
  ShoppingCart,
  ArrowLeft,
  Loader2,
  Heart,
  X
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useCart } from '@/contexts/cart-context';
import { Product } from '@/types';

import { getRecentlyViewed, removeFromRecentlyViewed as removeFromRecentlyViewedService } from '@/lib/firebase/services/recently-viewed-service';

// Recently viewed hook using Firestore
const useRecentlyViewed = () => {
  const [recentlyViewed, setRecentlyViewed] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchRecentlyViewed = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const recentlyViewedData = await getRecentlyViewed(user.uid);
        setRecentlyViewed(recentlyViewedData);
      } catch (error: unknown) {
        console.error('Error fetching recently viewed products:', error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load recently viewed products. Please try again.";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRecentlyViewed();
  }, [toast, user]);

  const removeFromRecentlyViewed = async (productId: string) => {
    if (!user) return;

    try {
      await removeFromRecentlyViewedService(user.uid, productId);

      // Update local state
      setRecentlyViewed(prev => prev.filter(item => item.id !== productId));

      toast({
        title: 'Removed',
        description: 'Item has been removed from recently viewed.',
      });
    } catch (error: unknown) {
      console.error('Error removing from recently viewed:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to remove item. Please try again.";
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  return { recentlyViewed, loading, removeFromRecentlyViewed };
};

// Loading component
const LoadingState = () => (
  <div className="flex justify-center items-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

// Empty state component
const EmptyState = () => (
  <div className="text-center py-16 bg-muted/30 rounded-lg">
    <Clock className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
    <h2 className="text-2xl font-semibold mb-2">No recently viewed products</h2>
    <p className="text-muted-foreground mb-6">
      Browse our products to see your viewing history here.
    </p>
    <Button asChild>
      <Link href="/products">Browse Products</Link>
    </Button>
  </div>
);

// Product card component
const ProductCard = ({
  product,
  onRemove,
  onAddToWishlist,
  onAddToCart,
  addingToWishlist,
  addingToCart
}: {
  product: Product;
  onRemove: (id: string) => void;
  onAddToWishlist: (product: Product) => void;
  onAddToCart: (product: Product) => void;
  addingToWishlist: string | null;
  addingToCart: string | null;
}) => (
  <Card className="overflow-hidden">
    <div className="relative aspect-square">
      <Image
        src={product.images[0]}
        alt={product.name}
        fill
        className="object-cover"
        unoptimized
      />
      <div className="absolute top-2 right-2 flex gap-2">
        <button
          onClick={() => onRemove(product.id)}
          className="bg-white/80 p-1.5 rounded-full hover:bg-white"
          aria-label="Remove from recently viewed"
        >
          <X className="h-4 w-4 text-gray-600 hover:text-red-500" />
        </button>
        <button
          onClick={() => onAddToWishlist(product)}
          className="bg-white/80 p-1.5 rounded-full hover:bg-white"
          aria-label="Add to wishlist"
          disabled={addingToWishlist === product.id}
        >
          {addingToWishlist === product.id ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Heart className="h-4 w-4 text-gray-600 hover:text-red-500" />
          )}
        </button>
      </div>
    </div>
    <CardContent className="p-4">
      <Link href={`/products/${product.slug ?? product.id}`} className="block">
        <h3 className="font-medium mb-1 hover:text-primary transition-colors">
          {product.name}
        </h3>
      </Link>
      <p className="text-muted-foreground text-sm mb-2">
        {product.category}
      </p>
      <div className="flex justify-between items-center">
        <span className="font-semibold">${product.price.toFixed(2)}</span>
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddToCart(product)}
          disabled={addingToCart === product.id}
        >
          {addingToCart === product.id ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              <ShoppingCart className="h-4 w-4 mr-1" />
              Add
            </>
          )}
        </Button>
      </div>
    </CardContent>
  </Card>
);

// Products grid component
const ProductsGrid = ({
  products,
  onRemove,
  onAddToWishlist,
  onAddToCart,
  addingToWishlist,
  addingToCart
}: {
  products: Product[];
  onRemove: (id: string) => void;
  onAddToWishlist: (product: Product) => void;
  onAddToCart: (product: Product) => void;
  addingToWishlist: string | null;
  addingToCart: string | null;
}) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    {products.map((product) => (
      <ProductCard
        key={product.id}
        product={product}
        onRemove={onRemove}
        onAddToWishlist={onAddToWishlist}
        onAddToCart={onAddToCart}
        addingToWishlist={addingToWishlist}
        addingToCart={addingToCart}
      />
    ))}
  </div>
);

export default function RecentlyViewedPage() {
  const { recentlyViewed, loading, removeFromRecentlyViewed } = useRecentlyViewed();
  const { addToCart } = useCart();
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [addingToWishlist, setAddingToWishlist] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const handleAddToCart = async (product: Product) => {
    setAddingToCart(product.id);

    try {
      // Add to cart using the cart context
      addToCart(product, 1);

      // Note: We don't need to show a toast here since the addToCart function
      // in the cart context already shows a toast notification
    } catch (error: unknown) {
      console.error('Error adding to cart:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add item to cart. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setAddingToCart(null);
    }
  };

  const handleAddToWishlist = async (product: Product) => {
    setAddingToWishlist(product.id);

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to add items to your wishlist.",
        variant: "destructive",
      });
      setAddingToWishlist(null);
      return;
    }

    try {
      // Check if product is already in wishlist
      const isInWishlist = await isProductInWishlist(user.uid, product.id);

      if (isInWishlist) {
        toast({
          title: "Already in Wishlist",
          description: `${product.name} is already in your wishlist.`,
          variant: "default",
        });
      } else {
        // Add to wishlist
        await addToWishlist(user.uid, product.id);

        toast({
          title: "Added to Wishlist",
          description: `${product.name} has been added to your wishlist.`,
          variant: "default",
        });
      }
    } catch (error: unknown) {
      console.error('Error adding to wishlist:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add item to wishlist. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setAddingToWishlist(null);
    }
  };

  // Determine which content to render based on loading state and products availability
  const renderContent = () => {
    if (loading) {
      return <LoadingState />;
    }

    if (recentlyViewed.length === 0) {
      return <EmptyState />;
    }

    return (
      <ProductsGrid
        products={recentlyViewed}
        onRemove={removeFromRecentlyViewed}
        onAddToWishlist={handleAddToWishlist}
        onAddToCart={handleAddToCart}
        addingToWishlist={addingToWishlist}
        addingToCart={addingToCart}
      />
    );
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container max-w-5xl mx-auto px-4 py-16">
          <div className="space-y-6 animate-fadeIn">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/account">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <h1 className="text-3xl font-bold">Recently Viewed</h1>
            </div>

            {renderContent()}
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
