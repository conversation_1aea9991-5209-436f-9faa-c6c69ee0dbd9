'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import Link from 'next/link';
import Image from 'next/image';
import {
  Heart,
  ShoppingCart,
  Trash2,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useCart } from '@/contexts/cart-context';
import { Product } from '@/types';

import { getWishlist, removeFromWishlist as removeFromWishlistService, clearWishlist as clearWishlistService } from '@/lib/firebase/services/wishlist-service';

// Wishlist hook using Firestore
const useWishlist = () => {
  const [wishlist, setWishlist] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchWishlist = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const wishlistData = await getWishlist(user.uid);
        setWishlist(wishlistData);
      } catch (error: unknown) {
        console.error('Error fetching wishlist:', error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load wishlist. Please try again.";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, [toast, user]);

  const removeFromWishlist = async (productId: string) => {
    if (!user) return;

    try {
      await removeFromWishlistService(user.uid, productId);

      // Update local state
      setWishlist(prev => prev.filter(item => item.id !== productId));

      toast({
        title: "Success",
        description: "Item removed from wishlist",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error removing from wishlist:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to remove item from wishlist";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const clearWishlist = async () => {
    if (!user) return;

    try {
      await clearWishlistService(user.uid);

      // Update local state
      setWishlist([]);

      toast({
        title: "Success",
        description: "Your wishlist has been cleared",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error clearing wishlist:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to clear wishlist";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return { wishlist, loading, removeFromWishlist, clearWishlist };
};

// Loading component
const LoadingState = () => (
  <div className="flex justify-center items-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

// Empty wishlist component
const EmptyWishlist = () => (
  <div className="text-center py-16 bg-muted/30 rounded-lg">
    <Heart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
    <h2 className="text-2xl font-semibold mb-2">Your wishlist is empty</h2>
    <p className="text-muted-foreground mb-6">
      Save items you love to your wishlist and revisit them anytime.
    </p>
    <Button asChild>
      <Link href="/products">Start Shopping</Link>
    </Button>
  </div>
);

// Product card component
const ProductCard = ({
  product,
  onRemove,
  onAddToCart,
  isAddingToCart
}: {
  product: Product;
  onRemove: (id: string) => void;
  onAddToCart: (product: Product) => void;
  isAddingToCart: boolean;
}) => (
  <Card className="overflow-hidden">
    <div className="relative aspect-square">
      <Image
        src={product.images[0]}
        alt={product.name}
        fill
        className="object-cover"
        unoptimized
      />
      <button
        onClick={() => onRemove(product.id)}
        className="absolute top-2 right-2 bg-white/80 p-1.5 rounded-full hover:bg-white"
        aria-label="Remove from wishlist"
      >
        <Trash2 className="h-4 w-4 text-red-500" />
      </button>
    </div>
    <CardContent className="p-4">
      <Link href={`/products/${product.slug ?? product.id}`} className="block">
        <h3 className="font-medium mb-1 hover:text-primary transition-colors">
          {product.name}
        </h3>
      </Link>
      <p className="text-muted-foreground text-sm mb-2">
        {product.category}
      </p>
      <div className="flex justify-between items-center">
        <span className="font-semibold">${product.price.toFixed(2)}</span>
        <Button
          size="sm"
          onClick={() => onAddToCart(product)}
          disabled={isAddingToCart}
        >
          {isAddingToCart ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              <ShoppingCart className="h-4 w-4 mr-1" />
              Add to Cart
            </>
          )}
        </Button>
      </div>
    </CardContent>
  </Card>
);

// Wishlist actions component
const WishlistActions = ({
  onMoveAllToCart,
  onClearWishlist,
  isMovingAllToCart
}: {
  onMoveAllToCart: () => void;
  onClearWishlist: () => void;
  isMovingAllToCart: boolean;
}) => (
  <div className="flex gap-2">
    <Button
      variant="outline"
      size="sm"
      onClick={onMoveAllToCart}
      disabled={isMovingAllToCart}
    >
      {isMovingAllToCart ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Moving...
        </>
      ) : (
        <>
          <ShoppingCart className="h-4 w-4 mr-2" />
          Move All to Cart
        </>
      )}
    </Button>
    <Button
      variant="outline"
      size="sm"
      onClick={onClearWishlist}
    >
      <Trash2 className="h-4 w-4 mr-2" />
      Clear Wishlist
    </Button>
  </div>
);

export default function WishlistPage() {
  const { wishlist, loading, removeFromWishlist, clearWishlist } = useWishlist();
  const { addToCart } = useCart();
  const { toast } = useToast();
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [movingAllToCart, setMovingAllToCart] = useState(false);

  const handleAddToCart = async (product: Product) => {
    setAddingToCart(product.id);

    try {
      // Add to cart
      addToCart(product, 1);

      toast({
        title: "Added to Cart",
        description: `${product.name} has been added to your cart.`,
        variant: "default",
      });

      // Remove from wishlist after adding to cart
      await removeFromWishlist(product.id);
    } catch (error: unknown) {
      console.error('Error adding to cart:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add item to cart. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setAddingToCart(null);
    }
  };

  const handleMoveAllToCart = async () => {
    if (wishlist.length === 0) return;

    setMovingAllToCart(true);

    try {
      // Add each product to cart
      for (const product of wishlist) {
        addToCart(product, 1);
      }

      toast({
        title: "Success",
        description: `${wishlist.length} items moved to your cart`,
        variant: "default",
      });

      // Clear wishlist after moving all to cart
      await clearWishlist();
    } catch (error: unknown) {
      console.error('Error moving all to cart:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to move all items to cart";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setMovingAllToCart(false);
    }
  };

  // Render content based on loading state and wishlist data
  const renderContent = () => {
    if (loading) {
      return <LoadingState />;
    }

    if (wishlist.length === 0) {
      return <EmptyWishlist />;
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {wishlist.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onRemove={removeFromWishlist}
            onAddToCart={handleAddToCart}
            isAddingToCart={addingToCart === product.id}
          />
        ))}
      </div>
    );
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container max-w-5xl mx-auto px-4 py-16">
          <div className="space-y-6 animate-fadeIn">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="icon" asChild>
                  <Link href="/account">
                    <ArrowLeft className="h-4 w-4" />
                  </Link>
                </Button>
                <h1 className="text-3xl font-bold">My Wishlist</h1>
              </div>

              {wishlist.length > 0 && !loading && (
                <WishlistActions
                  onMoveAllToCart={handleMoveAllToCart}
                  onClearWishlist={clearWishlist}
                  isMovingAllToCart={movingAllToCart}
                />
              )}
            </div>

            {renderContent()}
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
