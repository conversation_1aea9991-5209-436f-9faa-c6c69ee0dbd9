'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { MainLayout } from '@/components/layout/main-layout';
import { ProductGrid } from '@/components/ui/product-grid';
import { Loader2 } from 'lucide-react';
import { getActiveNewArrival, NewArrivalWithProducts } from '@/lib/firebase/services/new-arrival-service';

export default function NewArrivalsPage() {
  const [newArrival, setNewArrival] = useState<NewArrivalWithProducts | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch new arrival data
  useEffect(() => {
    const fetchNewArrival = async () => {
      try {
        setLoading(true);
        const data = await getActiveNewArrival();
        setNewArrival(data);
      } catch (error) {
        console.error('Error fetching new arrivals:', error);
        setNewArrival(null);
      } finally {
        setLoading(false);
      }
    };

    fetchNewArrival();
  }, []);

  return (
    <MainLayout>
      {loading ? (
        <div className="container mx-auto px-4 py-16 flex justify-center items-center min-h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : newArrival ? (
        <div className="animate-fadeInUp">
          {/* Hero Banner */}
          <div className="relative aspect-[21/9] w-full overflow-hidden">
            <Image
              src={newArrival.image}
              alt={newArrival.title}
              fill
              className="object-cover"
              priority
              unoptimized
            />
            <div className="absolute inset-0 bg-black/30 flex flex-col justify-center items-start p-8 md:p-16">
              <div className="max-w-xl text-white">
                <h1 className="text-3xl md:text-5xl font-bold mb-2 md:mb-4 animate-fadeInUp">
                  {newArrival.title}
                </h1>
                <p className="text-lg md:text-xl mb-4 md:mb-8 animate-fadeInUp animation-delay-200">
                  {newArrival.description}
                </p>
              </div>
            </div>
          </div>

          {/* Products */}
          <div className="container mx-auto px-4 py-16">
            <h2 className="text-3xl font-bold mb-8">Latest Products</h2>
            {newArrival.products.length > 0 ? (
              <ProductGrid products={newArrival.products} />
            ) : (
              <div className="text-center py-16">
                <p className="text-muted-foreground">No new products available at the moment. Check back soon!</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-3xl font-bold mb-4">New Arrivals</h1>
          <p className="text-muted-foreground mb-8">No new arrivals available at the moment. Check back soon!</p>
        </div>
      )}
    </MainLayout>
  );
}
