'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, ArrowLeft, Upload, Trash2, Link as LinkIcon } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  getCarouselSlideById,
  createCarouselSlide,
  updateCarouselSlide,
  CarouselSlideInput
} from '@/lib/firebase/services/carousel-service';
import { uploadImage } from '@/lib/firebase/services/storage-service';

// Define form schema
const slideSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  subtitle: z.string().min(1, 'Subtitle is required'),
  cta: z.string().min(1, 'Call to action text is required'),
  link: z.string().min(1, 'Link is required'),
  image: z.string().url('Image must be a valid URL'),
  isActive: z.boolean(),
});

type SlideFormValues = z.infer<typeof slideSchema>;

export default function CarouselSlidePage() {
  const router = useRouter();
  const params = useParams();
  const { id } = params;
  const isNew = id === 'new';
  const { toast } = useToast();

  const [loading, setLoading] = useState(!isNew);
  const [submitting, setSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  // Initialize form
  const form = useForm<SlideFormValues>({
    resolver: zodResolver(slideSchema),
    defaultValues: {
      title: '',
      subtitle: '',
      cta: 'Shop Now',
      link: '/products',
      image: '',
      isActive: true,
    },
  });

  // Fetch slide data if editing
  useEffect(() => {
    if (isNew) return;

    const fetchSlide = async () => {
      try {
        setLoading(true);
        // Use Firebase client SDK directly
        const slide = await getCarouselSlideById(id as string);

        if (slide) {
          // Set form values
          form.reset({
            title: slide.title,
            subtitle: slide.subtitle,
            cta: slide.cta,
            link: slide.link,
            image: slide.image,
            isActive: slide.isActive,
          });

          // Set image preview
          setImagePreview(slide.image);
        } else {
          throw new Error('Carousel slide not found');
        }
      } catch (error: unknown) {
        console.error('Error fetching carousel slide:', error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load carousel slide. Please try again.";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        router.push('/admin/carousel');
      } finally {
        setLoading(false);
      }
    };

    fetchSlide();
  }, [id, isNew, form, router, toast]);

  // Get action verb based on operation type
  const getActionVerb = (): string => {
    return isNew ? 'created' : 'updated';
  };

  // Handle successful submission
  const handleSubmitSuccess = () => {
    toast({
      title: "Success",
      description: `Carousel slide ${getActionVerb()} successfully.`,
      variant: "default",
    });
    router.push('/admin/carousel');
  };

  // Handle submission error
  const handleSubmitError = (error: unknown) => {
    const actionType = isNew ? 'creating' : 'updating';
    console.error(`Error ${actionType} carousel slide:`, error);

    let errorMessage: string;
    if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      const failAction = isNew ? 'create' : 'update';
      errorMessage = `Failed to ${failAction} carousel slide. Please try again.`;
    }

    toast({
      title: "Error",
      description: errorMessage,
      variant: "destructive",
    });
  };

  // Handle form submission
  const onSubmit = async (data: SlideFormValues) => {
    setSubmitting(true);

    try {
      // Use Firebase client SDK directly
      if (isNew) {
        // Create new slide
        const slideData: CarouselSlideInput = {
          title: data.title,
          subtitle: data.subtitle,
          cta: data.cta,
          link: data.link,
          image: data.image,
          isActive: data.isActive
        };

        await createCarouselSlide(slideData);
      } else {
        // Update existing slide
        await updateCarouselSlide(id as string, {
          title: data.title,
          subtitle: data.subtitle,
          cta: data.cta,
          link: data.link,
          image: data.image,
          isActive: data.isActive
        });
      }

      handleSubmitSuccess();
    } catch (error: unknown) {
      handleSubmitError(error);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Error",
        description: "Please upload an image file.",
        variant: "destructive",
      });
      return;
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Error",
        description: "Image size should be less than 5MB.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploadingImage(true);

      // Use Firebase Storage directly
      const imageUrl = await uploadImage(file, 'carousel');

      // Set image URL in form
      form.setValue('image', imageUrl);
      setImagePreview(imageUrl);

      toast({
        title: "Success",
        description: "Image uploaded successfully.",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error uploading image:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to upload image. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploadingImage(false);
    }
  };

  // Clear image
  const clearImage = () => {
    form.setValue('image', '');
    setImagePreview(null);
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push('/admin/carousel')} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isNew ? 'Add Carousel Slide' : 'Edit Carousel Slide'}
          </h1>
        </div>
        <Button
          type="submit"
          form="slide-form"
          disabled={submitting}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save
            </>
          )}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Slide Details</CardTitle>
            <CardDescription>
              Enter the details for this carousel slide.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form id="slide-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Transform Your Space" />
                      </FormControl>
                      <FormDescription>
                        The main heading displayed on the slide.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subtitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subtitle</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Elegant interior solutions for modern homes"
                          rows={2}
                        />
                      </FormControl>
                      <FormDescription>
                        A brief description displayed below the title.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="cta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Call to Action</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Shop Now" />
                        </FormControl>
                        <FormDescription>
                          Text for the action button.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="link"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Link</FormLabel>
                        <FormControl>
                          <div className="flex items-center">
                            <LinkIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                            <Input {...field} placeholder="/products" />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Where the button should link to.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Image URL</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://example.com/image.jpg" />
                      </FormControl>
                      <FormDescription>
                        URL of the slide background image.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label htmlFor="image-upload" className="text-sm font-medium">Upload Image</label>
                    {uploadingImage && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        Uploading...
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById('image-upload')?.click()}
                      disabled={uploadingImage}
                      className="w-full"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Select Image
                    </Button>
                    <input
                      id="image-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      disabled={uploadingImage}
                      aria-label="Upload image"
                    />
                    {imagePreview && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={clearImage}
                        disabled={uploadingImage}
                        className="text-red-500"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Clear
                      </Button>
                    )}
                  </div>
                  <p className="mt-1 text-xs text-muted-foreground">
                    Recommended size: 1920x820 pixels (21:9 aspect ratio)
                  </p>
                </div>

                <Separator />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Active Status</FormLabel>
                        <FormDescription>
                          Show this slide on the home page carousel.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              Preview how the slide will appear on the home page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative aspect-[21/9] w-full overflow-hidden rounded-lg border">
              {imagePreview ? (
                <Image
                  src={imagePreview}
                  alt="Slide preview"
                  fill
                  className="object-cover"
                  unoptimized
                />
              ) : (
                <div className="flex h-full items-center justify-center bg-muted">
                  <p className="text-muted-foreground">No image selected</p>
                </div>
              )}
              <div className="absolute inset-0 bg-black/30 flex flex-col justify-center items-start p-8">
                <div className="max-w-xl text-white">
                  <h1 className="text-3xl font-bold mb-2">
                    {form.watch('title') || 'Slide Title'}
                  </h1>
                  <p className="text-lg mb-4">
                    {form.watch('subtitle') || 'Slide subtitle text goes here'}
                  </p>
                  <Button size="lg">
                    {form.watch('cta') || 'Call to Action'}
                  </Button>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                <strong>Status:</strong>{' '}
                {form.watch('isActive') ? (
                  <span className="text-green-600">Active</span>
                ) : (
                  <span className="text-gray-500">Inactive</span>
                )}
              </p>
              <p className="text-sm text-muted-foreground">
                <strong>Link:</strong> {form.watch('link') || '/products'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
