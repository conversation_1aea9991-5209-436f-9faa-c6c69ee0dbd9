'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  getAllCarouselSlides,
  deleteCarouselSlide,
  toggleCarouselSlideActive,
  updateCarouselSlideOrder
} from '@/lib/firebase/services/carousel-service';
import Image from 'next/image';
import {
  Plus,
  MoreHorizontal,
  Pencil,
  Trash2,
  Loader2,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  ImageIcon,
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';

// Define carousel slide type (same as in the service)
interface CarouselSlide {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  cta: string;
  link: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export default function CarouselManagementPage() {
  const [slides, setSlides] = useState<CarouselSlide[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteSlideId, setDeleteSlideId] = useState<string | null>(null);
  const [toggleSlideId, setToggleSlideId] = useState<string | null>(null);
  const [toggleSlideActive, setToggleSlideActive] = useState<boolean>(false);
  const router = useRouter();
  const { toast } = useToast();

  // Fetch carousel slides
  useEffect(() => {
    fetchSlides();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch slides function
  const fetchSlides = async () => {
    try {
      setLoading(true);
      // Use the client-side Firebase SDK directly
      const carouselSlides = await getAllCarouselSlides();
      setSlides(carouselSlides);
    } catch (error: unknown) {
      console.error('Error fetching carousel slides:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load carousel slides. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  // Handle edit slide
  const handleEditSlide = (slide: CarouselSlide) => {
    router.push(`/admin/carousel/${slide.id}`);
  };

  // Handle delete slide
  const handleDeleteSlide = async () => {
    if (!deleteSlideId) return;

    try {
      // Use the client-side Firebase SDK directly
      await deleteCarouselSlide(deleteSlideId);

      // Remove slide from state
      setSlides(slides.filter(slide => slide.id !== deleteSlideId));

      toast({
        title: "Success",
        description: "Carousel slide deleted successfully.",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error deleting carousel slide:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete carousel slide. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setDeleteSlideId(null);
    }
  };

  // Handle toggle slide active status
  const handleToggleSlideActive = async () => {
    if (!toggleSlideId) return;

    try {
      // Use the client-side Firebase SDK directly
      await toggleCarouselSlideActive(toggleSlideId, toggleSlideActive);

      // Update slide in state
      setSlides(slides.map(slide =>
        slide.id === toggleSlideId
          ? { ...slide, isActive: toggleSlideActive }
          : slide
      ));

      toast({
        title: "Success",
        description: `Carousel slide ${toggleSlideActive ? 'activated' : 'deactivated'} successfully.`,
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error updating carousel slide:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update carousel slide. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setToggleSlideId(null);
    }
  };

  // Handle move slide up/down
  const handleMoveSlide = async (slideId: string, direction: 'up' | 'down') => {
    const currentIndex = slides.findIndex(slide => slide.id === slideId);
    if (currentIndex === -1) return;

    // Can't move first slide up or last slide down
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === slides.length - 1)
    ) {
      return;
    }

    // Calculate new order
    const newSlides = [...slides];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    // Swap slides
    [newSlides[currentIndex], newSlides[targetIndex]] = [newSlides[targetIndex], newSlides[currentIndex]];

    // Update order property
    const reorderedSlides = newSlides.map((slide, index) => ({
      ...slide,
      order: index + 1,
    }));

    // Update state optimistically
    setSlides(reorderedSlides);

    try {
      // Update each slide's order using the Firebase client SDK
      const updatePromises = reorderedSlides.map(slide =>
        updateCarouselSlideOrder(slide.id, slide.order)
      );

      await Promise.all(updatePromises);

      toast({
        title: "Success",
        description: "Carousel slides reordered successfully.",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error('Error reordering carousel slides:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to reorder carousel slides. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      // Revert state on error
      fetchSlides();
    }
  };



  // Loading state component
  const renderLoadingState = () => (
    <div className="flex justify-center items-center h-64">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  );

  // Empty state component
  const renderEmptyState = () => (
    <Card>
      <CardContent className="flex flex-col items-center justify-center h-64 text-center">
        <ImageIcon className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No carousel slides found</h3>
        <p className="text-muted-foreground mb-4">
          Add your first slide to get started with the home page carousel.
        </p>
        <Button asChild>
          <Link href="/admin/carousel/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Slide
          </Link>
        </Button>
      </CardContent>
    </Card>
  );

  // Slides table component
  const renderSlidesTable = () => (
    <div className="bg-white rounded-md shadow overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">Order</TableHead>
            <TableHead className="w-24">Image</TableHead>
            <TableHead>Title</TableHead>
            <TableHead>Subtitle</TableHead>
            <TableHead>CTA</TableHead>
            <TableHead>Link</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {slides.map((slide) => (
            <TableRow key={slide.id}>
              <TableCell className="font-medium">
                <div className="flex flex-col items-center">
                  <span>{slide.order}</span>
                  <div className="flex flex-col mt-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => handleMoveSlide(slide.id, 'up')}
                      disabled={slide.order === 1}
                    >
                      <ArrowUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => handleMoveSlide(slide.id, 'down')}
                      disabled={slide.order === slides.length}
                    >
                      <ArrowDown className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="relative h-12 w-24 rounded overflow-hidden">
                  <Image
                    src={slide.image}
                    alt={slide.title}
                    fill
                    className="object-cover"
                    unoptimized
                  />
                </div>
              </TableCell>
              <TableCell>{slide.title}</TableCell>
              <TableCell className="max-w-xs truncate">{slide.subtitle}</TableCell>
              <TableCell>{slide.cta}</TableCell>
              <TableCell className="max-w-xs truncate">{slide.link}</TableCell>
              <TableCell>
                <Badge
                  variant={slide.isActive ? "default" : "secondary"}
                  className={slide.isActive ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-gray-100 text-gray-800 hover:bg-gray-100"}
                >
                  {slide.isActive ? "Active" : "Inactive"}
                </Badge>
              </TableCell>
              <TableCell>{formatDate(slide.updatedAt)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => handleEditSlide(slide)}>
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setToggleSlideId(slide.id);
                        setToggleSlideActive(!slide.isActive);
                      }}
                    >
                      {slide.isActive ? (
                        <>
                          <EyeOff className="mr-2 h-4 w-4" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <Eye className="mr-2 h-4 w-4" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => setDeleteSlideId(slide.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  // Render content based on state
  const renderContent = () => {
    if (loading) {
      return renderLoadingState();
    }

    if (slides.length === 0) {
      return renderEmptyState();
    }

    return renderSlidesTable();
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Carousel Management</h1>
        <Button asChild>
          <Link href="/admin/carousel/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Slide
          </Link>
        </Button>
      </div>

      {renderContent()}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteSlideId} onOpenChange={() => setDeleteSlideId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              carousel slide from the database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSlide}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toggle Active Status Dialog */}
      <AlertDialog open={!!toggleSlideId} onOpenChange={() => setToggleSlideId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {toggleSlideActive ? 'Activate Slide?' : 'Deactivate Slide?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {toggleSlideActive
                ? 'This slide will be visible on the home page carousel.'
                : 'This slide will be hidden from the home page carousel.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleToggleSlideActive}>
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
