'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getAllShippingMethods } from '@/lib/firebase/services/shipping-service';
import { Loader2, Truck, Package, Clock, Plus, Settings } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export default function AdminShippingPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [activeMethodsCount, setActiveMethodsCount] = useState(0);
  const [inactiveMethodsCount, setInactiveMethodsCount] = useState(0);
  const [lowestPrice, setLowestPrice] = useState<number | null>(null);
  const [highestPrice, setHighestPrice] = useState<number | null>(null);

  useEffect(() => {
    const fetchShippingStats = async () => {
      try {
        setLoading(true);
        const methods = await getAllShippingMethods(true); // Include inactive methods
        
        // Count active and inactive methods
        const active = methods.filter(m => m.isActive).length;
        const inactive = methods.filter(m => !m.isActive).length;
        
        setActiveMethodsCount(active);
        setInactiveMethodsCount(inactive);
        
        // Find lowest and highest prices
        if (methods.length > 0) {
          const prices = methods.map(m => m.price);
          setLowestPrice(Math.min(...prices));
          setHighestPrice(Math.max(...prices));
        }
      } catch (error) {
        console.error('Error fetching shipping stats:', error);
        toast({
          title: 'Error',
          description: 'Failed to load shipping information. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchShippingStats();
  }, [toast]);

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-96">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Shipping</h1>
            <p className="text-muted-foreground">
              Manage your store's shipping options and settings
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Methods</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeMethodsCount}</div>
              <p className="text-xs text-muted-foreground">
                shipping options available to customers
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Methods</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inactiveMethodsCount}</div>
              <p className="text-xs text-muted-foreground">
                shipping options currently disabled
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lowest Price</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {lowestPrice !== null ? `$${lowestPrice.toFixed(2)}` : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                cheapest shipping option
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Price</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {highestPrice !== null ? `$${highestPrice.toFixed(2)}` : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                premium shipping option
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Methods</CardTitle>
              <CardDescription>
                Manage the shipping options available to your customers
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col space-y-2">
              <p className="text-sm text-muted-foreground mb-2">
                Create, edit, and manage shipping methods for your store. Set prices, delivery times, and availability.
              </p>
              <div className="flex space-x-2">
                <Button asChild>
                  <Link href="/admin/shipping/methods">
                    <Truck className="mr-2 h-4 w-4" />
                    View All Methods
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/admin/shipping/methods/new">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Method
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Shipping Settings</CardTitle>
              <CardDescription>
                Configure global shipping settings for your store
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col space-y-2">
              <p className="text-sm text-muted-foreground mb-2">
                Configure shipping zones, tax settings, and other global shipping options in your store settings.
              </p>
              <Button asChild>
                <Link href="/admin/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Store Settings
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
