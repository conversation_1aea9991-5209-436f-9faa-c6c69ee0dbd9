'use client';

import { useState, useEffect } from 'react';
import { useRouter, use<PERSON>arams } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, Arrow<PERSON>eft, Trash2 } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import {
  getShippingMethodById,
  updateShippingMethod,
  deleteShippingMethod
} from '@/lib/firebase/services/shipping-service';
import Link from 'next/link';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

// Form validation schema
const shippingMethodSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  description: z.string().min(5, { message: 'Description must be at least 5 characters' }),
  price: z.coerce.number().min(0, { message: 'Price must be a positive number' }),
  minDeliveryDays: z.coerce.number().int().min(1, { message: 'Minimum delivery days must be at least 1' }),
  maxDeliveryDays: z.coerce.number().int().min(1, { message: 'Maximum delivery days must be at least 1' }),
  isActive: z.boolean(),
});

type ShippingMethodFormValues = z.infer<typeof shippingMethodSchema>;

export default function EditShippingMethodPage() {
  const params = useParams();
  const id = params.id as string;
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [notFound, setNotFound] = useState(false);

  // Initialize form with default values
  const form = useForm<ShippingMethodFormValues>({
    resolver: zodResolver(shippingMethodSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      minDeliveryDays: 1,
      maxDeliveryDays: 5,
      isActive: true,
    },
  });

  // Fetch shipping method data
  useEffect(() => {
    const fetchShippingMethod = async () => {
      try {
        setIsLoading(true);
        const method = await getShippingMethodById(id);

        if (!method) {
          setNotFound(true);
          return;
        }

        // Set form values
        form.reset({
          name: method.name,
          description: method.description,
          price: method.price,
          minDeliveryDays: method.estimatedDeliveryDays.min,
          maxDeliveryDays: method.estimatedDeliveryDays.max,
          isActive: method.isActive,
        });
      } catch (error) {
        console.error('Error fetching shipping method:', error);
        toast({
          title: 'Error',
          description: 'Failed to load shipping method. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchShippingMethod();
  }, [id, form, toast]);

  // Handle form submission
  const onSubmit = async (values: ShippingMethodFormValues) => {
    // Validate that max days is greater than or equal to min days
    if (values.maxDeliveryDays < values.minDeliveryDays) {
      form.setError('maxDeliveryDays', {
        type: 'manual',
        message: 'Maximum delivery days must be greater than or equal to minimum delivery days',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Update shipping method in Firestore
      await updateShippingMethod(id, {
        name: values.name,
        description: values.description,
        price: values.price,
        estimatedDeliveryDays: {
          min: values.minDeliveryDays,
          max: values.maxDeliveryDays,
        },
        isActive: values.isActive,
      });

      toast({
        title: 'Success',
        description: 'Shipping method updated successfully',
      });

      // Redirect to shipping methods list
      router.push('/admin/shipping/methods');
    } catch (error) {
      console.error('Error updating shipping method:', error);
      toast({
        title: 'Error',
        description: 'Failed to update shipping method. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle shipping method deletion
  const handleDelete = async () => {
    try {
      await deleteShippingMethod(id);

      toast({
        title: 'Success',
        description: 'Shipping method deleted successfully',
      });

      // Redirect to shipping methods list
      router.push('/admin/shipping/methods');
    } catch (error) {
      console.error('Error deleting shipping method:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete shipping method. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  if (notFound) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold tracking-tight">Shipping Method Not Found</h1>
            <Button variant="outline" asChild>
              <Link href="/admin/shipping/methods">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Shipping Methods
              </Link>
            </Button>
          </div>
          <div className="border rounded-lg p-8 text-center">
            <p className="text-muted-foreground mb-4">
              The shipping method you are looking for does not exist or has been deleted.
            </p>
            <Button asChild>
              <Link href="/admin/shipping/methods">
                View All Shipping Methods
              </Link>
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Shipping Method</h1>
            <p className="text-muted-foreground">
              Update shipping option details
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href="/admin/shipping/methods">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <Button
              variant="destructive"
              onClick={() => setDeleteDialogOpen(true)}
              disabled={isLoading}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="border rounded-lg p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Standard Shipping" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the shipping method shown to customers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="absolute left-3 top-2.5">$</span>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="5.99"
                              className="pl-7"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          The cost of this shipping method
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Delivery within 3-5 business days"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        A brief description of the shipping method
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="minDeliveryDays"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Delivery Days</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Minimum estimated delivery time in days
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxDeliveryDays"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Delivery Days</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Maximum estimated delivery time in days
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Active Status</FormLabel>
                        <FormDescription>
                          Make this shipping method available to customers
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this shipping method. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
