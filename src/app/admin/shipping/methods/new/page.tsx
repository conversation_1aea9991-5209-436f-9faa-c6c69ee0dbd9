'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, ArrowLeft } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { createShippingMethod } from '@/lib/firebase/services/shipping-service';
import Link from 'next/link';

// Form validation schema
const shippingMethodSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  description: z.string().min(5, { message: 'Description must be at least 5 characters' }),
  price: z.coerce.number().min(0, { message: 'Price must be a positive number' }),
  minDeliveryDays: z.coerce.number().int().min(1, { message: 'Minimum delivery days must be at least 1' }),
  maxDeliveryDays: z.coerce.number().int().min(1, { message: 'Maximum delivery days must be at least 1' }),
  isActive: z.boolean(),
});

type ShippingMethodFormValues = z.infer<typeof shippingMethodSchema>;

export default function NewShippingMethodPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with default values
  const form = useForm<ShippingMethodFormValues>({
    resolver: zodResolver(shippingMethodSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      minDeliveryDays: 1,
      maxDeliveryDays: 5,
      isActive: true,
    },
  });

  // Handle form submission
  const onSubmit = async (values: ShippingMethodFormValues) => {
    // Validate that max days is greater than or equal to min days
    if (values.maxDeliveryDays < values.minDeliveryDays) {
      form.setError('maxDeliveryDays', {
        type: 'manual',
        message: 'Maximum delivery days must be greater than or equal to minimum delivery days',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create shipping method in Firestore
      await createShippingMethod({
        name: values.name,
        description: values.description,
        price: values.price,
        estimatedDeliveryDays: {
          min: values.minDeliveryDays,
          max: values.maxDeliveryDays,
        },
        isActive: values.isActive,
      });

      toast({
        title: 'Success',
        description: 'Shipping method created successfully',
      });

      // Redirect to shipping methods list
      router.push('/admin/shipping/methods');
    } catch (error) {
      console.error('Error creating shipping method:', error);
      toast({
        title: 'Error',
        description: 'Failed to create shipping method. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Add Shipping Method</h1>
            <p className="text-muted-foreground">
              Create a new shipping option for your customers
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/admin/shipping/methods">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Shipping Methods
            </Link>
          </Button>
        </div>

        <div className="border rounded-lg p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid gap-6 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Standard Shipping" {...field} />
                      </FormControl>
                      <FormDescription>
                        The name of the shipping method shown to customers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <span className="absolute left-3 top-2.5">$</span>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="5.99"
                            className="pl-7"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        The cost of this shipping method
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Delivery within 3-5 business days"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A brief description of the shipping method
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-6 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="minDeliveryDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Delivery Days</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" {...field} />
                      </FormControl>
                      <FormDescription>
                        Minimum estimated delivery time in days
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxDeliveryDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Delivery Days</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" {...field} />
                      </FormControl>
                      <FormDescription>
                        Maximum estimated delivery time in days
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Make this shipping method available to customers
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Create Shipping Method
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </AdminLayout>
  );
}
