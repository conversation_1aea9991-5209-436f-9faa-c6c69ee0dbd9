'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { uploadImage } from '@/lib/utils/image-upload';
import { addCategory } from '@/lib/firebase/services/category-service';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, ArrowLeft, Upload, X } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';

// Form schema
const categorySchema = z.object({
  name: z.string().min(2, { message: 'Category name is required' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  slug: z.string().min(2, { message: 'Slug is required' })
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
      message: 'Slug must contain only lowercase letters, numbers, and hyphens'
    }),
});

type CategoryFormValues = z.infer<typeof categorySchema>;

export default function NewCategoryPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [image, setImage] = useState<{ file: File; preview: string } | null>(null);

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      slug: '',
    },
  });

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/--+/g, '-')
      .trim();
  };

  // Update slug when name changes
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    form.setValue('name', name);

    // Only auto-generate slug if user hasn't manually edited it
    if (!form.getValues('slug') || form.getValues('slug') === generateSlug(form.getValues('name'))) {
      form.setValue('slug', generateSlug(name));
    }
  };

  const onSubmit = async (data: CategoryFormValues) => {
    setIsSubmitting(true);
    try {
      // Upload image first if there is one
      let imageUrl = '';
      if (image) {
        try {
          // Show toast for image upload
          toast({
            title: "Uploading Image",
            description: "Please wait while we upload your image...",
          });

          // Upload the image
          imageUrl = await uploadImage(image.file, 'categories');
          console.log('Uploaded image URL:', imageUrl);
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          toast({
            title: "Warning",
            description: "Failed to upload image. Using placeholder instead.",
            variant: "destructive",
          });
          // Use placeholder if image upload fails
          imageUrl = '/images/placeholder.png';
        }
      } else {
        // Use placeholder if no image is provided
        imageUrl = '/images/placeholder.png';
      }

      // Prepare form data for API call
      const categoryData = {
        ...data,
        // Generate slug from name if not provided
        slug: data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        // Add the image URL to the category data
        image: imageUrl,
        // Explicitly set isActive to true
        isActive: true
      };

      // Use the client SDK to create the category
      const categoryId = await addCategory(categoryData);
      console.log('Category created with ID:', categoryId);

      toast({
        title: "Success",
        description: "Category created successfully.",
        variant: "default",
      });

      router.push('/admin/categories');
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: "Error",
        description: "Failed to create category. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setImage({
        file,
        preview: URL.createObjectURL(file)
      });
    }
  };

  const removeImage = () => {
    if (image) {
      URL.revokeObjectURL(image.preview);
      setImage(null);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Add New Category</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => router.push('/admin/categories')}>
              Cancel
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Category
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <Card>
              <CardContent className="pt-6">
                <Form {...form}>
                  <form className="space-y-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter category name"
                              {...field}
                              onChange={handleNameChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="slug"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Slug</FormLabel>
                          <FormControl>
                            <Input placeholder="category-slug" {...field} />
                          </FormControl>
                          <FormDescription>
                            Used in URLs. Only lowercase letters, numbers, and hyphens.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter category description"
                              className="min-h-32"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Category Image</h3>

                <div className="space-y-4">
                  {image ? (
                    <div className="relative aspect-video rounded-md overflow-hidden border bg-muted">
                      <img
                        src={image.preview}
                        alt="Category image"
                        className="h-full w-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute top-2 right-2 bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <label className="flex flex-col items-center justify-center aspect-video rounded-md border-2 border-dashed bg-muted/50 hover:bg-muted cursor-pointer">
                      <div className="flex flex-col items-center justify-center p-4 text-center">
                        <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                        <p className="text-sm font-medium">Upload Image</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          PNG, JPG or WEBP up to 5MB
                        </p>
                      </div>
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageUpload}
                      />
                    </label>
                  )}

                  <div className="text-sm text-muted-foreground">
                    <p>• Recommended size: 1200 x 800 pixels</p>
                    <p>• This image will be used on category pages and listings</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
