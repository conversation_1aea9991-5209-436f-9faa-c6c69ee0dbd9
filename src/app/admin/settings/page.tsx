// @ts-nocheck - Suppress TypeScript errors in this file due to complex form typing issues
'use client';

import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, Globe, Mail, Phone, MapPin, CreditCard } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
  getGeneralSettings,
  updateGeneralSettings,
  getPaymentSettings,
  updatePaymentSettings,
  getEmailSettings,
  updateEmailSettings
} from '@/lib/firebase/services/settings-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Form schemas
const generalSettingsSchema = z.object({
  storeName: z.string().min(2, { message: 'Store name is required' }),
  storeEmail: z.string().email({ message: 'Please enter a valid email address' }),
  storePhone: z.string().min(5, { message: 'Phone number is required' }),
  storeAddress: z.string().min(5, { message: 'Address is required' }),
  storeCity: z.string().min(2, { message: 'City is required' }),
  storeState: z.string().min(2, { message: 'State is required' }),
  storeZip: z.string().min(2, { message: 'ZIP/Postal code is required' }),
  storeCountry: z.string().min(2, { message: 'Country is required' }),
});

const bankAccountDetailsSchema = z.object({
  bankName: z.string().min(1, { message: 'Bank name is required' }),
  accountName: z.string().min(1, { message: 'Account name is required' }),
  accountNumber: z.string().min(1, { message: 'Account number is required' }),
  routingNumber: z.string().optional(),
  swiftCode: z.string().optional(),
  iban: z.string().optional(),
  bsb: z.string().optional(),
  sortCode: z.string().optional(),
  branchCode: z.string().optional(),
  currency: z.string().min(1, { message: 'Currency is required' }),
  instructions: z.string().optional(),
});

const paymentSettingsSchema = z.object({
  currencyCode: z.string().min(1, { message: 'Currency is required' }),
  currencySymbol: z.string().min(1, { message: 'Currency symbol is required' }),
  stripeEnabled: z.boolean().default(false),
  stripePublicKey: z.string().optional().default(''),
  stripeSecretKey: z.string().optional().default(''),
  paypalEnabled: z.boolean().default(false),
  paypalClientId: z.string().optional().default(''),
  paypalSecretKey: z.string().optional().default(''),
  codEnabled: z.boolean().default(true),
  manualPaymentEnabled: z.boolean().default(false),
  bankAccountDetails: bankAccountDetailsSchema.optional(),
  paymentInstructions: z.string().optional(),
});

const emailSettingsSchema = z.object({
  smtpHost: z.string().min(1, { message: 'SMTP host is required' }),
  smtpPort: z.coerce.number().int().positive({ message: 'SMTP port must be a positive number' }),
  smtpUser: z.string().min(1, { message: 'SMTP username is required' }),
  smtpPassword: z.string().min(1, { message: 'SMTP password is required' }),
  smtpFromEmail: z.string().email({ message: 'Please enter a valid email address' }),
  smtpFromName: z.string().min(1, { message: 'From name is required' }),
  sendOrderConfirmation: z.boolean().default(true),
  sendShippingNotification: z.boolean().default(true),
  sendDeliveryNotification: z.boolean().default(true),
});

type GeneralSettingsFormValues = z.infer<typeof generalSettingsSchema>;
type PaymentSettingsFormValues = z.infer<typeof paymentSettingsSchema>;
type EmailSettingsFormValues = z.infer<typeof emailSettingsSchema>;

// Default settings if none exist in the database
const defaultGeneralSettings: GeneralSettingsFormValues = {
  storeName: 'Belinda Home Decor',
  storeEmail: '<EMAIL>',
  storePhone: '+****************',
  storeAddress: '123 Main Street',
  storeCity: 'New York',
  storeState: 'NY',
  storeZip: '10001',
  storeCountry: 'United States',
};

const defaultPaymentSettings: PaymentSettingsFormValues = {
  currencyCode: 'USD',
  currencySymbol: '$',
  stripeEnabled: false,
  stripePublicKey: '',
  stripeSecretKey: '',
  paypalEnabled: false,
  paypalClientId: '',
  paypalSecretKey: '',
  codEnabled: true,
  manualPaymentEnabled: false,
  bankAccountDetails: {
    bankName: '',
    accountName: '',
    accountNumber: '',
    routingNumber: '',
    swiftCode: '',
    iban: '',
    bsb: '',
    sortCode: '',
    branchCode: '',
    currency: 'USD',
    instructions: '',
  },
  paymentInstructions: 'Please transfer the amount to the bank account details provided and include the order reference {orderReference} in your transfer description.',
};

const defaultEmailSettings: EmailSettingsFormValues = {
  smtpHost: 'smtp.example.com',
  smtpPort: 587,
  smtpUser: '',
  smtpPassword: '',
  smtpFromEmail: '<EMAIL>',
  smtpFromName: 'Belinda Home Decor',
  sendOrderConfirmation: true,
  sendShippingNotification: true,
  sendDeliveryNotification: true,
};

export default function AdminSettingsPage() {
  const { toast } = useToast();
  const [isGeneralSubmitting, setIsGeneralSubmitting] = useState(false);
  const [isPaymentSubmitting, setIsPaymentSubmitting] = useState(false);
  const [isEmailSubmitting, setIsEmailSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // General settings form
  const generalForm = useForm<GeneralSettingsFormValues>({
    resolver: zodResolver(generalSettingsSchema),
    defaultValues: defaultGeneralSettings,
  });

  // Payment settings form
  const paymentForm = useForm<PaymentSettingsFormValues>({
    // @ts-expect-error - Type issues with zodResolver and useForm
    resolver: zodResolver(paymentSettingsSchema),
    defaultValues: defaultPaymentSettings,
    mode: 'onBlur',
  });

  // Email settings form
  const emailForm = useForm<EmailSettingsFormValues>({
    // @ts-expect-error - Type issues with zodResolver and useForm
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: defaultEmailSettings,
    mode: 'onBlur',
  });

  // Load settings from Firestore
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);
      try {
        // Load general settings
        const generalData = await getGeneralSettings();
        if (generalData) {
          generalForm.reset({
            storeName: generalData.storeName,
            storeEmail: generalData.storeEmail,
            storePhone: generalData.storePhone,
            storeAddress: generalData.storeAddress,
            storeCity: generalData.storeCity,
            storeState: generalData.storeState,
            storeZip: generalData.storeZip,
            storeCountry: generalData.storeCountry,
          });
        }

        // Load payment settings
        const paymentData = await getPaymentSettings();
        if (paymentData) {
          paymentForm.reset({
            currencyCode: paymentData.currencyCode,
            currencySymbol: paymentData.currencySymbol,
            stripeEnabled: paymentData.stripeEnabled,
            stripePublicKey: paymentData.stripePublicKey ?? '',
            stripeSecretKey: paymentData.stripeSecretKey ?? '',
            paypalEnabled: paymentData.paypalEnabled,
            paypalClientId: paymentData.paypalClientId ?? '',
            paypalSecretKey: paymentData.paypalSecretKey ?? '',
            codEnabled: paymentData.codEnabled,
            manualPaymentEnabled: paymentData.manualPaymentEnabled ?? false,
            bankAccountDetails: paymentData.bankAccountDetails ?? {
              bankName: '',
              accountName: '',
              accountNumber: '',
              routingNumber: '',
              swiftCode: '',
              iban: '',
              bsb: '',
              sortCode: '',
              branchCode: '',
              currency: paymentData.currencyCode,
              instructions: '',
            },
            paymentInstructions: paymentData.paymentInstructions ?? 'Please transfer the amount to the bank account details provided and include the order reference {orderReference} in your transfer description.',
          });
        }

        // Load email settings
        const emailData = await getEmailSettings();
        if (emailData) {
          emailForm.reset({
            smtpHost: emailData.smtpHost,
            smtpPort: emailData.smtpPort,
            smtpUser: emailData.smtpUser,
            smtpPassword: emailData.smtpPassword,
            smtpFromEmail: emailData.smtpFromEmail,
            smtpFromName: emailData.smtpFromName,
            sendOrderConfirmation: emailData.sendOrderConfirmation,
            sendShippingNotification: emailData.sendShippingNotification,
            sendDeliveryNotification: emailData.sendDeliveryNotification,
          });
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: "Error",
          description: "Failed to load settings. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [generalForm, paymentForm, emailForm, toast]);

  const onGeneralSubmit = async (values: GeneralSettingsFormValues) => {
    setIsGeneralSubmitting(true);
    try {
      // Update settings in Firestore
      await updateGeneralSettings(values);

      toast({
        title: "Success",
        description: "General settings updated successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating general settings:', error);
      toast({
        title: "Error",
        description: "Failed to update general settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneralSubmitting(false);
    }
  };

  const onPaymentSubmit = async (values: PaymentSettingsFormValues) => {
    setIsPaymentSubmitting(true);
    try {
      // Update settings in Firestore
      await updatePaymentSettings(values);

      toast({
        title: "Success",
        description: "Payment settings updated successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating payment settings:', error);
      toast({
        title: "Error",
        description: "Failed to update payment settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsPaymentSubmitting(false);
    }
  };

  const onEmailSubmit = async (values: EmailSettingsFormValues) => {
    setIsEmailSubmitting(true);
    try {
      // Update settings in Firestore
      await updateEmailSettings(values);

      toast({
        title: "Success",
        description: "Email settings updated successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating email settings:', error);
      toast({
        title: "Error",
        description: "Failed to update email settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsEmailSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Store Settings</h1>
          <p className="text-muted-foreground">
            Manage your store settings and configurations
          </p>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="payment">Payment</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
          </TabsList>

          {isLoading && (
            <div className="flex justify-center items-center py-16">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading settings...</span>
            </div>
          )}

          {/* General Settings */}
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure your store&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...generalForm}>
                  <form onSubmit={generalForm.handleSubmit(onGeneralSubmit)} className="space-y-6">
                    <div className="grid gap-6 sm:grid-cols-2">
                      <div className="sm:col-span-2">
                        <FormField
                          control={generalForm.control}
                          name="storeName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Store Name</FormLabel>
                              <FormControl>
                                <div className="flex items-center">
                                  <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <Input {...field} />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={generalForm.control}
                        name="storeEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Address</FormLabel>
                            <FormControl>
                              <div className="flex items-center">
                                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                                <Input {...field} />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={generalForm.control}
                        name="storePhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <div className="flex items-center">
                                <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                                <Input {...field} />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="sm:col-span-2">
                        <FormField
                          control={generalForm.control}
                          name="storeAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Address</FormLabel>
                              <FormControl>
                                <div className="flex items-center">
                                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <Input {...field} />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={generalForm.control}
                        name="storeCity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={generalForm.control}
                        name="storeState"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>State/Province</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={generalForm.control}
                        name="storeZip"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>ZIP/Postal Code</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={generalForm.control}
                        name="storeCountry"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Country</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" disabled={isGeneralSubmitting}>
                        {isGeneralSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payment Settings */}
          <TabsContent value="payment">
            <Card>
              <CardHeader>
                <CardTitle>Payment Settings</CardTitle>
                <CardDescription>
                  Configure payment methods and currency options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...paymentForm}>
                  <form onSubmit={paymentForm.handleSubmit((data) => onPaymentSubmit(data as unknown as PaymentSettingsFormValues))} className="space-y-6">
                    <div className="grid gap-6 sm:grid-cols-2">
                      <FormField
                        // @ts-expect-error - Type issues with form control
                        control={paymentForm.control}
                        name="currencyCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Currency</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select currency" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="USD">US Dollar (USD)</SelectItem>
                                <SelectItem value="EUR">Euro (EUR)</SelectItem>
                                <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                                <SelectItem value="CAD">Canadian Dollar (CAD)</SelectItem>
                                <SelectItem value="AUD">Australian Dollar (AUD)</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={paymentForm.control}
                        name="currencySymbol"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Currency Symbol</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Payment Methods</h3>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <CreditCard className="h-5 w-5" />
                              <div>
                                <h4 className="font-medium">Stripe</h4>
                                <p className="text-sm text-muted-foreground">
                                  Accept credit card payments via Stripe
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={paymentForm.control}
                              name="stripeEnabled"
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          {paymentForm.watch('stripeEnabled') && (
                            <div className="mt-4 grid gap-4 sm:grid-cols-2">
                              <FormField
                                control={paymentForm.control}
                                name="stripePublicKey"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Public Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} value={field.value ?? ''} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={paymentForm.control}
                                name="stripeSecretKey"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Secret Key</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="password"
                                        {...field}
                                        value={field.value ?? ''}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 19.5C8.32843 19.5 9 18.8284 9 18C9 17.1716 8.32843 16.5 7.5 16.5C6.67157 16.5 6 17.1716 6 18C6 18.8284 6.67157 19.5 7.5 19.5Z" fill="currentColor" />
                                <path d="M16.5 19.5C17.3284 19.5 18 18.8284 18 18C18 17.1716 17.3284 16.5 16.5 16.5C15.6716 16.5 15 17.1716 15 18C15 18.8284 15.6716 19.5 16.5 19.5Z" fill="currentColor" />
                                <path d="M3.96 5.13L3 3H5.5M3.96 5.13L5.5 3M3.96 5.13C4.43 6.4 5.5 9.5 5.5 9.5M5.5 3H21L19 14H6.64M5.5 9.5H19.64M5.5 9.5L6.64 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                              <div>
                                <h4 className="font-medium">Cash on Delivery</h4>
                                <p className="text-sm text-muted-foreground">
                                  Allow customers to pay when they receive their order
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={paymentForm.control}
                              name="codEnabled"
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7Z" stroke="currentColor" strokeWidth="2"/>
                                <path d="M3 7L12 13L21 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                              <div>
                                <h4 className="font-medium">Manual Bank Transfer</h4>
                                <p className="text-sm text-muted-foreground">
                                  Accept payments via manual bank transfer
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={paymentForm.control}
                              name="manualPaymentEnabled"
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          {paymentForm.watch('manualPaymentEnabled') && (
                            <div className="mt-6 space-y-4">
                              <h5 className="font-medium">Bank Account Details</h5>
                              <div className="grid gap-4 sm:grid-cols-2">
                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.bankName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Bank Name</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.accountName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Account Name</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.accountNumber"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Account Number</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.routingNumber"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Routing Number (US)</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.swiftCode"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>SWIFT Code</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={paymentForm.control}
                                  name="bankAccountDetails.iban"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>IBAN</FormLabel>
                                      <FormControl>
                                        <Input {...field} value={field.value ?? ''} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>

                              <FormField
                                control={paymentForm.control}
                                name="paymentInstructions"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Payment Instructions</FormLabel>
                                    <FormControl>
                                      <textarea
                                        className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="Enter payment instructions for customers. Use {orderReference} as a placeholder for the order reference."
                                        {...field}
                                        value={field.value ?? ''}
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      Use {'{orderReference}'} as a placeholder for the order reference number.
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" disabled={isPaymentSubmitting}>
                        {isPaymentSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Email Settings */}
          <TabsContent value="email">
            <Card>
              <CardHeader>
                <CardTitle>Email Settings</CardTitle>
                <CardDescription>
                  Configure email server and notification settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...emailForm}>
                  <form onSubmit={emailForm.handleSubmit((data) => onEmailSubmit(data as unknown as EmailSettingsFormValues))} className="space-y-6">
                    <div className="grid gap-6 sm:grid-cols-2">
                      <FormField
                        control={emailForm.control}
                        name="smtpHost"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMTP Host</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="smtpPort"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMTP Port</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="smtpUser"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMTP Username</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="smtpPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMTP Password</FormLabel>
                            <FormControl>
                              <Input type="password" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="smtpFromEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>From Email</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="smtpFromName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>From Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Email Notifications</h3>

                      <div className="space-y-2">
                        <FormField
                          control={emailForm.control}
                          name="sendOrderConfirmation"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Order Confirmation</FormLabel>
                                <FormDescription>
                                  Send email when a customer places an order
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={emailForm.control}
                          name="sendShippingNotification"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Shipping Notification</FormLabel>
                                <FormDescription>
                                  Send email when an order is shipped
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={emailForm.control}
                          name="sendDeliveryNotification"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Delivery Notification</FormLabel>
                                <FormDescription>
                                  Send email when an order is delivered
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" disabled={isEmailSubmitting}>
                        {isEmailSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
