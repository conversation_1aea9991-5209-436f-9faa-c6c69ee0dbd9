'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import {
  CheckCircle,
  XCircle,
  Clock,
  Mail,
  Eye,
  Filter,
  Search,
  CreditCard,
  AlertCircle,
} from 'lucide-react';

import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Order, PaymentStatus } from '@/types';
import { useAuth } from '@/contexts/auth-context';

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Helper function to format date
const formatDate = (date: Date | string) => {
  if (!date) return 'N/A';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'MMM d, yyyy h:mm a');
};

// Payment status badge component
const PaymentStatusBadge = ({ status }: { status: PaymentStatus }) => {
  const statusConfig = {
    pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: Clock },
    paid: { color: 'bg-green-100 text-green-800', label: 'Paid', icon: CheckCircle },
    failed: { color: 'bg-red-100 text-red-800', label: 'Failed', icon: XCircle },
    refunded: { color: 'bg-gray-100 text-gray-800', label: 'Refunded', icon: AlertCircle },
  };

  const config = statusConfig[status] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={`${config.color} border-0 flex items-center gap-1`}>
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

export default function PaymentManagementPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('pending');
  const { getIdToken } = useAuth();
  const router = useRouter();

  // Fetch orders
  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      const token = await getIdToken();
      
      const response = await fetch('/api/admin/orders', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }

      const data = await response.json();
      setOrders(data.orders || []);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter orders based on search and status
  useEffect(() => {
    let filtered = orders;

    // Filter by payment status based on active tab
    if (activeTab === 'pending') {
      filtered = filtered.filter(order => 
        order.paymentStatus === 'pending' && 
        (order.paymentMethod === 'manual_bank_transfer' || order.paymentMethod === 'bank_transfer')
      );
    } else if (activeTab === 'paid') {
      filtered = filtered.filter(order => order.paymentStatus === 'paid');
    } else if (activeTab === 'failed') {
      filtered = filtered.filter(order => order.paymentStatus === 'failed');
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${order.shippingAddress.firstName} ${order.shippingAddress.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  }, [orders, searchTerm, activeTab]);

  // Handle marking payment as received
  const handleMarkPaid = async (orderId: string) => {
    try {
      const token = await getIdToken();
      
      const response = await fetch(`/api/admin/orders/${orderId}/mark-paid`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark payment as received');
      }

      toast.success('Payment marked as received');
      fetchOrders(); // Refresh the list
    } catch (error) {
      console.error('Error marking payment as received:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to mark payment as received');
    }
  };

  // Handle marking payment as failed
  const handleMarkFailed = async (orderId: string) => {
    try {
      const token = await getIdToken();
      
      const response = await fetch(`/api/admin/orders/${orderId}/mark-paid`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark payment as failed');
      }

      toast.success('Payment marked as failed');
      fetchOrders(); // Refresh the list
    } catch (error) {
      console.error('Error marking payment as failed:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to mark payment as failed');
    }
  };

  // Load orders on component mount
  useEffect(() => {
    fetchOrders();
  }, []);

  // Calculate stats
  const pendingCount = orders.filter(order => 
    order.paymentStatus === 'pending' && 
    (order.paymentMethod === 'manual_bank_transfer' || order.paymentMethod === 'bank_transfer')
  ).length;
  
  const pendingAmount = orders
    .filter(order => 
      order.paymentStatus === 'pending' && 
      (order.paymentMethod === 'manual_bank_transfer' || order.paymentMethod === 'bank_transfer')
    )
    .reduce((sum, order) => sum + order.total, 0);

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payment Management</h1>
          <p className="text-muted-foreground">
            Manage manual payment verification and track payment status
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingCount}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(pendingAmount)} total value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Verified Today</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {orders.filter(order => 
                  order.paymentStatus === 'paid' && 
                  order.paidAt && 
                  new Date(order.paidAt).toDateString() === new Date().toDateString()
                ).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Payments verified today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Payments</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {orders.filter(order => order.paymentStatus === 'failed').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Orders</CardTitle>
            <CardDescription>
              View and manage payment status for manual bank transfer orders
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by order ID, email, or customer name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button onClick={fetchOrders} variant="outline">
                Refresh
              </Button>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="pending">
                  Pending ({pendingCount})
                </TabsTrigger>
                <TabsTrigger value="paid">Paid</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-4">
                {isLoading ? (
                  <div className="text-center py-8">
                    <p>Loading orders...</p>
                  </div>
                ) : filteredOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No orders found</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Order</TableHead>
                          <TableHead>Customer</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredOrders.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium">#{order.id.slice(-8)}</p>
                                <p className="text-sm text-muted-foreground">
                                  {order.paymentMethod}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium">
                                  {order.shippingAddress.firstName} {order.shippingAddress.lastName}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {order.email}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <p className="font-medium">{formatCurrency(order.total)}</p>
                            </TableCell>
                            <TableCell>
                              <p>{formatDate(order.createdAt)}</p>
                              {order.paidAt && (
                                <p className="text-sm text-muted-foreground">
                                  Paid: {formatDate(order.paidAt)}
                                </p>
                              )}
                            </TableCell>
                            <TableCell>
                              <PaymentStatusBadge status={order.paymentStatus ?? 'pending'} />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/admin/orders/${order.id}`)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                
                                {order.paymentStatus === 'pending' && (
                                  <>
                                    <Button
                                      size="sm"
                                      onClick={() => handleMarkPaid(order.id)}
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      <CheckCircle className="h-4 w-4" />
                                    </Button>
                                    
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => handleMarkFailed(order.id)}
                                    >
                                      <XCircle className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
