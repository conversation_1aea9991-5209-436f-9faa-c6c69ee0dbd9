'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  MoreHorizontal,
  Loader2,
  Shield,
  ShieldAlert,
  Mail,
  CheckCircle,
  XCircle,
  Filter
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getAllUsers, setUserAsAdmin, User as AuthUser } from '@/lib/firebase/services/auth-service';

// Update the User interface to extend the AuthUser interface
interface User extends AuthUser {
  isAdmin: boolean;
  emailVerified: boolean;
  createdAt: Date;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [adminActionUser, setAdminActionUser] = useState<{uid: string, name: string, makeAdmin: boolean} | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Fetch users
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const fetchedUsers = await getAllUsers();
        // Convert AuthUser[] to User[] by ensuring required properties
        const typedUsers: User[] = fetchedUsers.map(user => ({
          ...user,
          isAdmin: user.isAdmin === true,
          emailVerified: user.emailVerified === true,
          createdAt: user.createdAt || new Date(),
        }));
        setUsers(typedUsers);
      } catch (error) {
        console.error('Error fetching users:', error);
        toast({
          title: "Error",
          description: "Failed to load users. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [toast]);

  const handleAdminStatusChange = async () => {
    if (!adminActionUser) return;

    try {
      // First update the Firestore document
      await setUserAsAdmin(adminActionUser.uid, adminActionUser.makeAdmin);

      // Update local state
      setUsers(users.map(user =>
        user.uid === adminActionUser.uid
          ? {
              ...user,
              isAdmin: adminActionUser.makeAdmin,
              role: adminActionUser.makeAdmin ? 'admin' : 'customer'
            }
          : user
      ));

      toast({
        title: "Success",
        description: `User ${adminActionUser.makeAdmin ? 'promoted to' : 'removed from'} admin role.`,
        variant: "default",
      });
    } catch (error) {
      console.error('Error changing admin status:', error);
      toast({
        title: "Error",
        description: "Failed to update user role. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAdminActionUser(null);
    }
  };

  // Filter users based on search query and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.displayName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole =
      roleFilter === 'all' ||
      (roleFilter === 'admin' && user.isAdmin) ||
      (roleFilter === 'customer' && !user.isAdmin);

    const matchesVerification =
      verificationFilter === 'all' ||
      (verificationFilter === 'verified' && user.emailVerified) ||
      (verificationFilter === 'unverified' && !user.emailVerified);

    return matchesSearch && matchesRole && matchesVerification;
  });

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Users</h1>
          <p className="text-muted-foreground">
            Manage user accounts and permissions
          </p>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <div className="w-[150px]">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Admins</SelectItem>
                  <SelectItem value="customer">Customers</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-[150px]">
              <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Verification" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="unverified">Unverified</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Users Table */}
        {/* Loading state */}
        {loading && (
          <div className="flex justify-center items-center h-96">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}

        {/* No users found state */}
        {!loading && filteredUsers.length === 0 && (
          <div className="text-center py-12 bg-muted/30 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">No users found</h3>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search or filters
            </p>
          </div>
        )}

        {/* Users table state */}
        {!loading && filteredUsers.length > 0 && (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Verification</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.uid}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          {user.photoURL ? (
                            <AvatarImage src={user.photoURL} alt={user.displayName} />
                          ) : (
                            <AvatarFallback>
                              {user.displayName?.charAt(0) || user.email.charAt(0)}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <div className="font-medium">{user.displayName || 'Unnamed User'}</div>
                          <div className="text-xs text-muted-foreground truncate max-w-[150px]">
                            {user.uid}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{user.email}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {user.isAdmin ? (
                          <>
                            <ShieldAlert className="mr-1.5 h-4 w-4 text-amber-500" />
                            <span>Admin</span>
                          </>
                        ) : (
                          <>
                            <Shield className="mr-1.5 h-4 w-4 text-muted-foreground" />
                            <span>Customer</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.emailVerified ? (
                        <div className="flex items-center text-green-600">
                          <CheckCircle className="mr-1.5 h-4 w-4" />
                          <span>Verified</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-amber-600">
                          <XCircle className="mr-1.5 h-4 w-4" />
                          <span>Not verified</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {formatDate(user.createdAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <a href={`mailto:${user.email}`}>
                              <Mail className="mr-2 h-4 w-4" />
                              Email User
                            </a>
                          </DropdownMenuItem>
                          {user.isAdmin ? (
                            <DropdownMenuItem
                              onClick={() => setAdminActionUser({
                                uid: user.uid,
                                name: user.displayName || user.email,
                                makeAdmin: false
                              })}
                            >
                              <Shield className="mr-2 h-4 w-4" />
                              Remove Admin Role
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={() => setAdminActionUser({
                                uid: user.uid,
                                name: user.displayName || user.email,
                                makeAdmin: true
                              })}
                            >
                              <ShieldAlert className="mr-2 h-4 w-4" />
                              Make Admin
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Admin Role Change Confirmation Dialog */}
      <AlertDialog open={!!adminActionUser} onOpenChange={() => setAdminActionUser(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {adminActionUser?.makeAdmin
                ? 'Grant Admin Privileges?'
                : 'Remove Admin Privileges?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {adminActionUser?.makeAdmin
                ? `This will give ${adminActionUser?.name} full administrative access to the system.`
                : `This will remove ${adminActionUser?.name}'s administrative access to the system.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleAdminStatusChange}>
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
