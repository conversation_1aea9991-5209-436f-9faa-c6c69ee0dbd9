'use client';

import { useState, useEffect } from 'react';
import { getAdminOrders, AdminOrder } from '@/lib/firebase/services/admin-service';
import { updateOrderStatus } from '@/lib/firebase/services/order-service';
import Link from 'next/link';
import {
  Search,
  MoreHorizontal,
  Loader2,
  Calendar,
  X,
} from 'lucide-react';
import { format } from 'date-fns';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { OrderStatus } from '@/types';

// Using AdminOrder type from admin-service

// Define the badge variant types based on the Badge component
type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

// Status badge variants
const statusVariants: Record<OrderStatus, { variant: BadgeVariant, label: string }> = {
  pending: { variant: 'outline', label: 'Pending' },
  pending_payment: { variant: 'outline', label: 'Pending Payment' },
  processing: { variant: 'secondary', label: 'Processing' },
  shipped: { variant: 'default', label: 'Shipped' },
  delivered: { variant: 'default', label: 'Delivered' },
  cancelled: { variant: 'destructive', label: 'Cancelled' },
};

// Payment status badge variants
const paymentStatusVariants: Record<string, { variant: BadgeVariant, label: string }> = {
  pending: { variant: 'outline', label: 'Pending' },
  paid: { variant: 'default', label: 'Paid' }, // Changed from 'success' to 'default'
  failed: { variant: 'destructive', label: 'Failed' },
  refunded: { variant: 'secondary', label: 'Refunded' },
};

// Payment status badge component
const PaymentStatusBadge = ({ status }: { status: string }) => {
  // Default to 'pending' if status is not recognized
  const paymentStatus = status in paymentStatusVariants ? status : 'pending';
  const { variant, label } = paymentStatusVariants[paymentStatus];

  return (
    <Badge variant={variant}>
      {label}
    </Badge>
  );
};

export default function OrdersPage() {
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<AdminOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const { toast } = useToast();

  useEffect(() => {
    // Fetch orders
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const ordersData = await getAdminOrders();
        setOrders(ordersData);
        setFilteredOrders(ordersData);
      } catch (error) {
        console.error('Error fetching orders:', error);
        toast({
          title: "Error",
          description: "Failed to load orders. Please try again.",
          variant: "destructive",
        });
        setOrders([]);
        setFilteredOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [toast]);

  // Apply filters
  useEffect(() => {
    let result = [...orders];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        order =>
          order.id.toLowerCase().includes(query) ||
          order.customer.name.toLowerCase().includes(query) ||
          order.customer.email.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(order => order.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter) {
      const filterDate = format(dateFilter, 'yyyy-MM-dd');
      result = result.filter(
        order => format(order.date, 'yyyy-MM-dd') === filterDate
      );
    }

    setFilteredOrders(result);
  }, [orders, searchQuery, statusFilter, dateFilter]);

  const handleUpdateStatus = async (orderId: string, newStatus: OrderStatus) => {
    try {
      // Use Firebase client SDK directly to update the order status
      await updateOrderStatus(orderId, newStatus);

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );

      toast({
        title: "Status Updated",
        description: `Order ${orderId} status changed to ${newStatus.replace('_', ' ')}`,
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      toast({
        title: "Error",
        description: "Failed to update order status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
    setDateFilter(undefined);
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Orders</h1>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search orders..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={statusFilter}
          onValueChange={(value) => setStatusFilter(value as OrderStatus | 'all')}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="pending_payment">Pending Payment</SelectItem>
            <SelectItem value="processing">Processing</SelectItem>
            <SelectItem value="shipped">Shipped</SelectItem>
            <SelectItem value="delivered">Delivered</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={dateFilter ? "text-primary" : ""}
            >
              <Calendar className="mr-2 h-4 w-4" />
              {dateFilter ? format(dateFilter, 'PPP') : "Filter by date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <CalendarComponent
              mode="single"
              selected={dateFilter}
              onSelect={setDateFilter}
              disabled={(date) => date > new Date()}
            />
          </PopoverContent>
        </Popover>
        {(searchQuery || statusFilter !== 'all' || dateFilter) && (
          <Button variant="ghost" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear filters
          </Button>
        )}
      </div>

      {/* Orders Table */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Order ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead className="text-center">Items</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* Loading state */}
            {loading && (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                    Loading orders...
                  </div>
                </TableCell>
              </TableRow>
            )}

            {/* Empty state */}
            {!loading && filteredOrders.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No orders found.
                </TableCell>
              </TableRow>
            )}

            {/* Orders list */}
            {!loading && filteredOrders.length > 0 &&
              filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">
                    <Link href={`/admin/orders/${order.id}`} className="hover:text-primary transition-colors">
                      {order.id}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.customer.name}</div>
                      <div className="text-sm text-muted-foreground">{order.customer.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>{format(order.date, 'MMM d, yyyy')}</TableCell>
                  <TableCell className="text-right">${order.total.toFixed(2)}</TableCell>
                  <TableCell>
                    <Badge variant={statusVariants[order.status].variant}>
                      {statusVariants[order.status].label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <PaymentStatusBadge status={order.paymentStatus} />
                  </TableCell>
                  <TableCell className="text-center">{order.items}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/orders/${order.id}`}>View details</Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                        {order.status !== 'pending' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'pending')}>
                            Mark as Pending
                          </DropdownMenuItem>
                        )}
                        {order.status !== 'pending_payment' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'pending_payment')}>
                            Mark as Pending Payment
                          </DropdownMenuItem>
                        )}
                        {order.status !== 'processing' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'processing')}>
                            Mark as Processing
                          </DropdownMenuItem>
                        )}
                        {order.status !== 'shipped' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'shipped')}>
                            Mark as Shipped
                          </DropdownMenuItem>
                        )}
                        {order.status !== 'delivered' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'delivered')}>
                            Mark as Delivered
                          </DropdownMenuItem>
                        )}
                        {order.status !== 'cancelled' && (
                          <DropdownMenuItem onClick={() => handleUpdateStatus(order.id, 'cancelled')}>
                            Mark as Cancelled
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            }
          </TableBody>
        </Table>
      </div>
    </AdminLayout>
  );
}
