'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { uploadMultipleImages } from '@/lib/firebase/services/storage-service';
import { addProduct, checkSlugExists, generateUniqueSlug } from '@/lib/firebase/services/product-service';
import { getAdminCategories } from '@/lib/firebase/services/admin-service';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, ArrowLeft, Upload, X, AlertCircle } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';


// Form schema
const productSchema = z.object({
  name: z.string().min(2, { message: 'Product name is required' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  category: z.string().min(1, { message: 'Category is required' }),
  price: z.coerce.number().positive({ message: 'Price must be positive' }),
  compareAtPrice: z.coerce.number().nonnegative({ message: 'Compare at price must be positive' }).optional(),
  cost: z.coerce.number().nonnegative({ message: 'Cost must be positive' }).optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  stock: z.coerce.number().int({ message: 'Stock must be a whole number' }).nonnegative({ message: 'Stock must be positive' }),
  weight: z.coerce.number().nonnegative({ message: 'Weight must be positive' }).optional(),
  length: z.coerce.number().nonnegative({ message: 'Length must be positive' }).optional(),
  width: z.coerce.number().nonnegative({ message: 'Width must be positive' }).optional(),
  height: z.coerce.number().nonnegative({ message: 'Height must be positive' }).optional(),
  status: z.enum(['active', 'draft', 'archived']),
  taxable: z.boolean(),
  featured: z.boolean(),
  slug: z.string().optional(),
});

type ProductFormValues = z.infer<typeof productSchema>;

export default function NewProductPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<{ file: File; preview: string; id: string }[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [activeTab, setActiveTab] = useState('general');
  const [checkingSlug, setCheckingSlug] = useState(false);
  const [slugExists, setSlugExists] = useState(false);

  // Fetch categories from Firestore
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        const categoriesData = await getAdminCategories();
        if (categoriesData && categoriesData.length > 0) {
          setCategories(categoriesData.map(cat => ({ id: cat.id, name: cat.name })));
        } else {
          console.warn('No categories found in Firestore');
          toast({
            title: "Warning",
            description: "No categories found. Please create categories first.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast({
          title: "Error",
          description: "Failed to load categories. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, [toast]);

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      price: 0,
      compareAtPrice: undefined,
      cost: undefined,
      sku: '',
      barcode: '',
      stock: 0,
      weight: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      status: 'draft',
      taxable: true, // Required boolean field
      featured: false, // Required boolean field
      slug: '',
    } as ProductFormValues, // Type assertion to ensure compatibility
    mode: 'onChange',
  });

  // Handle slug validation
  const validateSlug = async (slug: string) => {
    if (!slug) return;

    setCheckingSlug(true);
    try {
      const exists = await checkSlugExists(slug);
      setSlugExists(exists);

      if (exists) {
        toast({
          title: "Warning",
          description: "This slug is already in use. Please choose a different one or generate a unique slug.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error checking slug:', error);
    } finally {
      setCheckingSlug(false);
    }
  };

  // Generate a unique slug
  const handleGenerateSlug = async () => {
    const productName = form.getValues('name');
    if (!productName) {
      toast({
        title: "Error",
        description: "Please enter a product name first",
        variant: "destructive",
      });
      return;
    }

    setCheckingSlug(true);
    try {
      const uniqueSlug = await generateUniqueSlug(productName);
      form.setValue('slug', uniqueSlug);
      setSlugExists(false);

      toast({
        title: "Success",
        description: "Generated a unique slug",
      });
    } catch (error) {
      console.error('Error generating slug:', error);
      toast({
        title: "Error",
        description: "Failed to generate a unique slug",
        variant: "destructive",
      });
    } finally {
      setCheckingSlug(false);
    }
  };

  const onSubmit = async (data: ProductFormValues) => {
    // Check if there are images
    if (images.length === 0) {
      toast({
        title: "Error",
        description: "At least one product image is required.",
        variant: "destructive",
      });
      setActiveTab('media');
      return;
    }

    // Validate slug if provided
    if (data.slug) {
      const exists = await checkSlugExists(data.slug);
      if (exists) {
        toast({
          title: "Error",
          description: "This slug is already in use. Please choose a different one or generate a unique slug.",
          variant: "destructive",
        });
        setActiveTab('general');
        return;
      }
    }

    setIsSubmitting(true);
    try {
      // Upload images first
      let imageUrls: string[] = [];
      try {
        // Extract the File objects from our image state
        const imageFiles = images.map(img => img.file);

        // Show toast for image upload
        toast({
          title: "Uploading Images",
          description: "Please wait while we upload your images...",
        });

        // Upload the images directly to Firebase Storage
        imageUrls = await uploadMultipleImages(imageFiles, 'products');
        console.log('Uploaded image URLs:', imageUrls);
      } catch (imageError) {
        console.error('Error uploading images:', imageError);
        toast({
          title: "Warning",
          description: "Failed to upload some images. Continuing with product creation.",
          variant: "destructive",
        });
        if (imageUrls.length === 0) {
          setIsSubmitting(false);
          setActiveTab('media');
          return;
        }
      }

      // Generate or use provided slug
      const slug = data.slug ?? await generateUniqueSlug(data.name);

      // Prepare product data
      const productData = {
        ...data,
        slug,
        images: imageUrls,
        categoryId: data.category, // Set categoryId to match the category ID
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Create the product directly using Firebase client SDK
      const productId = await addProduct(productData);
      console.log('Product created with ID:', productId);

      toast({
        title: "Success",
        description: `Product "${data.name}" created successfully with ${imageUrls.length} images.`,
        variant: "default",
      });

      router.push('/admin/products');
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        title: "Error",
        description: "Failed to create product. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files).map(file => ({
        file,
        preview: URL.createObjectURL(file),
        id: `img-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      }));

      setImages(prev => [...prev, ...newImages]);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => {
      const newImages = [...prev];
      URL.revokeObjectURL(newImages[index].preview);
      newImages.splice(index, 1);
      return newImages;
    });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Add New Product</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => router.push('/admin/products')}>
              Cancel
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Product
                </>
              )}
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="inventory">Inventory & Shipping</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form className="space-y-6">
              <TabsContent value="general" className="space-y-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid gap-6 sm:grid-cols-2">
                      <div className="sm:col-span-2">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Product Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter product name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="sm:col-span-2">
                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Enter product description"
                                  className="min-h-32"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={loadingCategories}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {categories.length > 0 ? (
                                  categories.map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))
                                ) : (
                                  <SelectItem value="no-categories" disabled>
                                    No categories found. Please create categories first.
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                            {categories.length === 0 && !loadingCategories && (
                              <FormDescription>
                                Create categories in Admin → Categories first
                              </FormDescription>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="archived">Archived</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="slug"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Slug</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <div className="relative flex-1">
                                  <Input
                                    placeholder="product-slug"
                                    {...field}
                                    className={slugExists ? "border-red-500 pr-10" : ""}
                                    onBlur={(e) => {
                                      field.onBlur();
                                      validateSlug(e.target.value);
                                    }}
                                  />
                                  {slugExists && (
                                    <div className="absolute right-3 top-2.5 text-red-500">
                                      ⚠️
                                    </div>
                                  )}
                                </div>
                              </FormControl>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={handleGenerateSlug}
                                disabled={checkingSlug}
                              >
                                {checkingSlug ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  "Generate"
                                )}
                              </Button>
                            </div>
                            <FormDescription>
                              URL-friendly identifier. {slugExists && (
                                <span className="text-red-500">This slug is already in use!</span>
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Pricing</h3>
                    <div className="grid gap-6 sm:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5">$</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="pl-7"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="compareAtPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Compare at Price</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5">$</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="pl-7"
                                  {...field}
                                  value={field.value ?? ''}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                    field.onChange(value);
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormDescription>
                              Original price for showing a discount
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="cost"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cost per item</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5">$</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="pl-7"
                                  {...field}
                                  value={field.value ?? ''}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                    field.onChange(value);
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormDescription>
                              Customers won&apos;t see this
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="taxable"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Charge tax on this product</FormLabel>
                              <FormDescription>
                                Apply standard tax rates to this product
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="featured"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Featured product</FormLabel>
                              <FormDescription>
                                Show this product on the homepage
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="inventory" className="space-y-6">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Inventory</h3>
                    <div className="grid gap-6 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="sku"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SKU (Stock Keeping Unit)</FormLabel>
                            <FormControl>
                              <Input placeholder="SKU-123" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="barcode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Barcode (ISBN, UPC, GTIN, etc.)</FormLabel>
                            <FormControl>
                              <Input placeholder="123456789012" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="stock"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Stock quantity</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="1"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Shipping</h3>
                    <div className="grid gap-6 sm:grid-cols-4">
                      <FormField
                        control={form.control}
                        name="weight"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (kg)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="length"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Length (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="width"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Width (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="height"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Height (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="media">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Product Images</h3>

                    {images.length === 0 && (
                      <div className="rounded-md bg-red-50 p-4 mb-4 border border-red-200">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                          <p className="text-sm text-red-700 font-medium">
                            At least one product image is required
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      {images.map((image, index) => (
                        <div key={image.id} className="relative aspect-square rounded-md overflow-hidden border bg-muted">
                          <Image
                            src={image.preview}
                            alt={`Product ${index + 1}`}
                            fill
                            sizes="(max-width: 768px) 50vw, 25vw"
                            className="object-cover"
                            unoptimized // Use unoptimized for blob URLs
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute top-1 right-1 bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}

                      {images.length < 10 && (
                        <label className="flex flex-col items-center justify-center aspect-square rounded-md border-2 border-dashed bg-muted/50 hover:bg-muted cursor-pointer">
                          <div className="flex flex-col items-center justify-center p-4 text-center">
                            <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                            <p className="text-sm font-medium">Upload Image</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              PNG, JPG or WEBP up to 5MB
                            </p>
                          </div>
                          <input
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleImageUpload}
                            multiple
                          />
                        </label>
                      )}
                    </div>

                    <div className="text-sm text-muted-foreground">
                      <p>• First image will be used as the product thumbnail</p>
                      <p>• You can upload up to 10 images per product</p>
                      <p className="font-medium text-red-600">• At least one image is required</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </form>
          </Form>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
