'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, ArrowLeft, Upload, X, Trash2 } from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import Image from 'next/image';
import { getProductById, updateProduct, checkSlugExists, generateUniqueSlug } from '@/lib/firebase/services/product-service';
import { getAdminCategories } from '@/lib/firebase/services/admin-service';
import { uploadMultipleImages } from '@/lib/firebase/services/storage-service';

// No fallback categories - we'll only use categories from Firestore

// Define variant schema with attributes matching ProductVariant type
const variantSchema = z.object({
  id: z.string(),
  name: z.string().min(1, { message: 'Variant name is required' }),
  price: z.coerce.number().positive({ message: 'Price must be positive' }).optional(),
  compareAtPrice: z.coerce.number().nonnegative({ message: 'Compare at price must be positive' }).optional(),
  stock: z.coerce.number().int().nonnegative().optional(),
  images: z.array(z.string()).optional(),
  attributes: z.record(z.string()),
});

// Form schema
const productSchema = z.object({
  name: z.string().min(2, { message: 'Product name is required' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  category: z.string().min(1, { message: 'Category is required' }),
  price: z.coerce.number().positive({ message: 'Price must be positive' }),
  compareAtPrice: z.coerce.number().nonnegative({ message: 'Compare at price must be positive' }).optional(),
  cost: z.coerce.number().nonnegative({ message: 'Cost must be positive' }).optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  stock: z.coerce.number().int({ message: 'Stock must be a whole number' }).nonnegative({ message: 'Stock must be positive' }),
  weight: z.coerce.number().nonnegative({ message: 'Weight must be positive' }).optional(),
  length: z.coerce.number().nonnegative({ message: 'Length must be positive' }).optional(),
  width: z.coerce.number().nonnegative({ message: 'Width must be positive' }).optional(),
  height: z.coerce.number().nonnegative({ message: 'Height must be positive' }).optional(),
  status: z.enum(['active', 'draft', 'archived']),
  taxable: z.boolean(),
  featured: z.boolean(),
  slug: z.string().optional(),
  images: z.array(z.string()).min(1, { message: 'At least one image is required' }),
  variants: z.array(variantSchema).optional(),
});

type ProductFormValues = z.infer<typeof productSchema>;

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;
  const { toast } = useToast();

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [images, setImages] = useState<{ file: File; preview: string }[]>([]);
  const [activeTab, setActiveTab] = useState('general');
  const [checkingSlug, setCheckingSlug] = useState(false);
  const [slugExists, setSlugExists] = useState(false);
  const [imagesToDelete, setImagesToDelete] = useState<string[]>([]);
  const [variantName, setVariantName] = useState('');
  const [variantPrice, setVariantPrice] = useState<number | undefined>(undefined);
  const [variantStock, setVariantStock] = useState<number | undefined>(undefined);
  const [variantAttributes, setVariantAttributes] = useState<Record<string, string>>({});
  const [attributeKey, setAttributeKey] = useState('');
  const [attributeValue, setAttributeValue] = useState('');

  // Initialize form with default values that match the schema
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      price: 0,
      compareAtPrice: undefined,
      cost: undefined,
      sku: '',
      barcode: '',
      stock: 0,
      weight: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      status: 'draft',
      taxable: true, // Required boolean field
      featured: false, // Required boolean field
      slug: '',
      images: [], // Will be populated from the product data
      variants: [], // Will be populated from the product data
    } as ProductFormValues, // Type assertion to ensure compatibility
  });

  // Fetch product data and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch product data
        const product = await getProductById(productId, true);
        if (!product) {
          toast({
            title: "Error",
            description: "Product not found",
            variant: "destructive",
          });
          router.push('/admin/products');
          return;
        }

        // Fetch categories
        try {
          const categoriesData = await getAdminCategories();
          if (categoriesData && categoriesData.length > 0) {
            setCategories(categoriesData.map(cat => ({ id: cat.id, name: cat.name })));
          } else {
            // No categories found in Firestore
            setCategories([]);
            console.warn('No categories found in Firestore');
            toast({
              title: "Warning",
              description: "No categories found. Please create categories first.",
              variant: "destructive",
            });
          }
        } catch (categoryError) {
          console.error('Error fetching categories:', categoryError);
          setCategories([]);
          toast({
            title: "Error",
            description: "Failed to load categories. Please refresh the page.",
            variant: "destructive",
          });
        }

        // Set form values
        form.reset({
          name: product.name,
          description: product.description,
          category: product.category,
          price: product.price,
          compareAtPrice: product.compareAtPrice,
          sku: product.sku || '',
          barcode: product.barcode || '',
          stock: product.stock || 0,
          status: product.status as 'active' | 'draft' | 'archived',
          featured: product.featured || false,
          slug: product.slug || '',
          images: product.images || [],
          variants: product.variants || [],
          // Add other fields as needed
        });
      } catch (error) {
        console.error('Error fetching product data:', error);
        toast({
          title: "Error",
          description: "Failed to load product data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [productId, router, toast, form]);

  const onSubmit = async (data: ProductFormValues) => {
    setSubmitting(true);
    try {
      // Validate slug if provided
      if (data.slug) {
        const exists = await checkSlugExists(data.slug, productId);
        if (exists) {
          toast({
            title: "Error",
            description: "This slug is already in use. Please choose a different one or generate a unique slug.",
            variant: "destructive",
          });
          setSubmitting(false);
          return;
        }
      }

      // Upload new images if there are any
      let newImageUrls: string[] = [];
      if (images.length > 0) {
        try {
          // Extract the File objects from our image state
          const imageFiles = images.map(img => img.file);

          // Show toast for image upload
          toast({
            title: "Uploading Images",
            description: "Please wait while we upload your images...",
          });

          // Upload the images directly to Firebase Storage
          newImageUrls = await uploadMultipleImages(imageFiles, 'products');
        } catch (imageError) {
          console.error('Error uploading images:', imageError);
          toast({
            title: "Warning",
            description: "Failed to upload some images. Continuing with product update.",
            variant: "destructive",
          });
        }
      }

      // Combine existing images with new ones
      const updatedImages = [...data.images, ...newImageUrls];

      // Ensure all variants have valid IDs and attributes
      const validatedVariants = data.variants?.map(variant => ({
        ...variant,
        id: variant.id || `variant-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        attributes: variant.attributes || {}
      }));

      // Prepare product data for update
      const productData = {
        ...data,
        images: updatedImages,
        variants: validatedVariants,
        // Ensure status is one of the valid values
        status: data.status || 'draft',
        // Ensure boolean fields are properly set
        taxable: data.taxable === undefined ? true : data.taxable,
        featured: data.featured === undefined ? false : data.featured
      };

      // Update the product with images to delete
      await updateProduct(productId, productData, undefined, imagesToDelete);

      toast({
        title: "Success",
        description: "Product updated successfully",
      });

      // Clear the image upload state
      setImages([]);
      setImagesToDelete([]);

      // Redirect back to products list
      router.push('/admin/products');
    } catch (error) {
      console.error('Error updating product:', error);
      toast({
        title: "Error",
        description: "Failed to update product. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files).map(file => ({
        file,
        preview: URL.createObjectURL(file)
      }));

      setImages(prev => [...prev, ...newImages]);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => {
      const newImages = [...prev];
      URL.revokeObjectURL(newImages[index].preview);
      newImages.splice(index, 1);
      return newImages;
    });
  };

  // Handle slug validation
  const validateSlug = async (slug: string) => {
    if (!slug) return;

    setCheckingSlug(true);
    try {
      const exists = await checkSlugExists(slug, productId);
      setSlugExists(exists);

      if (exists) {
        toast({
          title: "Warning",
          description: "This slug is already in use. Please choose a different one or generate a unique slug.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error checking slug:', error);
    } finally {
      setCheckingSlug(false);
    }
  };

  // Generate a unique slug
  const handleGenerateSlug = async () => {
    const productName = form.getValues('name');
    if (!productName) {
      toast({
        title: "Error",
        description: "Please enter a product name first",
        variant: "destructive",
      });
      return;
    }

    setCheckingSlug(true);
    try {
      const uniqueSlug = await generateUniqueSlug(productName, productId);
      form.setValue('slug', uniqueSlug);
      setSlugExists(false);

      toast({
        title: "Success",
        description: "Generated a unique slug",
      });
    } catch (error) {
      console.error('Error generating slug:', error);
      toast({
        title: "Error",
        description: "Failed to generate a unique slug",
        variant: "destructive",
      });
    } finally {
      setCheckingSlug(false);
    }
  };

  // Handle removing an existing product image
  const handleRemoveExistingImage = (url: string, index: number) => {
    // Add to list of images to delete
    setImagesToDelete(prev => [...prev, url]);

    // Remove from form values
    const currentImages = form.getValues('images');
    const updatedImages = [...currentImages];
    updatedImages.splice(index, 1);
    form.setValue('images', updatedImages);

    toast({
      title: "Image Removed",
      description: "The image will be deleted when you save the product",
    });
  };

  // Add attribute to variant
  const addAttribute = () => {
    if (!attributeKey || !attributeValue) {
      toast({
        title: "Error",
        description: "Both attribute name and value are required",
        variant: "destructive",
      });
      return;
    }

    setVariantAttributes(prev => ({
      ...prev,
      [attributeKey]: attributeValue
    }));

    // Clear inputs
    setAttributeKey('');
    setAttributeValue('');
  };

  // Remove attribute from variant
  const removeAttribute = (key: string) => {
    setVariantAttributes(prev => {
      const newAttributes = { ...prev };
      delete newAttributes[key];
      return newAttributes;
    });
  };

  // Add a new variant
  const addVariant = () => {
    if (!variantName) {
      toast({
        title: "Error",
        description: "Variant name is required",
        variant: "destructive",
      });
      return;
    }

    // Ensure we have at least one attribute
    if (Object.keys(variantAttributes).length === 0) {
      toast({
        title: "Error",
        description: "At least one attribute is required for a variant",
        variant: "destructive",
      });
      return;
    }

    // Create a valid variant with required fields
    const newVariant = {
      id: `variant-${Date.now()}`, // Generate a unique ID
      name: variantName,
      price: variantPrice,
      stock: variantStock,
      attributes: variantAttributes,
      images: [] // Initialize with empty array
    };

    const currentVariants = form.getValues('variants') || [];
    form.setValue('variants', [...currentVariants, newVariant]);

    // Reset variant form
    setVariantName('');
    setVariantPrice(undefined);
    setVariantStock(undefined);
    setVariantAttributes({});

    toast({
      title: "Success",
      description: "Variant added successfully",
    });
  };

  // Remove a variant
  const removeVariant = (index: number) => {
    const currentVariants = form.getValues('variants') || [];
    const updatedVariants = [...currentVariants];
    updatedVariants.splice(index, 1);
    form.setValue('variants', updatedVariants);

    toast({
      title: "Variant Removed",
      description: "The variant has been removed",
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => router.push('/admin/products')}>
              Cancel
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Product
                </>
              )}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="inventory">Inventory & Shipping</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="variants">Variants</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form className="space-y-6">
              <TabsContent value="general" className="space-y-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid gap-6 sm:grid-cols-2">
                      <div className="sm:col-span-2">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Product Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter product name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="sm:col-span-2">
                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Enter product description"
                                  className="min-h-32"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              value={field.value}
                              disabled={loading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {categories.length > 0 ? (
                                  categories.map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))
                                ) : (
                                  <SelectItem value="no-categories" disabled>
                                    No categories found. Please create categories first.
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                            {categories.length === 0 && !loading && (
                              <FormDescription>
                                Create categories in Admin → Categories first
                              </FormDescription>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="archived">Archived</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="slug"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Slug</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <div className="relative flex-1">
                                  <Input
                                    placeholder="product-slug"
                                    {...field}
                                    className={slugExists ? "border-red-500 pr-10" : ""}
                                    onBlur={(e) => {
                                      field.onBlur();
                                      validateSlug(e.target.value);
                                    }}
                                  />
                                  {slugExists && (
                                    <div className="absolute right-3 top-2.5 text-red-500">
                                      ⚠️
                                    </div>
                                  )}
                                </div>
                              </FormControl>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={handleGenerateSlug}
                                disabled={checkingSlug}
                              >
                                {checkingSlug ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  "Generate"
                                )}
                              </Button>
                            </div>
                            <FormDescription>
                              URL-friendly identifier. {slugExists && (
                                <span className="text-red-500">This slug is already in use!</span>
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Pricing</h3>
                    <div className="grid gap-6 sm:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5">$</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="pl-7"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="compareAtPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Compare at Price</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5">$</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="pl-7"
                                  {...field}
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                    field.onChange(value);
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormDescription>
                              Original price for showing a discount
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="featured"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Featured product</FormLabel>
                              <FormDescription>
                                Show this product on the homepage
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="inventory" className="space-y-6">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Inventory</h3>
                    <div className="grid gap-6 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="sku"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SKU (Stock Keeping Unit)</FormLabel>
                            <FormControl>
                              <Input placeholder="SKU-123" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="barcode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Barcode (ISBN, UPC, GTIN, etc.)</FormLabel>
                            <FormControl>
                              <Input placeholder="123456789012" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="stock"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Stock quantity</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="1"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Shipping</h3>
                    <div className="grid gap-6 sm:grid-cols-4">
                      <FormField
                        control={form.control}
                        name="weight"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (kg)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="length"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Length (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="width"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Width (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="height"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Height (cm)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="media">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Product Images</h3>

                    <FormField
                      control={form.control}
                      name="images"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              {field.value.map((url, index) => (
                                <div key={url} className="relative aspect-square overflow-hidden rounded-md border">
                                  <Image
                                    src={url}
                                    alt={`Product ${index + 1}`}
                                    fill
                                    sizes="(max-width: 768px) 50vw, 25vw"
                                    className="object-cover"
                                    unoptimized={url.startsWith('blob:')} // Skip optimization for blob URLs
                                  />
                                  <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity hover:bg-black/40 hover:opacity-100">
                                    <Button
                                      type="button"
                                      variant="destructive"
                                      size="icon"
                                      className="h-8 w-8"
                                      onClick={() => handleRemoveExistingImage(url, index)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </FormControl>
                          <FormDescription>
                            First image will be used as the product thumbnail.
                            {field.value.length === 0 && (
                              <span className="text-red-500 ml-1">At least one image is required.</span>
                            )}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">Add New Images</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        {images.map((image, index) => {
                          // Generate a stable key using the file name and size
                          const imageKey = `${image.file.name}-${image.file.size}-${index}`;
                          return (
                            <div key={imageKey} className="relative aspect-square rounded-md overflow-hidden border bg-muted">
                              <Image
                                src={image.preview}
                                alt={`Product ${index + 1}`}
                                fill
                                sizes="(max-width: 768px) 50vw, 25vw"
                                className="object-cover"
                                unoptimized // Use unoptimized for blob URLs
                              />
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="absolute top-1 right-1 bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          );
                        })}

                        <label className="flex flex-col items-center justify-center aspect-square rounded-md border-2 border-dashed bg-muted/50 hover:bg-muted cursor-pointer">
                          <div className="flex flex-col items-center justify-center p-4 text-center">
                            <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                            <p className="text-sm font-medium">Upload Image</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              PNG, JPG or WEBP up to 5MB
                            </p>
                          </div>
                          <input
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleImageUpload}
                            multiple
                          />
                        </label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="variants">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Product Variants</h3>

                    <div className="space-y-6">
                      {/* Current Variants */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">Current Variants</h4>
                        {form.watch('variants')?.length ? (
                          <div className="border rounded-md overflow-hidden">
                            <table className="w-full">
                              <thead className="bg-muted">
                                <tr>
                                  <th className="px-4 py-2 text-left">Name</th>
                                  <th className="px-4 py-2 text-left">Price</th>
                                  <th className="px-4 py-2 text-left">Stock</th>
                                  <th className="px-4 py-2 text-left">Attributes</th>
                                  <th className="px-4 py-2 text-right">Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                {form.watch('variants')?.map((variant, index) => (
                                  <tr key={variant.id || index} className="border-t">
                                    <td className="px-4 py-2">{variant.name}</td>
                                    <td className="px-4 py-2">
                                      {variant.price ? `$${variant.price.toFixed(2)}` : 'Default'}
                                    </td>
                                    <td className="px-4 py-2">
                                      {variant.stock !== undefined ? variant.stock : 'Default'}
                                    </td>
                                    <td className="px-4 py-2">
                                      {Object.entries(variant.attributes || {}).map(([key, value]) => (
                                        <span key={key} className="inline-flex items-center px-2 py-1 mr-1 mb-1 rounded-full text-xs bg-muted">
                                          {key}: {value}
                                        </span>
                                      ))}
                                    </td>
                                    <td className="px-4 py-2 text-right">
                                      <Button
                                        type="button"
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => removeVariant(index)}
                                      >
                                        <Trash2 className="h-4 w-4 mr-1" />
                                        Remove
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <div className="text-center py-8 border rounded-md bg-muted/30">
                            <p className="text-muted-foreground">No variants added yet</p>
                          </div>
                        )}
                      </div>

                      {/* Add New Variant */}
                      <div className="border rounded-md p-4">
                        <h4 className="text-sm font-medium mb-4">Add New Variant</h4>
                        <div className="space-y-4">
                          <div className="grid gap-4 sm:grid-cols-3">
                            <div>
                              <label htmlFor="variant-name" className="text-sm font-medium mb-1 block">Variant Name</label>
                              <Input
                                id="variant-name"
                                placeholder="e.g., Large / Red"
                                value={variantName}
                                onChange={(e) => setVariantName(e.target.value)}
                              />
                            </div>
                            <div>
                              <label htmlFor="variant-price" className="text-sm font-medium mb-1 block">Price (Optional)</label>
                              <Input
                                id="variant-price"
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="Override base price"
                                value={variantPrice === undefined ? '' : variantPrice}
                                onChange={(e) => setVariantPrice(e.target.value ? parseFloat(e.target.value) : undefined)}
                              />
                            </div>
                            <div>
                              <label htmlFor="variant-stock" className="text-sm font-medium mb-1 block">Stock (Optional)</label>
                              <Input
                                id="variant-stock"
                                type="number"
                                step="1"
                                min="0"
                                placeholder="Override base stock"
                                value={variantStock === undefined ? '' : variantStock}
                                onChange={(e) => setVariantStock(e.target.value ? parseInt(e.target.value) : undefined)}
                              />
                            </div>
                          </div>

                          <div>
                            <label htmlFor="attribute-key" className="text-sm font-medium mb-1 block">Attributes</label>
                            <div className="flex gap-2 mb-2">
                              <Input
                                id="attribute-key"
                                placeholder="Attribute name (e.g., Color)"
                                value={attributeKey}
                                onChange={(e) => setAttributeKey(e.target.value)}
                              />
                              <Input
                                id="attribute-value"
                                placeholder="Value (e.g., Red)"
                                value={attributeValue}
                                onChange={(e) => setAttributeValue(e.target.value)}
                              />
                              <Button type="button" onClick={addAttribute}>Add</Button>
                            </div>

                            <div className="flex flex-wrap gap-1 mt-2">
                              {Object.entries(variantAttributes).map(([key, value]) => (
                                <div key={key} className="flex items-center bg-muted px-2 py-1 rounded-full text-sm">
                                  <span>{key}: {value}</span>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 ml-1"
                                    onClick={() => removeAttribute(key)}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>

                          <Button type="button" onClick={addVariant}>
                            Add Variant
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-medium mb-4">Product Preview</h3>
                    <div className="border rounded-lg p-6">
                      <div className="grid md:grid-cols-2 gap-8">
                        {/* Product Images */}
                        <div className="space-y-4">
                          <div className="aspect-square relative rounded-lg overflow-hidden border bg-muted">
                            {form.watch('images').length > 0 ? (
                              <Image
                                src={form.watch('images')[0]}
                                alt={form.watch('name') || 'Product thumbnail'}
                                fill
                                sizes="(max-width: 768px) 100vw, 50vw"
                                className="object-cover"
                                unoptimized={form.watch('images')[0].startsWith('blob:')}
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full text-muted-foreground">
                                No image available
                              </div>
                            )}
                          </div>

                          {form.watch('images').length > 1 && (
                            <div className="grid grid-cols-4 gap-2">
                              {form.watch('images').slice(0, 4).map((image, i) => {
                                // Generate a stable key using the image URL
                                const imageKey = `preview-${image.replace(/[^a-zA-Z0-9]/g, '-')}-${i}`;
                                return (
                                  <div key={imageKey} className="aspect-square relative rounded-md overflow-hidden border bg-muted">
                                    <Image
                                      src={image}
                                      alt={`${form.watch('name') || 'Product'} thumbnail ${i + 1}`}
                                      fill
                                      sizes="(max-width: 768px) 25vw, 10vw"
                                      className="object-cover"
                                      unoptimized={image.startsWith('blob:')}
                                    />
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>

                        {/* Product Details */}
                        <div className="space-y-4">
                          <div>
                            <h1 className="text-2xl font-bold">{form.watch('name') || 'Product Name'}</h1>
                            <div className="flex items-center gap-2 mt-1">
                              <span className="text-xl font-semibold">${form.watch('price')?.toFixed(2) || '0.00'}</span>
                              {form.watch('compareAtPrice') && (
                                <span className="text-muted-foreground line-through">${form.watch('compareAtPrice')?.toFixed(2)}</span>
                              )}
                            </div>
                          </div>

                          <div className="prose prose-sm max-w-none">
                            <p>{form.watch('description') || 'Product description will appear here.'}</p>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">Status:</span>
                              {(() => {
                                const status = form.watch('status');
                                let bgColorClass = '';

                                if (status === 'active') {
                                  bgColorClass = 'bg-green-100 text-green-800';
                                } else if (status === 'draft') {
                                  bgColorClass = 'bg-yellow-100 text-yellow-800';
                                } else {
                                  bgColorClass = 'bg-gray-100 text-gray-800';
                                }

                                return (
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${bgColorClass}`}>
                                    {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Draft'}
                                  </span>
                                );
                              })()}
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="font-medium">Category:</span>
                              <span>{categories.find(c => c.id === form.watch('category'))?.name || 'Uncategorized'}</span>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="font-medium">Stock:</span>
                              <span>{form.watch('stock') || '0'} available</span>
                            </div>

                            {form.watch('sku') && (
                              <div className="flex items-center justify-between">
                                <span className="font-medium">SKU:</span>
                                <span>{form.watch('sku')}</span>
                              </div>
                            )}
                          </div>

                          <div className="pt-4">
                            <Button className="w-full">Add to Cart</Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 text-sm text-muted-foreground">
                      This is a preview of how your product will appear to customers. The actual appearance may vary slightly depending on the theme and layout of your store.
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </form>
          </Form>
        </Tabs>
      </div>
    </AdminLayout>
  );
}