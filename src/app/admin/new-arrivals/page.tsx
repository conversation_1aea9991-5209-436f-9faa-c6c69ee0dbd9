'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  getAllNewArrivals,
  deleteNewArrival,
  toggleNewArrivalActive,
  NewArrival as NewArrivalType
} from '@/lib/firebase/services/new-arrival-service';
import Image from 'next/image';
import {
  Plus,
  MoreHorizontal,
  Pencil,
  Trash2,
  Loader2,
  Eye,
  EyeOff,
  Calendar,
  ImageIcon,
  Package,
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { format } from 'date-fns';

// Use the imported type
type NewArrival = NewArrivalType;

export default function NewArrivalsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [newArrivals, setNewArrivals] = useState<NewArrival[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteNewArrivalId, setDeleteNewArrivalId] = useState<string | null>(null);
  const [toggleNewArrivalId, setToggleNewArrivalId] = useState<string | null>(null);
  const [isToggleActive, setIsToggleActive] = useState(false);

  // Define fetchNewArrivals with useCallback to prevent unnecessary re-renders
  const fetchNewArrivals = useCallback(async () => {
    try {
      setLoading(true);
      // Use the client-side Firebase SDK directly
      const newArrivalsData = await getAllNewArrivals();
      setNewArrivals(newArrivalsData);
    } catch (error) {
      console.error('Error fetching new arrivals:', error);
      toast({
        title: "Error",
        description: "Failed to load new arrivals. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Fetch new arrivals on component mount
  useEffect(() => {
    fetchNewArrivals();
  }, [fetchNewArrivals]);

  // Format date for display
  const formatDate = (date: Date) => {
    return format(date, 'MMM d, yyyy');
  };

  // Handle edit new arrival
  const handleEditNewArrival = (newArrival: NewArrival) => {
    router.push(`/admin/new-arrivals/${newArrival.id}`);
  };

  // Handle delete new arrival
  const handleDeleteNewArrival = async () => {
    if (!deleteNewArrivalId) return;

    try {
      // Use the client-side Firebase SDK directly
      await deleteNewArrival(deleteNewArrivalId);

      // Remove the deleted new arrival from the state
      setNewArrivals(newArrivals.filter(newArrival => newArrival.id !== deleteNewArrivalId));

      toast({
        title: "Success",
        description: "New arrival deleted successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error deleting new arrival:', error);
      toast({
        title: "Error",
        description: "Failed to delete new arrival. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleteNewArrivalId(null);
    }
  };

  // Handle toggle new arrival active status
  const handleToggleNewArrivalActive = async () => {
    if (!toggleNewArrivalId) return;

    try {
      // Use the client-side Firebase SDK directly
      await toggleNewArrivalActive(toggleNewArrivalId, isToggleActive);

      // Update the new arrival in the state
      setNewArrivals(newArrivals.map(newArrival =>
        newArrival.id === toggleNewArrivalId
          ? { ...newArrival, isActive: isToggleActive }
          : newArrival
      ));

      toast({
        title: "Success",
        description: `New arrival ${isToggleActive ? 'activated' : 'deactivated'} successfully.`,
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating new arrival status:', error);
      toast({
        title: "Error",
        description: "Failed to update new arrival status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setToggleNewArrivalId(null);
    }
  };

  // Check if a new arrival is currently active
  const isNewArrivalActive = (newArrival: NewArrival) => {
    const now = new Date();
    return (
      newArrival.isActive &&
      newArrival.startDate <= now &&
      (!newArrival.endDate || newArrival.endDate >= now)
    );
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">New Arrivals Management</h1>
        <Button asChild>
          <Link href="/admin/new-arrivals/new">
            <Plus className="mr-2 h-4 w-4" />
            Add New Arrival
          </Link>
        </Button>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      {/* Empty state */}
      {!loading && newArrivals.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 text-center">
            <ImageIcon className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No new arrivals found</h3>
            <p className="text-muted-foreground mb-4">
              Add your first new arrival to display on the new arrivals page.
            </p>
            <Button asChild>
              <Link href="/admin/new-arrivals/new">
                <Plus className="mr-2 h-4 w-4" />
                Add New Arrival
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Data table */}
      {!loading && newArrivals.length > 0 && (
        <div className="bg-white rounded-md shadow overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-24">Image</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Products</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {newArrivals.map((newArrival) => (
                <TableRow key={newArrival.id}>
                  <TableCell>
                    <div className="relative h-12 w-24 rounded overflow-hidden">
                      <Image
                        src={newArrival.image}
                        alt={newArrival.title}
                        fill
                        className="object-cover"
                        unoptimized
                      />
                    </div>
                  </TableCell>
                  <TableCell>{newArrival.title}</TableCell>
                  <TableCell className="max-w-xs truncate">{newArrival.description}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Package className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{newArrival.productIds.length} products</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      {formatDate(newArrival.startDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {newArrival.endDate ? (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        {formatDate(newArrival.endDate)}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No end date</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={isNewArrivalActive(newArrival) ? "default" : "secondary"}
                      className={isNewArrivalActive(newArrival) ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-gray-100 text-gray-800 hover:bg-gray-100"}
                    >
                      {isNewArrivalActive(newArrival) ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(newArrival.updatedAt)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEditNewArrival(newArrival)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setToggleNewArrivalId(newArrival.id);
                            setIsToggleActive(!newArrival.isActive);
                          }}
                        >
                          {newArrival.isActive ? (
                            <>
                              <EyeOff className="mr-2 h-4 w-4" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-4 w-4" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => setDeleteNewArrivalId(newArrival.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteNewArrivalId} onOpenChange={() => setDeleteNewArrivalId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the new arrival.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteNewArrival}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toggle Active Status Dialog */}
      <AlertDialog open={!!toggleNewArrivalId} onOpenChange={() => setToggleNewArrivalId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {isToggleActive ? 'Activate New Arrival' : 'Deactivate New Arrival'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {isToggleActive
                ? 'This will make the new arrival visible on the new arrivals page.'
                : 'This will hide the new arrival from the new arrivals page.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleToggleNewArrivalActive}>
              {isToggleActive ? 'Activate' : 'Deactivate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
