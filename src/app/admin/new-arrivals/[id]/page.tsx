'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import {
  ArrowLeft,
  Save,
  Loader2,
  Calendar,
  Upload,
  Image as ImageIcon,
  Package,
  X
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Product } from '@/types';
import { getNewArrivalById, createNewArrival, updateNewArrival, NewArrivalInput } from '@/lib/firebase/services/new-arrival-service';
import { getAdminProducts } from '@/lib/firebase/services/admin-service';
import { uploadImage } from '@/lib/firebase/services/storage-service';

// Define form schema
const newArrivalSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  image: z.string().url('Image must be a valid URL'),
  productIds: z.array(z.string()).min(1, 'At least one product is required'),
  isActive: z.boolean(),
  startDate: z.date(),
  endDate: z.date().nullable(),
});

type NewArrivalFormValues = z.infer<typeof newArrivalSchema>;

export default function NewArrivalPage() {
  const router = useRouter();
  const params = useParams();
  const { id } = params;
  const isNew = id === 'new';
  const { toast } = useToast();

  const [loading, setLoading] = useState(!isNew);
  const [submitting, setSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);

  // Initialize form
  const form = useForm<NewArrivalFormValues>({
    resolver: zodResolver(newArrivalSchema),
    defaultValues: {
      title: 'New Arrivals',
      description: 'Check out our latest products that just arrived!',
      image: '',
      productIds: [],
      isActive: true,
      startDate: new Date(),
      endDate: null,
    },
  });

  // Fetch products for selection
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoadingProducts(true);
        // Use Firebase client SDK directly
        const productsData = await getAdminProducts();
        // Convert to Product type
        const formattedProducts = productsData.map(p => ({
          id: p.id,
          name: p.name,
          description: '',
          price: p.price,
          compareAtPrice: 0,
          images: [p.image],
          category: p.category,
          categoryId: p.category,
          status: p.status,
          featured: false,
          stock: p.stock,
          sku: '',
          slug: p.slug
        } as Product));
        setProducts(formattedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast({
          title: "Error",
          description: "Failed to load products. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [toast]);

  // Fetch new arrival data if editing
  useEffect(() => {
    if (isNew) return;

    const fetchNewArrival = async () => {
      try {
        setLoading(true);
        // Use Firebase client SDK directly
        const newArrival = await getNewArrivalById(id as string);

        if (newArrival) {
          // Set form values
          form.reset({
            title: newArrival.title,
            description: newArrival.description,
            image: newArrival.image,
            productIds: newArrival.productIds || [],
            isActive: newArrival.isActive,
            startDate: newArrival.startDate,
            endDate: newArrival.endDate,
          });

          // Set image preview
          setImagePreview(newArrival.image);

          // Set selected products
          if (products.length > 0 && newArrival.productIds) {
            const selected = products.filter(product =>
              newArrival.productIds.includes(product.id)
            );
            setSelectedProducts(selected);
          }
        } else {
          throw new Error('New arrival not found');
        }
      } catch (error) {
        console.error('Error fetching new arrival:', error);
        toast({
          title: "Error",
          description: "Failed to load new arrival. Please try again.",
          variant: "destructive",
        });
        router.push('/admin/new-arrivals');
      } finally {
        setLoading(false);
      }
    };

    if (products.length > 0) {
      fetchNewArrival();
    }
  }, [id, isNew, router, toast, form, products]);

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Error",
        description: "Please upload an image file.",
        variant: "destructive",
      });
      return;
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Error",
        description: "Image size should be less than 5MB.",
        variant: "destructive",
      });
      return;
    }

    setUploadingImage(true);

    try {
      // Use Firebase Storage directly
      const imageUrl = await uploadImage(file, 'new-arrivals');

      // Set image URL in form
      form.setValue('image', imageUrl);
      setImagePreview(imageUrl);

      toast({
        title: "Success",
        description: "Image uploaded successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to upload image. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle product selection
  const handleProductSelect = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    // Add to selected products if not already selected
    if (!selectedProducts.some(p => p.id === productId)) {
      const updatedProducts = [...selectedProducts, product];
      setSelectedProducts(updatedProducts);

      // Update form value
      const productIds = updatedProducts.map(p => p.id);
      form.setValue('productIds', productIds);
    }
  };

  // Handle product removal
  const handleRemoveProduct = (productId: string) => {
    const updatedProducts = selectedProducts.filter(p => p.id !== productId);
    setSelectedProducts(updatedProducts);

    // Update form value
    const productIds = updatedProducts.map(p => p.id);
    form.setValue('productIds', productIds);
  };

  // Handle form submission
  const onSubmit = async (data: NewArrivalFormValues) => {
    setSubmitting(true);

    try {
      // Use Firebase client SDK directly
      if (isNew) {
        // Create new arrival
        const newArrivalData: NewArrivalInput = {
          title: data.title,
          description: data.description,
          image: data.image,
          productIds: data.productIds,
          isActive: data.isActive,
          startDate: data.startDate,
          endDate: data.endDate
        };

        await createNewArrival(newArrivalData);

        toast({
          title: "Success",
          description: "New arrival created successfully.",
          variant: "default",
        });
      } else {
        // Update existing new arrival
        await updateNewArrival(id as string, {
          title: data.title,
          description: data.description,
          image: data.image,
          productIds: data.productIds,
          isActive: data.isActive,
          startDate: data.startDate,
          endDate: data.endDate
        });

        toast({
          title: "Success",
          description: "New arrival updated successfully.",
          variant: "default",
        });
      }

      router.push('/admin/new-arrivals');
    } catch (error) {
      console.error('Error saving new arrival:', error);
      const errorMessage = error instanceof Error ? error.message : `Failed to ${isNew ? 'create' : 'update'} new arrival. Please try again.`;
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push('/admin/new-arrivals')} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isNew ? 'Add New Arrival' : 'Edit New Arrival'}
          </h1>
        </div>
        <Button
          type="submit"
          form="new-arrival-form"
          disabled={submitting}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save
            </>
          )}
        </Button>
      </div>

      {loading || loadingProducts ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>New Arrival Details</CardTitle>
              <CardDescription>
                Enter the details for this new arrival.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form id="new-arrival-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="New Arrivals" />
                        </FormControl>
                        <FormDescription>
                          The main heading for the new arrivals page.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Check out our latest products that just arrived!"
                            className="min-h-[100px]"
                          />
                        </FormControl>
                        <FormDescription>
                          A brief description of the new arrivals.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="grid gap-4 grid-cols-2">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date("1900-01-01")}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            When the new arrival becomes active.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>End Date (Optional)</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>No end date</span>
                                  )}
                                  <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <div className="p-2 border-b">
                                <Button
                                  variant="ghost"
                                  className="w-full justify-start text-left font-normal"
                                  onClick={() => field.onChange(null)}
                                >
                                  No end date
                                </Button>
                              </div>
                              <CalendarComponent
                                mode="single"
                                selected={field.value || undefined}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01") ||
                                  (form.getValues("startDate") && date < form.getValues("startDate"))
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            When the new arrival expires. Leave empty for no expiration.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Status</FormLabel>
                          <FormDescription>
                            Make this new arrival visible on the website.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Banner Image URL</FormLabel>
                        <FormControl>
                          <div className="flex gap-2">
                            <Input
                              {...field}
                              placeholder="https://example.com/image.jpg"
                              onChange={(e) => {
                                field.onChange(e);
                                setImagePreview(e.target.value);
                              }}
                            />
                            <div className="relative">
                              <Input
                                type="file"
                                accept="image/*"
                                className="absolute inset-0 opacity-0 cursor-pointer"
                                onChange={handleImageUpload}
                                disabled={uploadingImage}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                className="h-10"
                                disabled={uploadingImage}
                              >
                                {uploadingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Upload className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Enter an image URL or upload an image for the banner.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="productIds"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Products</FormLabel>
                        <FormControl>
                          <div className="space-y-4">
                            <Select
                              onValueChange={handleProductSelect}
                              value=""
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select products to feature" />
                              </SelectTrigger>
                              <SelectContent>
                                {products
                                  .filter(product => !selectedProducts.some(p => p.id === product.id))
                                  .map(product => (
                                    <SelectItem key={product.id} value={product.id}>
                                      {product.name}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>

                            <div className="border rounded-md p-4">
                              <div className="text-sm font-medium mb-2">Selected Products ({selectedProducts.length})</div>
                              {selectedProducts.length === 0 ? (
                                <div className="text-sm text-muted-foreground">
                                  No products selected. Please select at least one product.
                                </div>
                              ) : (
                                <div className="flex flex-wrap gap-2">
                                  {selectedProducts.map(product => (
                                    <Badge key={product.id} variant="secondary" className="flex items-center gap-1">
                                      {product.name}
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="h-4 w-4 p-0 ml-1"
                                        onClick={() => handleRemoveProduct(product.id)}
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                            <input
                              type="hidden"
                              {...field}
                              value={field.value.join(',')}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Select the products to display on the new arrivals page.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>
                Preview how the new arrival will appear on the new arrivals page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <div className="relative aspect-[21/9] w-full overflow-hidden bg-gray-200">
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="New Arrival Banner Preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <ImageIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black/30 flex flex-col justify-center items-start p-8">
                    <div className="max-w-xl text-white">
                      <h1 className="text-2xl font-bold mb-2">{form.watch('title')}</h1>
                      <p className="text-lg mb-4">{form.watch('description')}</p>
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="text-lg font-medium mb-2">Featured Products</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedProducts.slice(0, 4).map(product => (
                      <div key={product.id} className="border rounded p-2 flex items-center gap-2">
                        <div className="bg-gray-100 h-10 w-10 flex items-center justify-center rounded">
                          <Package className="h-5 w-5 text-gray-500" />
                        </div>
                        <div className="text-sm truncate">{product.name}</div>
                      </div>
                    ))}
                    {selectedProducts.length === 0 && (
                      <div className="col-span-2 text-center py-4 text-muted-foreground">
                        No products selected
                      </div>
                    )}
                    {selectedProducts.length > 4 && (
                      <div className="col-span-2 text-center text-sm text-muted-foreground">
                        +{selectedProducts.length - 4} more products
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <span className={`text-sm ${form.watch('isActive') ? 'text-green-600' : 'text-gray-500'}`}>
                    {form.watch('isActive') ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Start Date:</span>
                  <span className="text-sm">
                    {form.watch('startDate') ? format(form.watch('startDate'), 'PPP') : 'Not set'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">End Date:</span>
                  <span className="text-sm">
                    {form.watch('endDate') ? format(form.watch('endDate') as Date, 'PPP') : 'No end date'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Products:</span>
                  <span className="text-sm">{selectedProducts.length} selected</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </AdminLayout>
  );
}
