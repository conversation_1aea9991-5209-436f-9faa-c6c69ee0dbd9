'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  getAllSpecialOffers,
  deleteSpecialOffer,
  toggleSpecialOfferActive
} from '@/lib/firebase/services/special-offer-service';
import Image from 'next/image';
import Link from 'next/link';
import {
  Plus,
  MoreHorizontal,
  Pencil,
  Trash2,
  Loader2,
  Eye,
  EyeOff,
  Calendar,
  ImageIcon,
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { format } from 'date-fns';

// Define special offer type (same as in the service)
interface SpecialOffer {
  id: string;
  title: string;
  description: string;
  image: string;
  linkUrl: string;
  linkText: string;
  isActive: boolean;
  startDate: Date;
  endDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export default function SpecialOffersPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [offers, setOffers] = useState<SpecialOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteOfferId, setDeleteOfferId] = useState<string | null>(null);
  const [toggleOfferId, setToggleOfferId] = useState<string | null>(null);
  const [isToggleActive, setIsToggleActive] = useState(false);

  // Define fetchOffers with useCallback to avoid dependency issues
  const fetchOffers = useCallback(async () => {
    try {
      setLoading(true);
      // Use the client-side Firebase SDK directly
      const specialOffers = await getAllSpecialOffers();
      setOffers(specialOffers);
    } catch (error) {
      console.error('Error fetching special offers:', error);
      toast({
        title: "Error",
        description: "Failed to load special offers. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Fetch special offers on component mount
  useEffect(() => {
    fetchOffers();
  }, [fetchOffers]);

  // Format date for display
  const formatDate = (date: Date) => {
    return format(date, 'MMM d, yyyy');
  };

  // Handle edit offer
  const handleEditOffer = (offer: SpecialOffer) => {
    router.push(`/admin/special-offers/${offer.id}`);
  };

  // Handle delete offer
  const handleDeleteOffer = async () => {
    if (!deleteOfferId) return;

    try {
      // Use the client-side Firebase SDK directly
      await deleteSpecialOffer(deleteOfferId);

      // Remove the deleted offer from the state
      setOffers(offers.filter(offer => offer.id !== deleteOfferId));

      toast({
        title: "Success",
        description: "Special offer deleted successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error deleting special offer:', error);
      toast({
        title: "Error",
        description: "Failed to delete special offer. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleteOfferId(null);
    }
  };

  // Handle toggle offer active status
  const handleToggleOfferActive = async () => {
    if (!toggleOfferId) return;

    try {
      // Use the client-side Firebase SDK directly
      await toggleSpecialOfferActive(toggleOfferId, isToggleActive);

      // Update the offer in the state
      setOffers(offers.map(offer =>
        offer.id === toggleOfferId
          ? { ...offer, isActive: isToggleActive }
          : offer
      ));

      toast({
        title: "Success",
        description: `Special offer ${isToggleActive ? 'activated' : 'deactivated'} successfully.`,
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating special offer status:', error);
      toast({
        title: "Error",
        description: "Failed to update special offer status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setToggleOfferId(null);
    }
  };

  // Check if an offer is currently active
  const isOfferActive = (offer: SpecialOffer) => {
    const now = new Date();
    return (
      offer.isActive &&
      offer.startDate <= now &&
      (!offer.endDate || offer.endDate >= now)
    );
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Special Offers Management</h1>
        <Button asChild>
          <Link href="/admin/special-offers/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Offer
          </Link>
        </Button>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      {/* Empty state */}
      {!loading && offers.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 text-center">
            <ImageIcon className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No special offers found</h3>
            <p className="text-muted-foreground mb-4">
              Add your first special offer to display on the home page.
            </p>
            <Button asChild>
              <Link href="/admin/special-offers/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Offer
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Offers table */}
      {!loading && offers.length > 0 && (
        <div className="bg-white rounded-md shadow overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-24">Image</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Link</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {offers.map((offer) => (
                <TableRow key={offer.id}>
                  <TableCell>
                    <div className="relative h-12 w-24 rounded overflow-hidden">
                      <Image
                        src={offer.image}
                        alt={offer.title}
                        fill
                        className="object-cover"
                        unoptimized
                      />
                    </div>
                  </TableCell>
                  <TableCell>{offer.title}</TableCell>
                  <TableCell className="max-w-xs truncate">{offer.description}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    <span className="text-sm text-muted-foreground">{offer.linkText}</span>
                    <span className="block text-xs text-muted-foreground truncate">{offer.linkUrl}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      {formatDate(offer.startDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {offer.endDate ? (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        {formatDate(offer.endDate)}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No end date</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={isOfferActive(offer) ? "default" : "secondary"}
                      className={isOfferActive(offer) ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-gray-100 text-gray-800 hover:bg-gray-100"}
                    >
                      {isOfferActive(offer) ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(offer.updatedAt)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEditOffer(offer)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setToggleOfferId(offer.id);
                            setIsToggleActive(!offer.isActive);
                          }}
                        >
                          {offer.isActive ? (
                            <>
                              <EyeOff className="mr-2 h-4 w-4" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-4 w-4" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => setDeleteOfferId(offer.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteOfferId} onOpenChange={() => setDeleteOfferId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the special offer.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOffer}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toggle Active Status Dialog */}
      <AlertDialog open={!!toggleOfferId} onOpenChange={() => setToggleOfferId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {isToggleActive ? 'Activate Special Offer' : 'Deactivate Special Offer'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {isToggleActive
                ? 'This will make the special offer visible on the home page.'
                : 'This will hide the special offer from the home page.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleToggleOfferActive}>
              {isToggleActive ? 'Activate' : 'Deactivate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
