// @ts-nocheck - Suppress TypeScript errors in this file due to complex form typing issues
'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  getSpecialOfferById,
  createSpecialOffer,
  updateSpecialOffer
} from '@/lib/firebase/services/special-offer-service';
import { format } from 'date-fns';
import {
  ArrowLeft,
  Save,
  Loader2,
  Calendar,
  Upload,
  Image as ImageIcon
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

// Define form schema
const offerSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  image: z.string().url('Image must be a valid URL'),
  linkUrl: z.string().min(1, 'Link URL is required'),
  linkText: z.string().min(1, 'Link text is required'),
  isActive: z.boolean().default(true),
  startDate: z.date(),
  endDate: z.date().nullable(),
});

// Define form values type
type OfferFormValues = z.infer<typeof offerSchema>;

export default function SpecialOfferPage() {
  const router = useRouter();
  const params = useParams();
  const { id } = params;
  const isNew = id === 'new';
  const { toast } = useToast();

  const [loading, setLoading] = useState(!isNew);
  const [submitting, setSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  // Initialize form
  const form = useForm<OfferFormValues>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      title: 'Special Offer',
      description: 'Get 15% off on all products this month!',
      image: '',
      linkUrl: '/products',
      linkText: 'Shop Now',
      isActive: true,
      startDate: new Date(),
      endDate: null,
    },
  });

  // Fetch offer data if editing
  useEffect(() => {
    if (isNew) return;

    const fetchOffer = async () => {
      try {
        setLoading(true);
        // Use Firebase client SDK directly
        const offer = await getSpecialOfferById(id as string);

        if (offer) {
          // Set form values
          form.reset({
            title: offer.title,
            description: offer.description,
            image: offer.image,
            linkUrl: offer.linkUrl,
            linkText: offer.linkText,
            isActive: offer.isActive,
            startDate: new Date(offer.startDate),
            endDate: offer.endDate ? new Date(offer.endDate) : null,
          });

          // Set image preview
          setImagePreview(offer.image);
        } else {
          throw new Error('Special offer not found');
        }
      } catch (error) {
        console.error('Error fetching special offer:', error);
        toast({
          title: "Error",
          description: "Failed to load special offer. Please try again.",
          variant: "destructive",
        });
        router.push('/admin/special-offers');
      } finally {
        setLoading(false);
      }
    };

    fetchOffer();
  }, [id, isNew, router, toast, form]);

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type and size
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a JPEG, PNG, WebP, or GIF image.",
        variant: "destructive",
      });
      return;
    }

    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: "Image must be less than 5MB.",
        variant: "destructive",
      });
      return;
    }

    setUploadingImage(true);

    try {
      // Import Firebase storage functions
      const { ref, uploadBytesResumable, getDownloadURL } = await import('firebase/storage');
      const { storage } = await import('@/lib/firebase/services/storage-service');

      // Create a unique filename
      const filename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;

      // Create storage reference
      const storageRef = ref(storage, `special-offers/${filename}`);

      // Upload file
      const uploadTask = uploadBytesResumable(storageRef, file);

      // Wait for upload to complete
      const downloadURL = await new Promise<string>((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Track upload progress if needed
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            console.log(`Upload progress: ${progress}%`);
          },
          (error) => {
            // Handle upload error
            console.error('Error uploading image:', error);
            reject(error);
          },
          async () => {
            // Upload completed successfully, get download URL
            const url = await getDownloadURL(uploadTask.snapshot.ref);
            resolve(url);
          }
        );
      });

      // Set the image URL in the form
      setImagePreview(downloadURL);
      form.setValue('image', downloadURL);

      toast({
        title: "Success",
        description: "Image uploaded successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Error",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: OfferFormValues) => {
    setSubmitting(true);

    try {
      if (isNew) {
        // Create new special offer using Firebase client SDK
        await createSpecialOffer({
          title: data.title,
          description: data.description,
          image: data.image,
          linkUrl: data.linkUrl,
          linkText: data.linkText,
          isActive: data.isActive,
          startDate: data.startDate,
          endDate: data.endDate
        });
      } else {
        // Update existing special offer using Firebase client SDK
        await updateSpecialOffer(id as string, {
          title: data.title,
          description: data.description,
          image: data.image,
          linkUrl: data.linkUrl,
          linkText: data.linkText,
          isActive: data.isActive,
          startDate: data.startDate,
          endDate: data.endDate
        });
      }

      toast({
        title: "Success",
        description: `Special offer ${isNew ? 'created' : 'updated'} successfully.`,
        variant: "default",
      });
      router.push('/admin/special-offers');
    } catch (error) {
      console.error('Error saving special offer:', error);
      toast({
        title: "Error",
        description: `Failed to ${isNew ? 'create' : 'update'} special offer. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push('/admin/special-offers')} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isNew ? 'Add Special Offer' : 'Edit Special Offer'}
          </h1>
        </div>
        <Button
          type="submit"
          form="offer-form"
          disabled={submitting}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save
            </>
          )}
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Offer Details</CardTitle>
              <CardDescription>
                Enter the details for this special offer.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form id="offer-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Special Offer" />
                        </FormControl>
                        <FormDescription>
                          The main heading for the offer.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Get 15% off on all products this month!"
                            className="min-h-[100px]"
                          />
                        </FormControl>
                        <FormDescription>
                          A brief description of the offer.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 grid-cols-2">
                    <FormField
                      control={form.control}
                      name="linkText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Text</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Shop Now" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="linkUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Link</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="/products" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="grid gap-4 grid-cols-2">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date("1900-01-01")}
                                // initialFocus is deprecated
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            When the offer becomes active.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>End Date (Optional)</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>No end date</span>
                                  )}
                                  <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <div className="p-2 border-b">
                                <Button
                                  variant="ghost"
                                  className="w-full justify-start text-left font-normal"
                                  onClick={() => field.onChange(null)}
                                >
                                  No end date
                                </Button>
                              </div>
                              <CalendarComponent
                                mode="single"
                                selected={field.value || undefined}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01") ||
                                  (form.getValues("startDate") && date < form.getValues("startDate"))
                                }
                                // initialFocus is deprecated
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            When the offer expires. Leave empty for no expiration.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Status</FormLabel>
                          <FormDescription>
                            Make this offer visible on the website.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Image URL</FormLabel>
                        <FormControl>
                          <div className="flex gap-2">
                            <Input
                              {...field}
                              placeholder="https://example.com/image.jpg"
                              onChange={(e) => {
                                field.onChange(e);
                                setImagePreview(e.target.value);
                              }}
                            />
                            <div className="relative">
                              <Input
                                type="file"
                                accept="image/*"
                                className="absolute inset-0 opacity-0 cursor-pointer"
                                onChange={handleImageUpload}
                                disabled={uploadingImage}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                className="h-10"
                                disabled={uploadingImage}
                              >
                                {uploadingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Upload className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Enter an image URL or upload an image.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>
                Preview how the special offer will appear on the home page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-primary text-primary-foreground p-6">
                  <div className="flex flex-col md:flex-row items-center justify-between">
                    <div className="mb-4 md:mb-0 md:mr-8">
                      <h2 className="text-xl font-bold mb-2">{form.watch('title')}</h2>
                      <p className="mb-4">{form.watch('description')}</p>
                      <Button variant="secondary" size="sm">
                        {form.watch('linkText')}
                      </Button>
                    </div>
                    <div className="relative w-full md:w-1/2 aspect-video rounded-lg overflow-hidden bg-gray-200">
                      {imagePreview ? (
                        <img
                          src={imagePreview}
                          alt="Special Offer Preview"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <ImageIcon className="h-12 w-12 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <span className={`text-sm ${form.watch('isActive') ? 'text-green-600' : 'text-gray-500'}`}>
                    {form.watch('isActive') ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Start Date:</span>
                  <span className="text-sm">
                    {form.watch('startDate') ? format(form.watch('startDate'), 'PPP') : 'Not set'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">End Date:</span>
                  <span className="text-sm">
                    {form.watch('endDate') ? format(form.watch('endDate') as Date, 'PPP') : 'No end date'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Link:</span>
                  <span className="text-sm text-blue-600">{form.watch('linkUrl')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </AdminLayout>
  );
}
