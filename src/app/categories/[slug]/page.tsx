'use client';

import { useState, useEffect } from 'react';
import { getCategoryBySlug } from '@/lib/firebase/services/category-service';
import { getProductsByCategory } from '@/lib/firebase/services/product-service';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  Loader2,
  Filter,
  ChevronDown,
  ShoppingCart,
  Heart,
  SlidersHorizontal,
  X
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/components/ui/use-toast';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useAuth } from '@/contexts/auth-context';
import { Product } from '@/types';

// Category hook using Firestore
const useCategory = (slug: string) => {
  const [category, setCategory] = useState<{
    id: string;
    name: string;
    description: string;
    image: string;
  } | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        setLoading(true);

        // Get category data from Firestore
        const categoryData = await getCategoryBySlug(slug);

        if (!categoryData) {
          toast({
            title: 'Category Not Found',
            description: 'The requested category could not be found.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Get products for this category
        const categoryProducts = await getProductsByCategory(categoryData.id);

        // Add inStock property to products for filtering
        const productsWithStock = categoryProducts.map(product => ({
          ...product,
          inStock: product.stock ? product.stock > 0 : true
        }));

        setCategory({
          id: categoryData.id,
          name: categoryData.name,
          description: categoryData.description,
          image: categoryData.image || '/images/placeholder.png',
        });
        setProducts(productsWithStock);
      } catch (error) {
        console.error('Error fetching category:', error);
        toast({
          title: "Error",
          description: "Failed to load category. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [slug, toast]);

  return { category, products, loading };
};

export default function CategoryPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { category, products, loading } = useCategory(slug);
  const { addToCart } = useCart();
  const { addToWishlist, isInWishlist } = useWishlist();
  const { user } = useAuth();
  const { toast } = useToast();

  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState('all');
  const [inStockOnly, setInStockOnly] = useState(false);
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [addingToWishlist, setAddingToWishlist] = useState<string | null>(null);

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      // Filter by price range
      if (priceRange === 'under50' && product.price >= 50) return false;
      if (priceRange === '50to100' && (product.price < 50 || product.price > 100)) return false;
      if (priceRange === 'over100' && product.price <= 100) return false;

      // Filter by stock
      if (inStockOnly && (!product.stock || product.stock <= 0)) return false;

      return true;
    })
    .sort((a, b) => {
      // Sort products
      if (sortBy === 'featured') {
        // Extract nested ternary into if-else statements
        if (a.featured === b.featured) {
          return 0;
        } else if (a.featured) {
          return -1;
        } else {
          return 1;
        }
      } else if (sortBy === 'priceLow') {
        return a.price - b.price;
      } else if (sortBy === 'priceHigh') {
        return b.price - a.price;
      } else if (sortBy === 'newest') {
        // In a real app, you would sort by date
        return 0;
      }
      return 0;
    });

  const handleAddToCart = async (product: Product) => {
    if (addingToCart) return;

    setAddingToCart(product.id);
    try {
      // Call the actual cart service
      addToCart(product, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingToCart(null);
    }
  };

  const handleAddToWishlist = async (product: Product) => {
    if (addingToWishlist) return;

    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to add items to your wishlist.",
        variant: "destructive",
      });
      return;
    }

    setAddingToWishlist(product.id);
    try {
      // Call the actual wishlist service
      await addToWishlist(product);
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      // Error is already handled in the wishlist context
    } finally {
      setAddingToWishlist(null);
    }
  };

  return (
    <MainLayout>
      <div className="container py-8">
        {/* Loading state */}
        {loading && (
          <div className="flex justify-center items-center h-96">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}

        {/* Category not found state */}
        {!loading && !category && (
          <div className="text-center py-16">
            <h1 className="text-3xl font-bold mb-4">Category Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The category you&apos;re looking for doesn&apos;t exist.
            </p>
            <Button asChild>
              <Link href="/products">Browse All Products</Link>
            </Button>
          </div>
        )}

        {/* Category content */}
        {!loading && category && (
          <>
            {/* Category Header */}
            <div className="relative h-64 rounded-lg overflow-hidden mb-8">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover"
                unoptimized
              />
              <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                <div className="text-center text-white p-6">
                  <h1 className="text-4xl font-bold mb-2">{category.name}</h1>
                  <p className="max-w-2xl mx-auto">{category.description}</p>
                </div>
              </div>
            </div>

            {/* Filters and Sorting */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
              <div className="flex items-center gap-2">
                <p className="text-muted-foreground">
                  {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
                </p>
              </div>

              <div className="flex flex-wrap gap-2">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="sm:hidden">
                      <SlidersHorizontal className="h-4 w-4 mr-2" />
                      Filters
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left">
                    <SheetHeader>
                      <SheetTitle>Filters</SheetTitle>
                    </SheetHeader>
                    <div className="py-4">
                      <div className="space-y-6">
                        <div>
                          <h3 className="font-medium mb-4">Price Range</h3>
                          <RadioGroup
                            value={priceRange}
                            onValueChange={setPriceRange}
                            className="space-y-2"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="all" id="mobile-price-all" />
                              <Label htmlFor="mobile-price-all">All Prices</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="under50" id="mobile-price-under50" />
                              <Label htmlFor="mobile-price-under50">Under $50</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="50to100" id="mobile-price-50to100" />
                              <Label htmlFor="mobile-price-50to100">$50 to $100</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="over100" id="mobile-price-over100" />
                              <Label htmlFor="mobile-price-over100">Over $100</Label>
                            </div>
                          </RadioGroup>
                        </div>

                        <Separator />

                        <div>
                          <h3 className="font-medium mb-4">Availability</h3>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="mobile-in-stock"
                              checked={inStockOnly}
                              onCheckedChange={(checked) => setInStockOnly(checked as boolean)}
                            />
                            <Label htmlFor="mobile-in-stock">In Stock Only</Label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>

                <div className="hidden sm:flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className={priceRange !== 'all' ? 'bg-primary/10 border-primary/20' : ''}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Price
                    <ChevronDown className="h-4 w-4 ml-1" />
                    {priceRange !== 'all' && (
                      <X
                        className="h-4 w-4 ml-1 hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          setPriceRange('all');
                        }}
                      />
                    )}
                  </Button>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="in-stock"
                      checked={inStockOnly}
                      onCheckedChange={(checked) => setInStockOnly(checked as boolean)}
                    />
                    <Label htmlFor="in-stock">In Stock Only</Label>
                  </div>
                </div>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured">Featured</SelectItem>
                    <SelectItem value="priceLow">Price: Low to High</SelectItem>
                    <SelectItem value="priceHigh">Price: High to Low</SelectItem>
                    <SelectItem value="newest">Newest</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* No products found state */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-16 bg-muted/30 rounded-lg">
                <h2 className="text-2xl font-semibold mb-2">No products found</h2>
                <p className="text-muted-foreground mb-6">
                  Try adjusting your filters to find what you&apos;re looking for.
                </p>
                <Button onClick={() => {
                  setPriceRange('all');
                  setInStockOnly(false);
                }}>
                  Clear Filters
                </Button>
              </div>
            )}

            {/* Products grid */}
            {filteredProducts.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden">
                    <div className="relative aspect-square">
                      <Image
                        src={product.images[0]}
                        alt={product.name}
                        fill
                        className="object-cover"
                        unoptimized
                      />
                      <button
                        onClick={() => handleAddToWishlist(product)}
                        className={`absolute top-2 right-2 p-1.5 rounded-full ${isInWishlist(product.id) ? 'bg-primary/10 hover:bg-primary/20' : 'bg-white/80 hover:bg-white'}`}
                        aria-label="Add to wishlist"
                        disabled={addingToWishlist === product.id}
                      >
                        {addingToWishlist === product.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Heart className={`h-4 w-4 ${isInWishlist(product.id) ? 'text-primary' : 'text-gray-600 hover:text-red-500'}`} />
                        )}
                      </button>
                    </div>
                    <CardContent className="p-4">
                      <Link href={`/products/${product.slug || product.id}`} className="block">
                        <h3 className="font-medium mb-1 hover:text-primary transition-colors">
                          {product.name}
                        </h3>
                      </Link>
                      <p className="text-muted-foreground text-sm mb-2 line-clamp-2">
                        {product.description}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="font-semibold">${product.price.toFixed(2)}</span>
                        <Button
                          size="sm"
                          onClick={() => handleAddToCart(product)}
                          disabled={addingToCart === product.id || !product.stock || product.stock <= 0}
                        >
                          {/* Render button content based on state */}
                          {addingToCart === product.id && (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          )}
                          {!addingToCart && (!product.stock || product.stock <= 0) && (
                            'Out of Stock'
                          )}
                          {!addingToCart && product.stock && product.stock > 0 && (
                            <>
                              <ShoppingCart className="h-4 w-4 mr-1" />
                              Add
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}
