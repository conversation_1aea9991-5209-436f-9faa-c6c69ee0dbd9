import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllShippingMethods, 
  createShippingMethod 
} from '@/lib/firebase/services/shipping-service';
import { getAdminAuth } from '@/lib/firebase/admin';
import { z } from 'zod';

// Schema for validating shipping method creation
const shippingMethodSchema = z.object({
  name: z.string().min(2),
  description: z.string().min(5),
  price: z.number().min(0),
  estimatedDeliveryDays: z.object({
    min: z.number().int().min(1),
    max: z.number().int().min(1)
  }),
  isActive: z.boolean().default(true)
});

// GET all shipping methods
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const auth = getAdminAuth();
    
    try {
      const decodedToken = await auth.verifyIdToken(token);
      
      // Check if user has admin role
      const isAdmin = decodedToken.admin === true;
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Forbidden: Admin access required' },
          { status: 403 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const includeInactive = url.searchParams.get('includeInactive') === 'true';

    // Fetch shipping methods
    const methods = await getAllShippingMethods(includeInactive);
    
    return NextResponse.json({ methods });
  } catch (error) {
    console.error('Error fetching shipping methods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch shipping methods' },
      { status: 500 }
    );
  }
}

// POST a new shipping method
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const auth = getAdminAuth();
    
    try {
      const decodedToken = await auth.verifyIdToken(token);
      
      // Check if user has admin role
      const isAdmin = decodedToken.admin === true;
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Forbidden: Admin access required' },
          { status: 403 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    
    try {
      const validatedData = shippingMethodSchema.parse(body);
      
      // Create shipping method
      const id = await createShippingMethod(validatedData);
      
      return NextResponse.json({ id, success: true }, { status: 201 });
    } catch (validationError) {
      return NextResponse.json(
        { error: 'Invalid shipping method data', details: validationError },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating shipping method:', error);
    return NextResponse.json(
      { error: 'Failed to create shipping method' },
      { status: 500 }
    );
  }
}
