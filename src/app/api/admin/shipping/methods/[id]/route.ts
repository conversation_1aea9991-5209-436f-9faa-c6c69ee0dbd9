import { NextRequest, NextResponse } from 'next/server';
import { 
  getShippingMethodById, 
  updateShippingMethod,
  deleteShippingMethod
} from '@/lib/firebase/services/shipping-service';
import { getAdminAuth } from '@/lib/firebase/admin';
import { z } from 'zod';

// Schema for validating shipping method updates
const shippingMethodUpdateSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().min(5).optional(),
  price: z.number().min(0).optional(),
  estimatedDeliveryDays: z.object({
    min: z.number().int().min(1),
    max: z.number().int().min(1)
  }).optional(),
  isActive: z.boolean().optional()
});

// Verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { isAdmin: false, error: 'Unauthorized', status: 401 };
  }

  const token = authHeader.substring(7);
  const auth = getAdminAuth();
  
  try {
    const decodedToken = await auth.verifyIdToken(token);
    
    // Check if user has admin role
    const isAdmin = decodedToken.admin === true;
    if (!isAdmin) {
      return { isAdmin: false, error: 'Forbidden: Admin access required', status: 403 };
    }
    
    return { isAdmin: true, userId: decodedToken.uid };
  } catch (error) {
    return { isAdmin: false, error: 'Invalid authentication token', status: 401 };
  }
}

// GET a specific shipping method
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const id = (await params).id;
  try {
    // Verify admin authentication
    const authResult = await verifyAdmin(request);
    if (!authResult.isAdmin) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    // Fetch shipping method
    const method = await getShippingMethodById(id);
    
    if (!method) {
      return NextResponse.json(
        { error: 'Shipping method not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ method });
  } catch (error) {
    console.error(`Error fetching shipping method ${id}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch shipping method' },
      { status: 500 }
    );
  }
}

// PATCH (update) a shipping method
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const id = (await params).id;
  try {
    // Verify admin authentication
    const authResult = await verifyAdmin(request);
    if (!authResult.isAdmin) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    // Check if shipping method exists
    const existingMethod = await getShippingMethodById(id);
    if (!existingMethod) {
      return NextResponse.json(
        { error: 'Shipping method not found' },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    
    try {
      const validatedData = shippingMethodUpdateSchema.parse(body);
      
      // Update shipping method
      await updateShippingMethod(id, validatedData);
      
      return NextResponse.json({ success: true });
    } catch (validationError) {
      return NextResponse.json(
        { error: 'Invalid shipping method data', details: validationError },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error(`Error updating shipping method ${id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update shipping method' },
      { status: 500 }
    );
  }
}

// DELETE a shipping method
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const id = (await params).id;
  try {
    // Verify admin authentication
    const authResult = await verifyAdmin(request);
    if (!authResult.isAdmin) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    // Check if shipping method exists
    const existingMethod = await getShippingMethodById(id);
    if (!existingMethod) {
      return NextResponse.json(
        { error: 'Shipping method not found' },
        { status: 404 }
      );
    }

    // Delete shipping method
    await deleteShippingMethod(id);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error deleting shipping method ${id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete shipping method' },
      { status: 500 }
    );
  }
}
