import { NextRequest, NextResponse } from 'next/server';
import { updateOrder } from '@/lib/firebase/admin-services/order-service';
import { verifyAuthToken } from '@/lib/firebase/admin-auth';
import { getUserAdmin } from '@/lib/firebase/admin-services/user-service';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyAuthToken(token);
    
    // Verify admin privileges
    const user = await getUserAdmin(decodedToken.uid);
    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const orderId = params.id;
    
    // Parse request body for optional payment details
    const body = await request.json().catch(() => ({}));
    const { paymentReference, adminNotes } = body;

    // Update order to mark as paid
    const updateData: any = {
      paymentStatus: 'paid',
      status: 'processing', // Move to processing once payment is confirmed
      paidAt: new Date(),
    };

    // Add payment reference if provided
    if (paymentReference) {
      updateData.paymentReference = paymentReference;
    }

    // Add admin notes if provided
    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    await updateOrder(orderId, updateData);

    return NextResponse.json({
      success: true,
      message: 'Order marked as paid successfully',
      orderId,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error marking order as paid:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyAuthToken(token);
    
    // Verify admin privileges
    const user = await getUserAdmin(decodedToken.uid);
    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const orderId = params.id;
    
    // Parse request body for optional admin notes
    const body = await request.json().catch(() => ({}));
    const { adminNotes } = body;

    // Update order to mark payment as failed/cancelled
    const updateData: any = {
      paymentStatus: 'failed',
      status: 'cancelled',
    };

    // Add admin notes if provided
    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    await updateOrder(orderId, updateData);

    return NextResponse.json({
      success: true,
      message: 'Order payment marked as failed',
      orderId,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error marking order payment as failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
