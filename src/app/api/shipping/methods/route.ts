import { NextRequest, NextResponse } from 'next/server';
import { getAllShippingMethods } from '@/lib/firebase/services/shipping-service';

export async function GET(request: NextRequest) {
  try {
    // Fetch all active shipping methods
    const shippingMethods = await getAllShippingMethods();
    
    return NextResponse.json({ methods: shippingMethods });
  } catch (error) {
    console.error('Error fetching shipping methods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch shipping methods' },
      { status: 500 }
    );
  }
}
