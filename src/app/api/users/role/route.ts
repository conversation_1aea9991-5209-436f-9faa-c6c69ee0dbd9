import { NextRequest } from 'next/server';
import { z } from 'zod';
import { getAdminAuth, getAdminFirestore } from '@/lib/firebase/admin';
import { FieldValue } from 'firebase-admin/firestore';
import { handleApiError, createApiResponse } from '@/lib/api-utils';

// Schema for update role request
const updateRoleSchema = z.object({
  uid: z.string().min(1, 'User ID is required'),
  role: z.enum(['admin', 'editor', 'viewer', 'customer']),
});

export async function PUT(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = updateRoleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return handleApiError(
        { code: 'invalid-argument' },
        validationResult.error.message,
        400
      );
    }

    const { uid, role } = validationResult.data;
    
    // Get permissions for the role
    const permissions = role === 'admin' 
      ? [
          'read:products', 'write:products', 'delete:products',
          'read:categories', 'write:categories', 'delete:categories',
          'read:orders', 'write:orders', 'delete:orders',
          'read:users', 'write:users', 'delete:users',
          'read:settings', 'write:settings',
          'read:dashboard', 'read:reports',
          'admin'
        ]
      : role === 'editor'
      ? [
          'read:products', 'write:products',
          'read:categories', 'write:categories',
          'read:orders', 'write:orders',
          'read:users',
          'read:settings',
          'read:dashboard', 'read:reports'
        ]
      : role === 'viewer'
      ? [
          'read:products',
          'read:categories',
          'read:orders',
          'read:dashboard'
        ]
      : []; // customer has no special permissions

    // Update custom claims in Firebase Auth
    const auth = getAdminAuth();
    await auth.setCustomUserClaims(uid, {
      role,
      permissions,
      admin: role === 'admin',
      editor: role === 'editor' || role === 'admin',
      viewer: role === 'viewer' || role === 'editor' || role === 'admin'
    });

    // Update role in Firestore
    const db = getAdminFirestore();
    await db.collection('users').doc(uid).set({
      role,
      permissions,
      isAdmin: role === 'admin',
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });

    return createApiResponse(
      { uid, role },
      `User role updated to ${role}`
    );
  } catch (error) {
    return handleApiError(error, 'Failed to update user role');
  }
}
