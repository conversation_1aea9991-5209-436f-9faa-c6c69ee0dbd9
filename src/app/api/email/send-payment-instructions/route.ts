import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService } from '@/lib/email/email-service';
import { getOrderAdmin } from '@/lib/firebase/admin-services/order-service';
import { verifyAuthToken } from '@/lib/firebase/admin';

// Validation schema for email request
const emailRequestSchema = z.object({
  orderId: z.string().min(1),
  paymentInstructions: z.object({
    orderId: z.string(),
    orderReference: z.string(),
    bankAccountDetails: z.object({
      bankName: z.string(),
      accountName: z.string(),
      accountNumber: z.string(),
      routingNumber: z.string().optional(),
      swiftCode: z.string().optional(),
      iban: z.string().optional(),
      bsb: z.string().optional(),
      sortCode: z.string().optional(),
      branchCode: z.string().optional(),
      currency: z.string(),
      instructions: z.string().optional(),
    }),
    amount: z.number(),
    currency: z.string(),
    instructions: z.string(),
    dueDate: z.string().optional(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate the request body
    const body = await request.json();
    const validationResult = emailRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { orderId, paymentInstructions } = validationResult.data;

    // Get the order from Firestore
    const order = await getOrderAdmin(orderId);
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if order has an email address
    if (!order.email) {
      return NextResponse.json(
        { error: 'Order does not have an email address' },
        { status: 400 }
      );
    }

    // Convert date string to Date object if provided
    const processedPaymentInstructions = {
      ...paymentInstructions,
      dueDate: paymentInstructions.dueDate ? new Date(paymentInstructions.dueDate) : undefined,
    };

    // Send the payment instructions email
    await emailService.sendPaymentInstructions(order, processedPaymentInstructions);

    return NextResponse.json({
      success: true,
      message: 'Payment instructions email sent successfully',
      orderId,
      emailSentTo: order.email,
    });

  } catch (error) {
    console.error('Error sending payment instructions email:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Email settings not configured')) {
        return NextResponse.json(
          { error: 'Email service not configured. Please configure SMTP settings in admin panel.' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}

// Admin endpoint to resend payment instructions
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication and admin privileges
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyAuthToken(token);
    
    // For admin endpoint, we could add additional admin verification here
    // For now, any authenticated user can resend emails

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = emailRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { orderId, paymentInstructions } = validationResult.data;

    // Get the order from Firestore
    const order = await getOrderAdmin(orderId);
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if order has an email address
    if (!order.email) {
      return NextResponse.json(
        { error: 'Order does not have an email address' },
        { status: 400 }
      );
    }

    // Convert date string to Date object if provided
    const processedPaymentInstructions = {
      ...paymentInstructions,
      dueDate: paymentInstructions.dueDate ? new Date(paymentInstructions.dueDate) : undefined,
    };

    // Send the payment instructions email
    await emailService.sendPaymentInstructions(order, processedPaymentInstructions);

    return NextResponse.json({
      success: true,
      message: 'Payment instructions email resent successfully',
      orderId,
      emailSentTo: order.email,
      resentBy: decodedToken.uid,
    });

  } catch (error) {
    console.error('Error resending payment instructions email:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Email settings not configured')) {
        return NextResponse.json(
          { error: 'Email service not configured. Please configure SMTP settings in admin panel.' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to resend email' },
      { status: 500 }
    );
  }
}
