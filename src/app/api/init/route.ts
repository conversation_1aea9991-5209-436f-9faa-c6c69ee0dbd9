import { NextRequest, NextResponse } from 'next/server';
import { initializeShipping } from '@/lib/firebase/init-shipping';

export async function GET(request: NextRequest) {
  try {
    // Initialize shipping methods
    await initializeShipping();
    
    return NextResponse.json({ success: true, message: 'Initialization completed successfully' });
  } catch (error) {
    console.error('Error during initialization:', error);
    return NextResponse.json(
      { error: 'Initialization failed', details: (error as Error).message },
      { status: 500 }
    );
  }
}
