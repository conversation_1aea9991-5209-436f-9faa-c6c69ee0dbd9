import { NextRequest, NextResponse } from 'next/server';
import { getOrderByPaymentIntentId } from '@/lib/firebase/services/order-service';
import { getAdminAuth } from '@/lib/firebase/admin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const paymentIntentId = (await params).id;
    
    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID is required' },
        { status: 400 }
      );
    }

    // Get user ID from session if available
    let userId = 'guest';
    const authHeader = request.headers.get('Authorization');

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const auth = getAdminAuth();
        const decodedToken = await auth.verifyIdToken(token);
        userId = decodedToken.uid;
      } catch (error) {
        console.error('Error verifying auth token:', error);
        // Continue as guest if token verification fails
      }
    }

    // Fetch the order by payment intent ID
    const order = await getOrderByPaymentIntentId(paymentIntentId);
    
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if the user is authorized to view this order
    // For now, we'll allow access to the order if it's the user's order or if it's a guest order
    if (order.userId !== 'guest' && order.userId !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    return NextResponse.json({ order });
  } catch (error) {
    console.error('Error fetching order by payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order by payment intent' },
      { status: 500 }
    );
  }
}
