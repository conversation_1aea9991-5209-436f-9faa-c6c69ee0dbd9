import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/firebase/admin';
import { syncStripeDataToFirestore } from '@/lib/firebase/admin-services/stripe-sync-service';
import { getStripeCustomerId } from '@/lib/firebase/admin-services/stripe-customer-service';

export async function POST(request: NextRequest) {
  try {
    // Get user ID from auth token
    let userId = 'guest';
    const authHeader = request.headers.get('Authorization');

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const auth = getAdminAuth();
        const decodedToken = await auth.verifyIdToken(token);
        userId = decodedToken.uid;
      } catch (error) {
        console.error('Error verifying auth token:', error);
        return NextResponse.json(
          { error: 'Invalid authentication token' },
          { status: 401 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get Stripe customer ID for the user
    const stripeCustomerId = await getStripeCustomerId(userId);
    
    if (!stripeCustomerId) {
      // User doesn't have a Stripe customer yet - this is okay for first-time users
      console.log(`No Stripe customer found for user ${userId} - skipping sync`);
      return NextResponse.json({ 
        success: true, 
        message: 'No Stripe customer to sync' 
      });
    }

    // Sync the customer data
    const syncedData = await syncStripeDataToFirestore(stripeCustomerId);
    
    if (!syncedData) {
      return NextResponse.json(
        { error: 'Failed to sync customer data' },
        { status: 500 }
      );
    }

    console.log(`Successfully synced Stripe data for user ${userId} after checkout success`);
    
    return NextResponse.json({
      success: true,
      message: 'Customer data synced successfully',
      data: {
        totalSpent: syncedData.totalSpent,
        totalOrders: syncedData.totalOrders,
        lastSyncAt: syncedData.lastSyncAt,
      }
    });

  } catch (error) {
    console.error('Error in sync-after-success:', error);
    return NextResponse.json(
      { error: 'Failed to sync customer data' },
      { status: 500 }
    );
  }
}
