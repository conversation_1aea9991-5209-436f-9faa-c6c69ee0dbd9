import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getPaymentSettingsAdmin } from '@/lib/firebase/admin-services/settings-service';
import { createPendingOrderAdmin, getOrderAdmin } from '@/lib/firebase/admin-services/order-service';
import { verifyServerItems } from '@/lib/firebase/admin-services/product-service';
import { getShippingMethodAdmin } from '@/lib/firebase/admin-services/shipping-service';
import { verifyAuthToken } from '@/lib/firebase/admin';
import { emailService } from '@/lib/email/email-service';

// Validation schema for manual payment request
const manualPaymentSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    name: z.string(),
    price: z.number(),
    quantity: z.number().min(1),
    image: z.string(),
    variantId: z.string().optional(),
    variantName: z.string().optional(),
  })),
  shippingAddress: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    streetAddress: z.string().min(1),
    apartment: z.string().optional(),
    city: z.string().min(1),
    country: z.string().min(1),
    state: z.string().min(1),
    zipCode: z.string().min(1),
    phone: z.string().min(1),
  }),
  billingAddress: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    streetAddress: z.string().min(1),
    apartment: z.string().optional(),
    city: z.string().min(1),
    country: z.string().min(1),
    state: z.string().min(1),
    zipCode: z.string().min(1),
    phone: z.string().min(1),
  }).optional(),
  sameAsShipping: z.boolean(),
  email: z.string().email(),
  shippingMethod: z.object({
    id: z.string(),
    name: z.string(),
    price: z.number(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Get payment settings from Firestore
    const paymentSettings = await getPaymentSettingsAdmin();

    if (!paymentSettings?.manualPaymentEnabled || !paymentSettings?.bankAccountDetails) {
      return NextResponse.json(
        { error: 'Manual payments are not configured' },
        { status: 500 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = manualPaymentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { items, shippingAddress, billingAddress, sameAsShipping, email, shippingMethod } = validationResult.data;

    // Get user ID from auth token if available
    let userId = 'guest';
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decodedToken = await verifyAuthToken(token);
        userId = decodedToken.uid;
      } catch (error) {
        console.log('No valid auth token, proceeding as guest');
      }
    }

    // Server-side verification of items and pricing
    const serverVerifiedItems = await verifyServerItems(items);
    
    // Calculate subtotal from server-verified items
    const calculatedSubtotal = serverVerifiedItems.reduce((sum, item) => {
      return sum + (item.price * item.quantity);
    }, 0);

    // Verify shipping method and get actual cost
    const shippingOptionFromDB = await getShippingMethodAdmin(shippingMethod.id);
    if (!shippingOptionFromDB) {
      return NextResponse.json(
        { error: 'Invalid shipping method' },
        { status: 400 }
      );
    }

    const actualShippingCost = shippingOptionFromDB.price;
    const total = calculatedSubtotal + actualShippingCost;

    // Create a pending order in Firestore
    const internalOrderId = await createPendingOrderAdmin(
      userId,
      email,
      serverVerifiedItems,
      shippingAddress,
      sameAsShipping ? shippingAddress : (billingAddress || shippingAddress),
      {
        id: shippingOptionFromDB.id,
        name: shippingOptionFromDB.name,
        price: actualShippingCost
      },
      calculatedSubtotal,
      actualShippingCost,
      total,
      paymentSettings.currencyCode
    );

    // Generate order reference for bank transfer
    const orderReference = `ORD-${internalOrderId.slice(-8).toUpperCase()}`;

    // Create payment instructions
    const paymentInstructions = {
      orderId: internalOrderId,
      orderReference,
      bankAccountDetails: paymentSettings.bankAccountDetails,
      amount: total,
      currency: paymentSettings.currencyCode,
      instructions: paymentSettings.paymentInstructions?.replace('{orderReference}', orderReference) || 
        `Please transfer ${paymentSettings.currencySymbol}${total.toFixed(2)} to the bank account details provided and include the order reference ${orderReference} in your transfer description.`,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    };

    // Send payment instructions email
    try {
      const order = await getOrderAdmin(internalOrderId);
      if (order && order.email) {
        await emailService.sendPaymentInstructions(order, paymentInstructions);
        console.log(`Payment instructions email sent to ${order.email} for order ${internalOrderId}`);
      }
    } catch (emailError) {
      console.error('Failed to send payment instructions email:', emailError);
      // Don't fail the order creation if email fails
    }

    // Return the payment instructions and order ID
    return NextResponse.json({
      success: true,
      paymentType: 'manual',
      internalOrderId,
      paymentInstructions,
      orderSummary: {
        items: serverVerifiedItems,
        subtotal: calculatedSubtotal,
        shippingCost: actualShippingCost,
        total,
        currency: paymentSettings.currencyCode,
        currencySymbol: paymentSettings.currencySymbol
      }
    });

  } catch (error) {
    console.error('Error creating manual payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
