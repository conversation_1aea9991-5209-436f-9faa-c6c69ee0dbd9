import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { z } from 'zod';
import { getPaymentSettingsAdmin } from '@/lib/firebase/admin-services/settings-service';
import { createPendingOrderAdmin } from '@/lib/firebase/admin-services/order-service';
import { getAdminAuth } from '@/lib/firebase/admin';
import { getProductById } from '@/lib/firebase/admin-services/product-service';
import { getShippingMethodByIdAdmin } from '@/lib/firebase/admin-services/shipping-service';
import { getOrCreateStripeCustomer } from '@/lib/firebase/admin-services/stripe-customer-service';

// Schema for checkout session request
const checkoutSessionSchema = z.object({
  items: z.array(
    z.object({
      productId: z.string(),
      name: z.string(),
      price: z.number(),
      quantity: z.number(),
      image: z.string(),
      variantId: z.string().optional(),
      variantName: z.string().optional(),
    })
  ),
  shippingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    streetAddress: z.string(),
    apartment: z.string().optional(),
    city: z.string(),
    country: z.string(),
    state: z.string(),
    zipCode: z.string(),
    phone: z.string(),
  }),
  billingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    streetAddress: z.string(),
    apartment: z.string().optional(),
    city: z.string(),
    country: z.string(),
    state: z.string(),
    zipCode: z.string(),
    phone: z.string(),
  }).optional(),
  sameAsShipping: z.boolean().optional(),
  email: z.string().email(),
  shippingMethod: z.object({
    id: z.string(),
    name: z.string(),
    price: z.number(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Get payment settings from Firestore
    const paymentSettings = await getPaymentSettingsAdmin();

    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      return NextResponse.json(
        { error: 'Stripe payments are not configured' },
        { status: 500 }
      );
    }

    // Initialize Stripe with the secret key
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil',
    });

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = checkoutSessionSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const {
      items,
      shippingAddress,
      billingAddress,
      sameAsShipping,
      email,
      shippingMethod
    } = validationResult.data;

    // Get user ID from session if available
    let userId = 'guest';
    const authHeader = request.headers.get('Authorization');

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const auth = getAdminAuth();
        const decodedToken = await auth.verifyIdToken(token);
        userId = decodedToken.uid;
      } catch (error) {
        console.error('Error verifying auth token:', error);
        // Continue as guest if token verification fails
      }
    }

    // Fetch authoritative product prices from the database
    const serverVerifiedItems = [];
    let calculatedSubtotal = 0;

    for (const clientItem of items) {
      const productFromDB = await getProductById(clientItem.productId);

      if (!productFromDB) {
        return NextResponse.json(
          { error: `Product ${clientItem.productId} not found` },
          { status: 400 }
        );
      }

      let actualPrice = productFromDB.price;
      if (clientItem.variantId && productFromDB.variants) {
        const variant = productFromDB.variants.find(v => v.id === clientItem.variantId);
        if (variant && variant.price !== undefined) {
          actualPrice = variant.price;
        }
      }

      const itemSubtotal = actualPrice * clientItem.quantity;
      serverVerifiedItems.push({
        productId: clientItem.productId,
        name: clientItem.name,
        price: actualPrice,
        quantity: clientItem.quantity,
        image: clientItem.image,
        variantId: clientItem.variantId,
        variantName: clientItem.variantName,
        subtotal: itemSubtotal
      });

      calculatedSubtotal += itemSubtotal;
    }

    // Fetch authoritative shipping cost
    const shippingOptionFromDB = await getShippingMethodByIdAdmin(shippingMethod.id);

    if (!shippingOptionFromDB) {
      return NextResponse.json(
        { error: `Shipping method ${shippingMethod.id} not found` },
        { status: 400 }
      );
    }

    const actualShippingCost = shippingOptionFromDB.price;
    const total = calculatedSubtotal + actualShippingCost;

    // Get or create Stripe customer
    const customerName = `${shippingAddress.firstName} ${shippingAddress.lastName}`;
    const stripeCustomerId = await getOrCreateStripeCustomer(userId, email, customerName);

    // Create a pending order in Firestore
    const internalOrderId = await createPendingOrderAdmin(
      userId,
      email,
      serverVerifiedItems,
      shippingAddress,
      sameAsShipping ? shippingAddress : (billingAddress || shippingAddress),
      {
        id: shippingOptionFromDB.id,
        name: shippingOptionFromDB.name,
        price: actualShippingCost
      },
      calculatedSubtotal,
      actualShippingCost,
      total,
      paymentSettings.currencyCode
    );

    // Create line items for Stripe
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = serverVerifiedItems.map(item => ({
      price_data: {
        currency: paymentSettings.currencyCode.toLowerCase(),
        product_data: {
          name: item.name,
          images: item.image ? [item.image] : [],
          metadata: {
            productId: item.productId,
            variantId: item.variantId || '',
          },
        },
        unit_amount: Math.round(item.price * 100), // Convert to cents
      },
      quantity: item.quantity,
    }));

    // Add shipping as a line item
    if (actualShippingCost > 0) {
      lineItems.push({
        price_data: {
          currency: paymentSettings.currencyCode.toLowerCase(),
          product_data: {
            name: shippingOptionFromDB.name,
            metadata: {
              type: 'shipping',
              shippingMethodId: shippingOptionFromDB.id,
            },
          },
          unit_amount: Math.round(actualShippingCost * 100),
        },
        quantity: 1,
      });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'}/checkout?cancelled=true`,
      billing_address_collection: 'required',
      shipping_address_collection: {
        allowed_countries: ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES'],
      },
      customer_email: email,
      metadata: {
        internalOrderId,
        userId,
        customerEmail: email,
      },
      // Enable automatic tax calculation if configured
      automatic_tax: {
        enabled: false, // Set to true if you have tax calculation configured
      },
    });

    return NextResponse.json({
      sessionId: session.id,
      sessionUrl: session.url,
      internalOrderId,
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
