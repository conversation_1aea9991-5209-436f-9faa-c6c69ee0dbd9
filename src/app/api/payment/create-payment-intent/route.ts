import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { z } from 'zod';
import { getPaymentSettingsAdmin } from '@/lib/firebase/admin-services/settings-service';
import { createPendingOrderAdmin } from '@/lib/firebase/admin-services/order-service';
import { getAdminAuth } from '@/lib/firebase/admin';
import { getProductById } from '@/lib/firebase/admin-services/product-service';
import { getShippingMethodByIdAdmin } from '@/lib/firebase/admin-services/shipping-service';
import { getOrCreateStripeCustomer } from '@/lib/firebase/admin-services/stripe-customer-service';

// Schema for payment intent request
const paymentIntentSchema = z.object({
  items: z.array(
    z.object({
      productId: z.string(),
      name: z.string(),
      price: z.number(),
      quantity: z.number(),
      image: z.string(),
      variantId: z.string().optional(),
      variantName: z.string().optional(),
    })
  ),
  shippingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    streetAddress: z.string(),
    apartment: z.string().optional(),
    city: z.string(),
    country: z.string(),
    state: z.string(),
    zipCode: z.string(),
    phone: z.string(),
  }),
  billingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    streetAddress: z.string(),
    apartment: z.string().optional(),
    city: z.string(),
    country: z.string(),
    state: z.string(),
    zipCode: z.string(),
    phone: z.string(),
  }).optional(),
  sameAsShipping: z.boolean().optional(),
  email: z.string().email(),
  shippingMethod: z.object({
    id: z.string(),
    name: z.string(),
    price: z.number(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Get payment settings from Firestore
    const paymentSettings = await getPaymentSettingsAdmin();

    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      return NextResponse.json(
        { error: 'Stripe payments are not configured' },
        { status: 500 }
      );
    }

    // Initialize Stripe with the secret key
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil', // Use a standard Stripe API version
    });

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = paymentIntentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const {
      items,
      shippingAddress,
      billingAddress,
      sameAsShipping,
      email,
      shippingMethod
    } = validationResult.data;

    // Get user ID from session if available
    let userId = 'guest';
    const authHeader = request.headers.get('Authorization');

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const auth = getAdminAuth();
        const decodedToken = await auth.verifyIdToken(token);
        userId = decodedToken.uid;
      } catch (error) {
        console.error('Error verifying auth token:', error);
        // Continue as guest if token verification fails
      }
    }

    // Fetch authoritative product prices from the database
    const serverVerifiedItems = [];
    let calculatedSubtotal = 0;

    for (const clientItem of items) {
      // Get the product from the database to verify its price
      const productFromDB = await getProductById(clientItem.productId);

      if (!productFromDB) {
        return NextResponse.json(
          { error: `Product ${clientItem.productId} not found` },
          { status: 400 }
        );
      }

      // If a variant is specified, find its price
      let actualPrice = productFromDB.price;
      if (clientItem.variantId && productFromDB.variants) {
        const variant = productFromDB.variants.find(v => v.id === clientItem.variantId);
        if (variant && variant.price !== undefined) {
          actualPrice = variant.price;
        }
      }

      // Create a server-verified item with the correct price
      const itemSubtotal = actualPrice * clientItem.quantity;
      serverVerifiedItems.push({
        productId: clientItem.productId,
        name: clientItem.name,
        price: actualPrice,
        quantity: clientItem.quantity,
        image: clientItem.image,
        variantId: clientItem.variantId,
        variantName: clientItem.variantName,
        subtotal: itemSubtotal
      });

      calculatedSubtotal += itemSubtotal;
    }

    // Fetch authoritative shipping cost from the database
    const shippingOptionFromDB = await getShippingMethodByIdAdmin(shippingMethod.id);

    if (!shippingOptionFromDB) {
      return NextResponse.json(
        { error: `Shipping method ${shippingMethod.id} not found` },
        { status: 400 }
      );
    }

    const actualShippingCost = shippingOptionFromDB.price;
    const total = calculatedSubtotal + actualShippingCost;

    // Get or create Stripe customer BEFORE creating payment intent
    const customerName = `${validationResult.data.shippingAddress.firstName} ${validationResult.data.shippingAddress.lastName}`;
    const stripeCustomerId = await getOrCreateStripeCustomer(userId, email, customerName);

    // Create a pending order in Firestore
    const internalOrderId = await createPendingOrderAdmin(
      userId,
      email,
      serverVerifiedItems,
      validationResult.data.shippingAddress,
      validationResult.data.sameAsShipping ? validationResult.data.shippingAddress : (validationResult.data.billingAddress || validationResult.data.shippingAddress),
      {
        id: shippingOptionFromDB.id,
        name: shippingOptionFromDB.name,
        price: actualShippingCost
      },
      calculatedSubtotal,
      actualShippingCost,
      total,
      paymentSettings.currencyCode
    );

    // Create a PaymentIntent with the server-calculated amount and currency
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(total * 100), // Stripe requires amount in cents
      currency: paymentSettings.currencyCode.toLowerCase(),
      customer: stripeCustomerId, // CRITICAL: Always associate with customer
      metadata: {
        internalOrderId, // Link to the Firestore pending order
        userId,
        customerEmail: email,
      },
      receipt_email: email, // For Stripe to send its receipt
      shipping: {
        name: customerName,
        address: {
          line1: validationResult.data.shippingAddress.streetAddress,
          line2: validationResult.data.shippingAddress.apartment || '',
          city: validationResult.data.shippingAddress.city,
          state: validationResult.data.shippingAddress.state,
          postal_code: validationResult.data.shippingAddress.zipCode,
          country: validationResult.data.shippingAddress.country,
        },
        phone: validationResult.data.shippingAddress.phone,
      },
    });

    // Return the client secret and order ID
    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      internalOrderId, // Return the order ID for client reference
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
