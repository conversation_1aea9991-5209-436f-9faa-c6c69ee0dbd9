'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  Minus,
  Plus,
  ShoppingCart,
  Heart,
  Share2,
  Check,
  Star,
  ChevronRight,
  Loader2
} from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ProductGrid } from '@/components/products/product-grid';
import { useCart } from '@/contexts/cart-context';
import { useToast } from '@/components/ui/use-toast';
import {
  getProductBySlug,
  getRelatedProducts,
  getProductReviews
} from '@/lib/firebase/services/product-service';
import { Product, ProductVariant, Review } from '@/types';

export default function ProductDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { addToCart } = useCart();
  const { toast } = useToast();

  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProductData = async () => {
      try {
        setLoading(true);

        // Get product by slug
        const productData = await getProductBySlug(slug);

        if (!productData) {
          toast({
            title: "Error",
            description: "Product not found",
            variant: "destructive",
          });
          return;
        }

        setProduct(productData);
        setSelectedImage(productData.images[0] || null);

        // Get related products
        const related = await getRelatedProducts(productData.categoryId || productData.category, productData.id, 4);
        setRelatedProducts(related);

        // Get product reviews
        const productReviews = await getProductReviews(productData.id);
        setReviews(productReviews);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast({
          title: "Error",
          description: "Failed to load product details",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [slug, toast]);

  // Handle quantity change
  const handleQuantityChange = (value: number) => {
    setQuantity(Math.max(1, value));
  };

  // Handle variant selection
  const handleVariantSelect = (variant: ProductVariant) => {
    setSelectedVariant(variant);

    // If variant has images, select the first one
    if (variant.images && variant.images.length > 0) {
      setSelectedImage(variant.images[0]);
    }
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (!product) return;

    addToCart(product, quantity, selectedVariant || undefined);
  };

  // Calculate price and discount
  const price = selectedVariant?.price !== undefined ? selectedVariant.price : product?.price || 0;
  const compareAtPrice = selectedVariant?.compareAtPrice !== undefined ? selectedVariant.compareAtPrice : product?.compareAtPrice;
  const discountPercentage = compareAtPrice
    ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100)
    : 0;

  // Check if product is on sale
  const isOnSale = compareAtPrice !== undefined && compareAtPrice > price;

  // Calculate average rating
  const averageRating = product?.ratingAvg || 0;

  if (loading) {
    return (
      <MainLayout>
        <div className="container py-16 flex justify-center items-center min-h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (!product) {
    return (
      <MainLayout>
        <div className="container py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
          <p className="mb-6">The product you are looking for does not exist or has been removed.</p>
          <Button asChild>
            <Link href="/products">Browse Products</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container py-8">
        {/* Breadcrumbs */}
        <nav className="flex items-center text-sm mb-8">
          <Link href="/" className="text-muted-foreground hover:text-foreground">
            Home
          </Link>
          <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
          <Link href="/products" className="text-muted-foreground hover:text-foreground">
            Products
          </Link>
          <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
          <Link
            href={`/categories/${product.categoryId}`}
            className="text-muted-foreground hover:text-foreground"
          >
            {product.category}
          </Link>
          <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
          <span className="font-medium truncate max-w-[200px]">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
              {selectedImage ? (
                <Image
                  src={selectedImage}
                  alt={product.name}
                  fill
                  className="object-cover"
                  unoptimized
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gray-200">
                  <ShoppingCart className="h-16 w-16 text-gray-400" />
                </div>
              )}

              {isOnSale && (
                <Badge className="absolute top-4 left-4 bg-red-500 hover:bg-red-600">
                  {discountPercentage}% OFF
                </Badge>
              )}
            </div>

            {/* Thumbnail images */}
            {product.images.length > 1 && (
              <div className="flex gap-2 overflow-x-auto pb-2">
                {product.images.map((image, index) => (
                  <button
                    key={image}
                    className={`relative aspect-square w-20 min-w-20 overflow-hidden rounded-md border-2 ${
                      selectedImage === image ? 'border-primary' : 'border-transparent'
                    }`}
                    onClick={() => setSelectedImage(image)}
                  >
                    <Image
                      src={image}
                      alt={`${product.name} - Image ${index + 1}`}
                      fill
                      className="object-cover"
                      unoptimized
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">{product.name}</h1>

              <div className="flex items-center gap-4 mt-2">
                {/* Rating */}
                {averageRating > 0 && (
                  <div className="flex items-center">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= Math.round(averageRating)
                              ? 'fill-amber-400 text-amber-400'
                              : 'fill-gray-200 text-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="ml-2 text-sm font-medium">
                      {averageRating.toFixed(1)}
                    </span>
                    <span className="ml-1 text-sm text-muted-foreground">
                      ({product.ratingCount} {product.ratingCount === 1 ? 'review' : 'reviews'})
                    </span>
                  </div>
                )}

                {/* SKU */}
                {product.sku && (
                  <div className="text-sm text-muted-foreground">
                    SKU: <span className="font-medium">{product.sku}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-3xl font-bold">
                ${price.toFixed(2)}
              </span>
              {isOnSale && compareAtPrice && (
                <span className="text-xl text-muted-foreground line-through">
                  ${compareAtPrice.toFixed(2)}
                </span>
              )}
            </div>

            {/* Description */}
            <p className="text-gray-600">{product.description}</p>

            {/* Variants */}
            {product.variants && product.variants.length > 0 && (
              <div>
                <h3 className="font-medium mb-3">Options</h3>
                <div className="flex flex-wrap gap-2">
                  {product.variants.map((variant) => (
                    <button
                      key={variant.id}
                      className={`px-4 py-2 rounded-md border ${
                        selectedVariant?.id === variant.id
                          ? 'border-primary bg-primary/10 text-primary'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleVariantSelect(variant)}
                    >
                      {variant.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity */}
            <div>
              <h3 className="font-medium mb-3">Quantity</h3>
              <div className="flex items-center border rounded-md w-32">
                <button
                  onClick={() => handleQuantityChange(quantity - 1)}
                  className="w-10 h-10 flex items-center justify-center text-gray-600"
                  aria-label="Decrease quantity"
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="flex-1 text-center">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(quantity + 1)}
                  className="w-10 h-10 flex items-center justify-center text-gray-600"
                  aria-label="Increase quantity"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Stock status */}
            <div className="flex items-center gap-2">
              {(selectedVariant?.stock !== undefined ? selectedVariant.stock : product?.stock || 0) > 0 ? (
                <>
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-green-600 font-medium">In Stock</span>
                </>
              ) : (
                <span className="text-red-600 font-medium">Out of Stock</span>
              )}
            </div>

            {/* Add to cart button */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                size="lg"
                className="flex-1"
                onClick={handleAddToCart}
                disabled={(selectedVariant?.stock !== undefined ? selectedVariant.stock : product?.stock || 0) <= 0}
              >
                <ShoppingCart className="mr-2 h-5 w-5" />
                Add to Cart
              </Button>

              <Button size="lg" variant="outline" className="sm:w-12">
                <Heart className="h-5 w-5" />
                <span className="sr-only">Add to Wishlist</span>
              </Button>

              <Button size="lg" variant="outline" className="sm:w-12">
                <Share2 className="h-5 w-5" />
                <span className="sr-only">Share Product</span>
              </Button>
            </div>

            {/* Features */}
            {product.features && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-medium mb-2">Features</h3>
                <ul className="space-y-1">
                  {product.features.map((feature) => (
                    <li key={feature} className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Product details tabs */}
        <div className="mb-16">
          <Tabs defaultValue="description">
            <TabsList className="w-full justify-start border-b rounded-none h-auto p-0 bg-transparent">
              <TabsTrigger
                value="description"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary bg-transparent px-4 py-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                Description
              </TabsTrigger>
              <TabsTrigger
                value="specifications"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary bg-transparent px-4 py-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                Specifications
              </TabsTrigger>
              <TabsTrigger
                value="reviews"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary bg-transparent px-4 py-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                Reviews ({reviews.length})
              </TabsTrigger>
            </TabsList>

            <div className="py-6">
              <TabsContent value="description" className="mt-0">
                <div className="prose max-w-none">
                  <p>{product.description}</p>
                  {/* Add more detailed description here */}
                </div>
              </TabsContent>

              <TabsContent value="specifications" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="font-medium mb-4">Product Specifications</h3>
                    <div className="space-y-2">
                      {product.dimensions && (
                        <div className="grid grid-cols-2 py-2 border-b">
                          <span className="text-muted-foreground">Dimensions</span>
                          <span>
                            {product.dimensions.length && product.dimensions.width && product.dimensions.height
                              ? `${product.dimensions.length} × ${product.dimensions.width} × ${product.dimensions.height} cm`
                              : 'N/A'}
                          </span>
                        </div>
                      )}

                      {product.weight && (
                        <div className="grid grid-cols-2 py-2 border-b">
                          <span className="text-muted-foreground">Weight</span>
                          <span>{product.weight} kg</span>
                        </div>
                      )}

                      <div className="grid grid-cols-2 py-2 border-b">
                        <span className="text-muted-foreground">Category</span>
                        <span>{product.category}</span>
                      </div>

                      {product.sku && (
                        <div className="grid grid-cols-2 py-2 border-b">
                          <span className="text-muted-foreground">SKU</span>
                          <span>{product.sku}</span>
                        </div>
                      )}

                      {product.barcode && (
                        <div className="grid grid-cols-2 py-2 border-b">
                          <span className="text-muted-foreground">Barcode</span>
                          <span>{product.barcode}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="reviews" className="mt-0">
                {reviews.length === 0 ? (
                  <div className="text-center py-8">
                    <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Be the first to review this product
                    </p>
                    <Button>Write a Review</Button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b pb-6">
                        <div className="flex justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="font-medium">{review.userName}</div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(review.createdAt.toDate()).toLocaleDateString()}
                            </div>
                          </div>
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-4 w-4 ${
                                  star <= review.rating
                                    ? 'fill-amber-400 text-amber-400'
                                    : 'fill-gray-200 text-gray-200'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <h4 className="font-medium mb-1">{review.title}</h4>
                        <p className="text-gray-600">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Related products */}
        {relatedProducts.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Related Products</h2>
            <ProductGrid products={relatedProducts} columns={4} />
          </div>
        )}
      </div>
    </MainLayout>
  );
}
