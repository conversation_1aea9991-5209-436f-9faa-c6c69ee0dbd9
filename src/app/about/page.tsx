'use client';

import Image from 'next/image';
import Link from 'next/link';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Heart, 
  Award, 
  Users, 
  Truck, 
  ShieldCheck, 
  Clock, 
  Phone, 
  Mail, 
  MapPin 
} from 'lucide-react';

export default function AboutPage() {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="relative h-[400px] rounded-lg overflow-hidden mb-16">
          <Image
            src="/images/about-hero.jpg"
            alt="About Belinda Home Decor"
            fill
            className="object-cover"
            unoptimized
          />
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-center text-white p-6 max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">About Belinda Home Decor</h1>
              <p className="text-lg md:text-xl">
                Transforming houses into homes with elegant curtains and wall designs since 2010
              </p>
            </div>
          </div>
        </div>
        
        {/* Our Story */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-6">Our Story</h2>
            <div className="space-y-4">
              <p>
                Belinda Home Decor was founded in 2010 by interior designer Belinda Thompson with a simple mission: to make beautiful home decor accessible to everyone.
              </p>
              <p>
                What started as a small curtain shop in Singapore has grown into a beloved brand offering a wide range of home decor products, from premium curtains to elegant wall designs and accessories.
              </p>
              <p>
                Our passion for quality craftsmanship and attention to detail has earned us the trust of thousands of customers across Singapore and beyond.
              </p>
            </div>
            <div className="mt-8">
              <Button asChild>
                <Link href="/products">
                  Explore Our Products
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative h-[400px] rounded-lg overflow-hidden">
            <Image
              src="/images/about-story.jpg"
              alt="Our Story"
              fill
              className="object-cover"
              unoptimized
            />
          </div>
        </div>
        
        {/* Our Values */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">Our Values</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-primary/10 p-3 rounded-full mb-4">
                    <Heart className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Passion</h3>
                  <p className="text-muted-foreground">
                    We&apos;re passionate about creating beautiful spaces that inspire and comfort.
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-primary/10 p-3 rounded-full mb-4">
                    <Award className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Quality</h3>
                  <p className="text-muted-foreground">
                    We never compromise on quality, using only the finest materials and craftsmanship.
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-primary/10 p-3 rounded-full mb-4">
                    <Users className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Customer Focus</h3>
                  <p className="text-muted-foreground">
                    Our customers are at the heart of everything we do, guiding our decisions and innovations.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Why Choose Us */}
        <div className="bg-muted/30 rounded-lg p-8 mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">Why Choose Belinda Home Decor</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <ShieldCheck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Premium Quality</h3>
              <p className="text-sm text-muted-foreground">
                All our products are made with premium materials for durability and beauty.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <Truck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Fast Delivery</h3>
              <p className="text-sm text-muted-foreground">
                We offer quick and reliable delivery services across Singapore.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Expert Advice</h3>
              <p className="text-sm text-muted-foreground">
                Our team of interior design experts is always ready to help.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Easy Returns</h3>
              <p className="text-sm text-muted-foreground">
                Not satisfied? We offer hassle-free returns within 30 days.
              </p>
            </div>
          </div>
        </div>
        
        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">Meet Our Team</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="relative h-64 rounded-lg overflow-hidden mb-4">
                <Image
                  src="/images/team-1.jpg"
                  alt="Belinda Thompson"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
              <h3 className="font-semibold">Belinda Thompson</h3>
              <p className="text-sm text-muted-foreground">Founder & Creative Director</p>
            </div>
            
            <div className="text-center">
              <div className="relative h-64 rounded-lg overflow-hidden mb-4">
                <Image
                  src="/images/team-2.jpg"
                  alt="David Chen"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
              <h3 className="font-semibold">David Chen</h3>
              <p className="text-sm text-muted-foreground">Head of Operations</p>
            </div>
            
            <div className="text-center">
              <div className="relative h-64 rounded-lg overflow-hidden mb-4">
                <Image
                  src="/images/team-3.jpg"
                  alt="Sarah Wong"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
              <h3 className="font-semibold">Sarah Wong</h3>
              <p className="text-sm text-muted-foreground">Lead Designer</p>
            </div>
            
            <div className="text-center">
              <div className="relative h-64 rounded-lg overflow-hidden mb-4">
                <Image
                  src="/images/team-4.jpg"
                  alt="Michael Tan"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
              <h3 className="font-semibold">Michael Tan</h3>
              <p className="text-sm text-muted-foreground">Customer Experience Manager</p>
            </div>
          </div>
        </div>
        
        {/* Contact Section */}
        <div className="bg-muted/30 rounded-lg p-8">
          <h2 className="text-3xl font-bold mb-8 text-center">Get In Touch</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <Phone className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Call Us</h3>
              <p className="text-muted-foreground">+65 6123 4567</p>
              <p className="text-sm text-muted-foreground">Mon-Fri: 9am-6pm</p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <Mail className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Email Us</h3>
              <p className="text-muted-foreground"><EMAIL></p>
              <p className="text-sm text-muted-foreground">We&apos;ll respond within 24 hours</p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="bg-white p-3 rounded-full mb-4 shadow-sm">
                <MapPin className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Visit Us</h3>
              <p className="text-muted-foreground">123 Orchard Road</p>
              <p className="text-sm text-muted-foreground">Singapore 238893</p>
            </div>
          </div>
          
          <Separator className="my-8" />
          
          <div className="text-center">
            <Button asChild size="lg">
              <Link href="/contact">
                Contact Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
