'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ProductGrid } from "@/components/ui/product-grid";
import { MainLayout } from "@/components/layout/main-layout";
import { Loader2 } from 'lucide-react';
import { Product } from '@/types';
import { Category, getAllCategories } from '@/lib/firebase/services/category-service';
import { getFeaturedProducts } from '@/lib/firebase/services/product-service';
import { getActiveCarouselSlides, CarouselSlide } from '@/lib/firebase/services/carousel-service';
import { getActiveSpecialOffer, SpecialOffer } from '@/lib/firebase/services/special-offer-service';

export default function Home() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [carouselSlides, setCarouselSlides] = useState<CarouselSlide[]>([]);
  const [specialOffer, setSpecialOffer] = useState<SpecialOffer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [carouselLoading, setCarouselLoading] = useState<boolean>(true);
  const [specialOfferLoading, setSpecialOfferLoading] = useState<boolean>(true);

  // Fetch carousel slides
  useEffect(() => {
    const fetchCarouselSlides = async () => {
      try {
        setCarouselLoading(true);
        const slides = await getActiveCarouselSlides();
        setCarouselSlides(slides);
      } catch (error) {
        console.error('Error fetching carousel slides:', error);
        // Default slides will be used from the service
        setCarouselSlides([]);
      } finally {
        setCarouselLoading(false);
      }
    };

    fetchCarouselSlides();
  }, []);

  // Fetch special offer
  useEffect(() => {
    const fetchSpecialOffer = async () => {
      try {
        setSpecialOfferLoading(true);
        const offer = await getActiveSpecialOffer();
        setSpecialOffer(offer);
      } catch (error) {
        console.error('Error fetching special offer:', error);
        setSpecialOffer(null);
      } finally {
        setSpecialOfferLoading(false);
      }
    };

    fetchSpecialOffer();
  }, []);

  // Fetch featured products and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [productsData, categoriesData] = await Promise.all([
          getFeaturedProducts(4),
          getAllCategories()
        ]);
        setFeaturedProducts(productsData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setFeaturedProducts([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <MainLayout>
      {/* Hero Section with Carousel */}
      <section className="relative">
        {carouselLoading ? (
          <div className="aspect-[21/9] w-full flex justify-center items-center bg-muted">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <Carousel className="w-full">
            <CarouselContent>
              {carouselSlides.map((slide) => (
                <CarouselItem key={slide.id}>
                  <div className="relative aspect-[21/9] w-full overflow-hidden">
                    <Image
                      src={slide.image}
                      alt={slide.title}
                      fill
                      className="object-cover"
                      priority
                      unoptimized
                    />
                    <div className="absolute inset-0 bg-black/30 flex flex-col justify-center items-start p-8 md:p-16">
                      <div className="max-w-xl text-white">
                        <h1 className="text-3xl md:text-5xl font-bold mb-2 md:mb-4 animate-fadeInUp">
                          {slide.title}
                        </h1>
                        <p className="text-lg md:text-xl mb-4 md:mb-8 animate-fadeInUp animation-delay-200">
                          {slide.subtitle}
                        </p>
                        <Link href={slide.link}>
                          <Button size="lg" className="animate-fadeInUp animation-delay-300">
                            {slide.cta}
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-4" />
            <CarouselNext className="right-4" />
          </Carousel>
        )}
      </section>

      {/* Featured Categories */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold mb-8 text-center">Shop by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {categories.map((category) => (
            <Link
              href={`/categories/${category.id}`}
              key={category.id}
              className="group"
            >
              <div className="relative aspect-square overflow-hidden rounded-lg">
                <Image
                  // loader={imageLoader}
                  src={category.image}
                  alt={category.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                  unoptimized
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent flex flex-col justify-end p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{category.name}</h3>
                  <p className="text-white/80 mb-4">{category.description}</p>
                  <span className="text-white font-medium inline-flex items-center">
                    Shop Now
                    <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Featured Products */}
      <section className="container mx-auto px-4 py-16 bg-muted/30">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Featured Products</h2>
          <Link href="/products">
            <Button variant="outline">View All</Button>
          </Link>
        </div>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <ProductGrid products={featuredProducts} />
        )}
      </section>

      {/* Promotional Banner */}
      <section className="py-16 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4">
          {specialOfferLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary-foreground" />
            </div>
          ) : specialOffer ? (
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-8 md:mb-0 md:mr-8">
                <h2 className="text-3xl font-bold mb-4">{specialOffer.title}</h2>
                <p className="text-xl mb-6">{specialOffer.description}</p>
                <Link href={specialOffer.linkUrl}>
                  <Button variant="secondary" size="lg">{specialOffer.linkText}</Button>
                </Link>
              </div>
              <div className="relative w-full md:w-1/2 aspect-video rounded-lg overflow-hidden">
                <Image
                  src={specialOffer.image}
                  alt={specialOffer.title}
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
            </div>
          ) : null}
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold mb-12 text-center">Why Choose Us</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center text-center p-6">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <svg className="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Quality Materials</h3>
            <p className="text-muted-foreground">We source only the finest materials for our products, ensuring durability and elegance.</p>
          </div>
          <div className="flex flex-col items-center text-center p-6">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <svg className="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8 4-8-4V5l8 4 8-4v2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Fast Shipping</h3>
            <p className="text-muted-foreground">We deliver across Zimbabwe with quick and reliable shipping options.</p>
          </div>
          <div className="flex flex-col items-center text-center p-6">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <svg className="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Customer Support</h3>
            <p className="text-muted-foreground">Our dedicated team is always ready to assist you with any questions or concerns.</p>
          </div>
        </div>
      </section>
    </MainLayout>
  );
}
