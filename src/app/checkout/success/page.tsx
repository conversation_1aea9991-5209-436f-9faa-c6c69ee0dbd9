'use client';

import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';
import { CheckC<PERSON>cle, ShoppingBag, ArrowRight, Loader2, Copy, Building2 } from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useSearchParams } from 'next/navigation';
import { useCartStore } from '@/store/cart';
import { useCheckoutStore } from '@/store/checkout';
import { Order } from '@/types';
import { PaymentInstructions } from '@/types/checkout';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';

// Manual Payment Instructions Component
function ManualPaymentInstructionsDisplay({ paymentInstructions }: { paymentInstructions: PaymentInstructions }) {
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast.success(`${fieldName} copied to clipboard`);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Payment Instructions
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800 font-medium mb-2">
            Please transfer the exact amount to complete your order:
          </p>
          <div className="text-2xl font-bold text-blue-900">
            {formatCurrency(paymentInstructions.amount, paymentInstructions.currency)}
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-sm font-medium text-yellow-800 mb-2">
            Important: Order Reference
          </p>
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-yellow-700">
                Include this reference in your transfer:
              </p>
              <p className="font-bold text-lg text-yellow-900 font-mono">
                {paymentInstructions.orderReference}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(paymentInstructions.orderReference, 'Order Reference')}
            >
              {copiedField === 'Order Reference' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium">Bank Account Details</h4>

          <div className="grid gap-3">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Bank Name</p>
                <p className="font-medium">{paymentInstructions.bankAccountDetails.bankName}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.bankName, 'Bank Name')}
              >
                {copiedField === 'Bank Name' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Account Name</p>
                <p className="font-medium">{paymentInstructions.bankAccountDetails.accountName}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.accountName, 'Account Name')}
              >
                {copiedField === 'Account Name' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm text-gray-600">Account Number</p>
                <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.accountNumber}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.accountNumber, 'Account Number')}
              >
                {copiedField === 'Account Number' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>

            {paymentInstructions.bankAccountDetails.routingNumber && (
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">Routing Number</p>
                  <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.routingNumber}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.routingNumber!, 'Routing Number')}
                >
                  {copiedField === 'Routing Number' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            )}

            {paymentInstructions.bankAccountDetails.swiftCode && (
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">SWIFT Code</p>
                  <p className="font-medium font-mono">{paymentInstructions.bankAccountDetails.swiftCode}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(paymentInstructions.bankAccountDetails.swiftCode!, 'SWIFT Code')}
                >
                  {copiedField === 'SWIFT Code' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="text-sm text-gray-600 space-y-2">
          <p>{paymentInstructions.instructions}</p>
          {paymentInstructions.dueDate && (
            <p>
              <strong>Payment Due:</strong> {new Date(paymentInstructions.dueDate).toLocaleDateString()}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Component to handle payment verification
function PaymentVerification() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [paymentInstructions, setPaymentInstructions] = useState<PaymentInstructions | null>(null);
  const searchParams = useSearchParams();
  const clearCart = useCartStore(state => state.clearCart);
  const { internalOrderId, paymentInstructions: checkoutPaymentInstructions } = useCheckoutStore();
  const { user, getIdToken } = useAuth();

  // Fetch order details
  const fetchOrderDetails = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch order details');
      }
      const data = await response.json();
      setOrder(data.order);
      return data.order;
    } catch (error) {
      console.error('Error fetching order details:', error);
      return null;
    }
  };



  useEffect(() => {
    // Handle manual payment success
    if (internalOrderId) {
      fetchOrderDetails(internalOrderId)
        .then(orderData => {
          if (orderData) {
            setStatus('success');
            clearCart();

            // Set payment instructions if available from checkout store
            if (checkoutPaymentInstructions) {
              setPaymentInstructions(checkoutPaymentInstructions);
            }
          }
        });
      return;
    }

    // If no order ID, show success anyway (direct visit)
    setStatus('success');
  }, [clearCart, internalOrderId, checkoutPaymentInstructions]);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Verifying your payment...</p>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="container max-w-2xl mx-auto py-16 px-4">
        <div className="text-center space-y-6">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-100 mb-4">
            <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>

          <h1 className="text-3xl font-bold">Payment Failed</h1>

          <p className="text-muted-foreground">
            {errorMessage ?? 'There was an issue processing your payment. Please try again.'}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Button
              variant="outline"
              className="flex-1"
              asChild
            >
              <Link href="/checkout">
                Try Again
              </Link>
            </Button>

            <Button
              className="flex-1"
              asChild
            >
              <Link href="/cart">
                Return to Cart
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto py-16 px-4">
      <div className="space-y-6 animate-fadeIn">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>

          <h1 className="text-3xl font-bold">Thank You for Your Order!</h1>

          <p className="text-muted-foreground mt-4">
            Your order has been received. {paymentInstructions ? 'Please complete your payment using the instructions below.' : 'We\'ll send you a confirmation email shortly.'}
          </p>
        </div>

        {/* Payment Instructions */}
        {paymentInstructions && (
          <ManualPaymentInstructionsDisplay paymentInstructions={paymentInstructions} />
        )}

        {/* Order Details */}
        <div className="bg-muted p-6 rounded-lg">
          <h2 className="font-medium mb-4">Order Details</h2>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Order Number:</span>
              <span className="font-medium">{order?.id ?? `ORD-${Math.floor(100000 + Math.random() * 900000)}`}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">Date:</span>
              <span>{order?.createdAt ? new Date(order.createdAt).toLocaleDateString() : new Date().toLocaleDateString()}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className={`font-medium ${paymentInstructions ? 'text-yellow-600' : 'text-green-600'}`}>
                {paymentInstructions ? 'Pending Payment' : (order?.status ?? 'Processing')}
              </span>
            </div>

            {order?.total && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total:</span>
                <span className="font-medium">${order.total.toFixed(2)}</span>
              </div>
            )}
          </div>

          <Separator className="my-4" />

          <p className="text-sm text-muted-foreground">
            {paymentInstructions
              ? 'You will receive an email with these payment instructions. Once payment is received, we will process your order.'
              : 'You will receive an email confirmation at the email address you provided.'
            }
          </p>
        </div>

        {/* Important Notice for Manual Payments */}
        {paymentInstructions && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">Important Payment Information</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Please include the order reference <strong>{paymentInstructions.orderReference}</strong> in your bank transfer</li>
              <li>• Your order will be processed once payment is received and verified</li>
              <li>• Payment verification may take 1-3 business days</li>
              <li>• You will receive an email confirmation once payment is verified</li>
            </ul>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <Button
            variant="outline"
            className="flex-1"
            asChild
          >
            <Link href="/products">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Continue Shopping
            </Link>
          </Button>

          <Button
            className="flex-1"
            asChild
          >
            <Link href="/account/orders">
              View Orders
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function PaymentVerificationFallback() {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Loading payment information...</p>
    </div>
  );
}

export default function OrderSuccessPage() {
  return (
    <MainLayout>
      <Suspense fallback={<PaymentVerificationFallback />}>
        <PaymentVerification />
      </Suspense>
    </MainLayout>
  );
}
