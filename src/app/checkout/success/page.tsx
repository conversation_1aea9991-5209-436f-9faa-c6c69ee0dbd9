'use client';

import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';
import { CheckCir<PERSON>, ShoppingBag, ArrowRight, Loader2 } from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useSearchParams } from 'next/navigation';
import { useCartStore } from '@/store/cart';
import { useCheckoutStore } from '@/store/checkout';
import { useStripe, Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Order } from '@/types';
import { useAuth } from '@/contexts/auth-context';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? '');

// Component to handle payment verification
function PaymentVerification() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const searchParams = useSearchParams();
  const stripe = useStripe();
  const clearCart = useCartStore(state => state.clearCart);
  const { internalOrderId, paymentIntentId } = useCheckoutStore();
  const { user, getIdToken } = useAuth();

  // Fetch order details
  const fetchOrderDetails = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch order details');
      }
      const data = await response.json();
      setOrder(data.order);
      return data.order;
    } catch (error) {
      console.error('Error fetching order details:', error);
      return null;
    }
  };

  // Sync Stripe data after successful payment
  const syncStripeData = async () => {
    if (!user) return; // Only sync for logged-in users

    try {
      const token = await getIdToken();
      if (!token) return;

      const response = await fetch('/api/payment/sync-after-success', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Stripe data synced successfully:', data);
      } else {
        console.warn('Failed to sync Stripe data:', await response.text());
      }
    } catch (error) {
      console.error('Error syncing Stripe data:', error);
    }
  };

  useEffect(() => {
    if (!stripe) {
      return;
    }

    const clientSecret = searchParams.get('payment_intent_client_secret');
    const paymentIntentIdFromUrl = searchParams.get('payment_intent');

    // If we have an internal order ID from the checkout store, try to fetch it
    if (internalOrderId) {
      fetchOrderDetails(internalOrderId)
        .then(orderData => {
          if (orderData) {
            setStatus('success');
            clearCart();
            // Sync Stripe data after successful order fetch
            syncStripeData();
          }
        });
      return;
    }

    // If no client secret is present, assume this is a direct visit (not from payment)
    if (!clientSecret) {
      setStatus('success');
      return;
    }

    // Verify the payment with Stripe
    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      switch (paymentIntent?.status) {
        case 'succeeded':
        case 'processing':
          // Both succeeded and processing are treated as success
          setStatus('success');
          clearCart(); // Clear the cart on successful payment

          // Sync Stripe data after successful payment
          syncStripeData();

          // If we have a payment intent ID, try to fetch the order
          if (paymentIntent?.id || paymentIntentId || paymentIntentIdFromUrl) {
            const piId = paymentIntent?.id || paymentIntentId || paymentIntentIdFromUrl;
            if (piId) {
              // Try to fetch the order using the payment intent ID
              fetch(`/api/orders/by-payment-intent/${piId}`)
                .then(response => {
                  if (response.ok) {
                    return response.json();
                  }
                  throw new Error('Failed to fetch order by payment intent');
                })
                .then(data => {
                  if (data.order && data.order.id) {
                    fetchOrderDetails(data.order.id);
                  }
                })
                .catch(error => {
                  console.error('Error fetching order by payment intent:', error);
                });
            }
          }
          break;
        case 'requires_payment_method':
          setStatus('error');
          setErrorMessage('Your payment was not successful, please try again.');
          break;
        default:
          setStatus('error');
          setErrorMessage('Something went wrong with your payment.');
          break;
      }
    });
  }, [stripe, searchParams, clearCart, internalOrderId, paymentIntentId]);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Verifying your payment...</p>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="container max-w-2xl mx-auto py-16 px-4">
        <div className="text-center space-y-6">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-100 mb-4">
            <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>

          <h1 className="text-3xl font-bold">Payment Failed</h1>

          <p className="text-muted-foreground">
            {errorMessage ?? 'There was an issue processing your payment. Please try again.'}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Button
              variant="outline"
              className="flex-1"
              asChild
            >
              <Link href="/checkout">
                Try Again
              </Link>
            </Button>

            <Button
              className="flex-1"
              asChild
            >
              <Link href="/cart">
                Return to Cart
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-2xl mx-auto py-16 px-4">
      <div className="text-center space-y-6 animate-fadeIn">
        <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 mb-4">
          <CheckCircle className="h-10 w-10 text-green-600" />
        </div>

        <h1 className="text-3xl font-bold">Thank You for Your Order!</h1>

        <p className="text-muted-foreground">
          Your order has been received and is now being processed. We&apos;ll send you a confirmation email shortly.
        </p>

        <div className="bg-muted p-6 rounded-lg text-left">
          <h2 className="font-medium mb-4">Order Details</h2>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Order Number:</span>
              <span className="font-medium">{order?.id ?? `ORD-${Math.floor(100000 + Math.random() * 900000)}`}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">Date:</span>
              <span>{order?.createdAt ? new Date(order.createdAt).toLocaleDateString() : new Date().toLocaleDateString()}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className="text-green-600 font-medium">{order?.status ?? 'Processing'}</span>
            </div>

            {order?.total && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total:</span>
                <span className="font-medium">${order.total.toFixed(2)}</span>
              </div>
            )}
          </div>

          <Separator className="my-4" />

          <p className="text-sm text-muted-foreground">
            You will receive an email confirmation at the email address you provided.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <Button
            variant="outline"
            className="flex-1"
            asChild
          >
            <Link href="/products">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Continue Shopping
            </Link>
          </Button>

          <Button
            className="flex-1"
            asChild
          >
            <Link href="/account/orders">
              View Orders
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function PaymentVerificationFallback() {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Loading payment information...</p>
    </div>
  );
}

export default function OrderSuccessPage() {
  return (
    <MainLayout>
      <Elements stripe={stripePromise}>
        <Suspense fallback={<PaymentVerificationFallback />}>
          <PaymentVerification />
        </Suspense>
      </Elements>
    </MainLayout>
  );
}
