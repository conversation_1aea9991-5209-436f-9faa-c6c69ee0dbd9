'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCartStore } from '@/store/cart';
import { useCheckoutStore } from '@/store/checkout';
import { CheckoutSteps } from '@/components/checkout/checkout-steps';
import { CheckoutSummary } from '@/components/checkout/checkout-summary';
import { Information } from '@/components/checkout/information';
import { Shipping } from '@/components/checkout/shipping';
import { Payment } from '@/components/checkout/payment';
import { Confirmation } from '@/components/checkout/confirmation';

export default function CheckoutPage() {
  const router = useRouter();
  const items = useCartStore((state) => state.items);
  const step = useCheckoutStore((state) => state.step);

  useEffect(() => {
    if (items.length === 0) {
      router.push('/cart');
    }
  }, [items, router]);

  const renderStep = () => {
    switch (step) {
      case 'information':
        return <Information />;
      case 'shipping':
        return <Shipping />;
      case 'payment':
        return <Payment />;
      case 'confirmation':
        return <Confirmation />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8">
        <div>
          <CheckoutSteps />
          {renderStep()}
        </div>
        <CheckoutSummary />
      </div>
    </div>
  );
}