'use client';

import { useState } from 'react';
import Link from 'next/link';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Mail } from 'lucide-react';

// FAQ categories and questions
const faqData = {
  products: [
    {
      question: "What types of curtains do you offer?",
      answer: "We offer a wide range of curtains including blackout curtains, sheer curtains, room darkening curtains, and decorative curtains. Our curtains come in various materials, patterns, and sizes to suit different home decor styles."
    },
    {
      question: "How do I measure my windows for curtains?",
      answer: "To measure for curtains, you'll need to measure the width of your window or curtain rod and the desired length. For width, measure the curtain rod from end to end or add 4-8 inches on each side of the window if you haven't installed the rod yet. For length, measure from the top of the rod to where you want the curtains to end (floor, below windowsill, etc.)."
    },
    {
      question: "Do you offer custom-sized curtains?",
      answer: "Yes, we offer custom-sized curtains to fit your specific window dimensions. Please contact our customer service team for more information on custom orders, pricing, and production time."
    },
    {
      question: "What wall design options do you have?",
      answer: "Our wall design collection includes wallpapers, wall decals, wall panels, and decorative wall art. We offer various patterns, textures, and colors to enhance your interior spaces."
    },
    {
      question: "Are your products eco-friendly?",
      answer: "Many of our products are made with eco-friendly materials and processes. Look for our 'Eco-Friendly' label on product pages to identify these items. We're continuously expanding our sustainable product offerings."
    }
  ],
  orders: [
    {
      question: "How do I place an order?",
      answer: "You can place an order through our website by browsing our products, adding items to your cart, and proceeding to checkout. You'll need to create an account or check out as a guest, provide shipping information, and complete payment."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept credit/debit cards (Visa, Mastercard, American Express), PayNow, and bank transfers. All payments are processed securely through our payment gateway."
    },
    {
      question: "Can I modify or cancel my order?",
      answer: "You can modify or cancel your order within 24 hours of placing it, provided it hasn't been shipped yet. Please contact our customer service team as soon as possible with your order number to request changes or cancellation."
    },
    {
      question: "Do you offer international shipping?",
      answer: "Currently, we only ship within Singapore. We're working on expanding our shipping options to other countries in Southeast Asia in the near future."
    },
    {
      question: "How long will it take to receive my order?",
      answer: "Standard delivery within Singapore takes 3-5 business days. Express delivery options are available at checkout for an additional fee, which can reduce delivery time to 1-2 business days."
    }
  ],
  shipping: [
    {
      question: "What are your shipping rates?",
      answer: "We offer free standard shipping on orders over $100. For orders under $100, a flat shipping fee of $10 applies. Express shipping is available for an additional $15."
    },
    {
      question: "How can I track my order?",
      answer: "Once your order ships, you'll receive a confirmation email with a tracking number and link. You can also track your order by logging into your account on our website and viewing your order history."
    },
    {
      question: "Do you ship to all areas in Singapore?",
      answer: "Yes, we deliver to all residential and commercial addresses in Singapore, including Sentosa and Jurong Island."
    },
    {
      question: "What if I'm not home to receive my delivery?",
      answer: "If you're not home during delivery, our courier will leave a notice and attempt delivery again the next business day. After two failed attempts, your package will be held at the nearest collection point for 5 business days."
    },
    {
      question: "Do you offer installation services?",
      answer: "Yes, we offer professional installation services for curtains and wall designs at an additional cost. You can add installation services during checkout or contact our customer service team to arrange installation for an existing order."
    }
  ],
  returns: [
    {
      question: "What is your return policy?",
      answer: "We accept returns within 30 days of delivery for most products in their original, unused condition with tags attached. Custom-made items and cut fabrics cannot be returned unless they're defective."
    },
    {
      question: "How do I initiate a return?",
      answer: "To initiate a return, log into your account, go to your order history, select the order containing the item you wish to return, and follow the return instructions. Alternatively, you can contact our customer service team for assistance."
    },
    {
      question: "Do I have to pay for return shipping?",
      answer: "Return shipping fees are the customer's responsibility unless the return is due to our error (wrong item shipped, defective product, etc.). In such cases, we'll provide a prepaid return label."
    },
    {
      question: "How long does it take to process a refund?",
      answer: "Once we receive and inspect your return, refunds are typically processed within 5-7 business days. The time it takes for the refund to appear in your account depends on your payment method and financial institution."
    },
    {
      question: "Can I exchange an item instead of returning it?",
      answer: "Yes, you can exchange items for a different size, color, or style. To request an exchange, follow the same process as returns but select 'Exchange' instead of 'Return' and specify the item you'd like in exchange."
    }
  ],
  account: [
    {
      question: "How do I create an account?",
      answer: "You can create an account by clicking on the 'Sign Up' or 'Register' button in the top right corner of our website. You'll need to provide your email address and create a password. You can also sign up using your Google or Facebook account."
    },
    {
      question: "I forgot my password. How do I reset it?",
      answer: "Click on the 'Login' button, then select 'Forgot Password'. Enter the email address associated with your account, and we'll send you a password reset link. Follow the instructions in the email to create a new password."
    },
    {
      question: "How can I update my account information?",
      answer: "After logging in, go to 'My Account' and select 'Account Settings' or 'Profile'. From there, you can update your personal information, change your password, and manage your saved addresses and payment methods."
    },
    {
      question: "Can I view my order history?",
      answer: "Yes, you can view your complete order history by logging into your account and selecting 'Order History' or 'My Orders'. This section displays all your past and current orders, including order status and tracking information."
    },
    {
      question: "How do I delete my account?",
      answer: "To delete your account, please contact our customer service team. For security reasons, we cannot delete accounts through automated processes and need to verify your identity before proceeding with account deletion."
    }
  ]
};

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<{
    category: string;
    question: string;
    answer: string;
  }[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Handle search
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }
    
    const results: {
      category: string;
      question: string;
      answer: string;
    }[] = [];
    
    // Search through all categories and questions
    Object.entries(faqData).forEach(([category, questions]) => {
      questions.forEach(({ question, answer }) => {
        if (
          question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          answer.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          results.push({
            category,
            question,
            answer,
          });
        }
      });
    });
    
    setSearchResults(results);
    setHasSearched(true);
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Frequently Asked Questions</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Find answers to common questions about our products, ordering process, shipping, returns, and more.
            </p>
          </div>
          
          {/* Search */}
          <div className="mb-12">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search for answers..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
              </div>
              <Button onClick={handleSearch}>Search</Button>
            </div>
            
            {/* Search Results */}
            {hasSearched && (
              <div className="mt-6">
                <h2 className="text-xl font-semibold mb-4">
                  {searchResults.length > 0
                    ? `Search Results (${searchResults.length})`
                    : 'No results found'}
                </h2>
                
                {searchResults.length > 0 ? (
                  <Accordion type="single" collapsible className="space-y-4">
                    {searchResults.map((result, index) => (
                      <AccordionItem key={index} value={`search-${index}`} className="border rounded-lg px-4">
                        <AccordionTrigger className="text-left">
                          {result.question}
                        </AccordionTrigger>
                        <AccordionContent>
                          <p className="text-muted-foreground mb-2">{result.answer}</p>
                          <p className="text-sm text-primary">
                            Category: {result.category.charAt(0).toUpperCase() + result.category.slice(1)}
                          </p>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : hasSearched && (
                  <div className="text-center py-8 bg-muted/30 rounded-lg">
                    <p className="text-muted-foreground mb-4">
                      We couldn&apos;t find any answers matching your search. Please try different keywords or contact us for assistance.
                    </p>
                    <Button asChild>
                      <Link href="/contact">
                        <Mail className="mr-2 h-4 w-4" />
                        Contact Us
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* FAQ Categories */}
          <Tabs defaultValue="products">
            <TabsList className="w-full justify-start overflow-x-auto">
              <TabsTrigger value="products">Products</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="shipping">Shipping</TabsTrigger>
              <TabsTrigger value="returns">Returns & Refunds</TabsTrigger>
              <TabsTrigger value="account">Account</TabsTrigger>
            </TabsList>
            
            <div className="mt-6">
              <TabsContent value="products">
                <Accordion type="single" collapsible className="space-y-4">
                  {faqData.products.map((faq, index) => (
                    <AccordionItem key={faq.question} value={`product-${index}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
              
              <TabsContent value="orders">
                <Accordion type="single" collapsible className="space-y-4">
                  {faqData.orders.map((faq, index) => (
                    <AccordionItem key={faq.question} value={`order-${index}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
              
              <TabsContent value="shipping">
                <Accordion type="single" collapsible className="space-y-4">
                  {faqData.shipping.map((faq, index) => (
                    <AccordionItem key={faq.question} value={`shipping-${index}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
              
              <TabsContent value="returns">
                <Accordion type="single" collapsible className="space-y-4">
                  {faqData.returns.map((faq, index) => (
                    <AccordionItem key={faq.question} value={`return-${index}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
              
              <TabsContent value="account">
                <Accordion type="single" collapsible className="space-y-4">
                  {faqData.account.map((faq, index) => (
                    <AccordionItem key={faq.question} value={`account-${index}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
            </div>
          </Tabs>
          
          {/* Still have questions */}
          <div className="mt-16 text-center p-8 bg-muted/30 rounded-lg">
            <h2 className="text-2xl font-bold mb-4">Still have questions?</h2>
            <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
              If you couldn&apos;t find the answer to your question, our customer support team is here to help.
            </p>
            <Button asChild size="lg">
              <Link href="/contact">
                <Mail className="mr-2 h-4 w-4" />
                Contact Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
