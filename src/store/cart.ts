import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CartItem, Cart } from '@/types/cart';

interface CartStore extends Cart {
  addItem: (item: Omit<CartItem, 'id'>) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,
      addItem: (newItem) => {
        const items = get().items;
        const existingItemIndex = items.findIndex(
          (item) => 
            item.productId === newItem.productId && 
            item.variantId === newItem.variantId
        );

        if (existingItemIndex > -1) {
          // Update quantity if item exists
          const updatedItems = [...items];
          updatedItems[existingItemIndex].quantity += newItem.quantity;
          
          set({
            items: updatedItems,
            total: calculateTotal(updatedItems),
          });
        } else {
          // Add new item
          const item: CartItem = {
            ...newItem,
            id: `${newItem.productId}-${newItem.variantId}-${Date.now()}`,
          };
          
          set((state) => ({
            items: [...state.items, item],
            total: calculateTotal([...state.items, item]),
          }));
        }
      },
      removeItem: (itemId) => {
        set((state) => {
          const items = state.items.filter((item) => item.id !== itemId);
          return {
            items,
            total: calculateTotal(items),
          };
        });
      },
      updateQuantity: (itemId, quantity) => {
        set((state) => {
          const items = state.items.map((item) =>
            item.id === itemId ? { ...item, quantity } : item
          );
          return {
            items,
            total: calculateTotal(items),
          };
        });
      },
      clearCart: () => {
        set({ items: [], total: 0 });
      },
    }),
    {
      name: 'cart-storage',
    }
  )
);

const calculateTotal = (items: CartItem[]): number => {
  return Number(
    items
      .reduce((sum, item) => sum + item.price * item.quantity, 0)
      .toFixed(2)
  );
};