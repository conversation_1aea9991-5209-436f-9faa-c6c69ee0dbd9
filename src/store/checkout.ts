import { create } from 'zustand';
import { CheckoutState, Address, PaymentMethod, CheckoutStep, ShippingMethod } from '@/types/checkout';

interface CheckoutStore extends CheckoutState {
  setStep: (step: CheckoutStep) => void;
  setEmail: (email: string) => void;
  setShippingAddress: (address: Address) => void;
  setBillingAddress: (address: Address) => void;
  setSameAsShipping: (same: boolean) => void;
  setPaymentMethod: (method: PaymentMethod) => void;
  setShippingMethod: (method: ShippingMethod) => void;
  setInternalOrderId: (id: string) => void;
  setPaymentIntentId: (id: string) => void;
  reset: () => void;
}

const initialState: CheckoutState = {
  step: 'information',
  shippingAddress: null,
  billingAddress: null,
  sameAsShipping: true,
  paymentMethod: null,
  shippingMethod: null,
  email: '',
  internalOrderId: undefined,
  paymentIntentId: undefined,
};

export const useCheckoutStore = create<CheckoutStore>((set) => ({
  ...initialState,
  setStep: (step) => set({ step }),
  setEmail: (email) => set({ email }),
  setShippingAddress: (address) => set({ shippingAddress: address }),
  setBillingAddress: (address) => set({ billingAddress: address }),
  setSameAsShipping: (same) => set({ sameAsShipping: same }),
  setPaymentMethod: (method) => set({ paymentMethod: method }),
  setShippingMethod: (method) => set({ shippingMethod: method }),
  setInternalOrderId: (id) => set({ internalOrderId: id }),
  setPaymentIntentId: (id) => set({ paymentIntentId: id }),
  reset: () => set(initialState),
}));