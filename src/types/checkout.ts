export interface Address {
  firstName: string;
  lastName: string;
  streetAddress: string;
  apartment?: string;
  city: string;
  country: string;
  state: string;
  zipCode: string;
  phone: string;
}

export interface PaymentMethod {
  type: 'card';
  // We'll expand this later when implementing actual payment integration
}

export interface ShippingMethod {
  id: string;
  name: string;
  price: number;
  description: string;
}

export type CheckoutStep = 'information' | 'shipping' | 'payment' | 'confirmation';

export interface CheckoutState {
  step: CheckoutStep;
  shippingAddress: Address | null;
  billingAddress: Address | null;
  sameAsShipping: boolean;
  paymentMethod: PaymentMethod | null;
  shippingMethod: ShippingMethod | null;
  email: string;
  internalOrderId?: string;
  paymentIntentId?: string;
}