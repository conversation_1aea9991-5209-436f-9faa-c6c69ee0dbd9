export interface Address {
  firstName: string;
  lastName: string;
  streetAddress: string;
  apartment?: string;
  city: string;
  country: string;
  state: string;
  zipCode: string;
  phone: string;
}

export interface PaymentMethod {
  type: 'card' | 'manual_bank_transfer';
  // We'll expand this later when implementing actual payment integration
}

export interface ShippingMethod {
  id: string;
  name: string;
  price: number;
  description: string;
}

export type CheckoutStep = 'information' | 'shipping' | 'payment' | 'confirmation';

export interface CheckoutState {
  step: CheckoutStep;
  shippingAddress: Address | null;
  billingAddress: Address | null;
  sameAsShipping: boolean;
  paymentMethod: PaymentMethod | null;
  shippingMethod: ShippingMethod | null;
  email: string;
  internalOrderId?: string;
  paymentIntentId?: string;
  paymentInstructions?: PaymentInstructions;
}

export interface PaymentInstructions {
  orderId: string;
  orderReference: string;
  bankAccountDetails: BankAccountDetails;
  amount: number;
  currency: string;
  instructions: string;
  dueDate?: Date;
}

export interface BankAccountDetails {
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber?: string;
  swiftCode?: string;
  iban?: string;
  bsb?: string;
  sortCode?: string;
  branchCode?: string;
  currency: string;
  instructions?: string;
}