import { Order, OrderItem, Address } from './index';

// Payment processor types
export type PaymentProcessor = 'stripe' | 'manual' | 'paypal' | 'square';

// Payment method types
export type PaymentMethodType = 'credit_card' | 'bank_transfer' | 'manual_bank_transfer' | 'cash_on_delivery';

// Bank account details for manual payments
export interface BankAccountDetails {
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber?: string; // For US banks
  swiftCode?: string; // For international transfers
  iban?: string; // For European banks
  bsb?: string; // For Australian banks
  sortCode?: string; // For UK banks
  branchCode?: string;
  currency: string;
  instructions?: string;
}

// Payment settings interface
export interface PaymentSettings {
  // General settings
  currencyCode: string;
  currencySymbol: string;
  
  // Stripe settings
  stripeEnabled: boolean;
  stripePublicKey?: string;
  stripeSecretKey?: string;
  
  // PayPal settings
  paypalEnabled: boolean;
  paypalClientId?: string;
  paypalSecretKey?: string;
  
  // Manual payment settings
  manualPaymentEnabled: boolean;
  bankAccountDetails?: BankAccountDetails;
  paymentInstructions?: string;
  
  // Other payment methods
  codEnabled: boolean;
  
  updatedAt?: Date;
}

// Payment instruction data
export interface PaymentInstructions {
  orderId: string;
  orderReference: string;
  bankAccountDetails: BankAccountDetails;
  amount: number;
  currency: string;
  instructions: string;
  dueDate?: Date;
}

// Payment processor interface
export interface PaymentProcessor {
  name: string;
  type: PaymentProcessor;
  isEnabled: boolean;
  
  // Create payment intent/session
  createPayment(orderData: CreatePaymentRequest): Promise<CreatePaymentResponse>;
  
  // Verify payment status
  verifyPayment(paymentId: string): Promise<PaymentVerificationResult>;
  
  // Handle webhook events
  handleWebhook?(payload: any): Promise<WebhookResult>;
}

// Create payment request
export interface CreatePaymentRequest {
  orderId: string;
  userId: string;
  email: string;
  items: OrderItem[];
  shippingAddress: Address;
  billingAddress: Address;
  shippingMethod: {
    id: string;
    name: string;
    price: number;
  };
  subtotal: number;
  shippingCost: number;
  total: number;
  currency: string;
}

// Create payment response
export interface CreatePaymentResponse {
  success: boolean;
  paymentId?: string;
  clientSecret?: string;
  redirectUrl?: string;
  paymentInstructions?: PaymentInstructions;
  error?: string;
}

// Payment verification result
export interface PaymentVerificationResult {
  success: boolean;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentId: string;
  amount?: number;
  currency?: string;
  error?: string;
}

// Webhook result
export interface WebhookResult {
  success: boolean;
  orderId?: string;
  status?: 'paid' | 'failed' | 'cancelled';
  error?: string;
}

// Manual payment processor implementation
export class ManualPaymentProcessor implements PaymentProcessor {
  name = 'Manual Bank Transfer';
  type: PaymentProcessor = 'manual';
  isEnabled = true;
  
  private bankAccountDetails: BankAccountDetails;
  private paymentInstructions: string;
  
  constructor(bankAccountDetails: BankAccountDetails, paymentInstructions: string) {
    this.bankAccountDetails = bankAccountDetails;
    this.paymentInstructions = paymentInstructions;
  }
  
  async createPayment(orderData: CreatePaymentRequest): Promise<CreatePaymentResponse> {
    try {
      // Generate order reference for bank transfer
      const orderReference = `ORD-${orderData.orderId.slice(-8).toUpperCase()}`;
      
      const paymentInstructions: PaymentInstructions = {
        orderId: orderData.orderId,
        orderReference,
        bankAccountDetails: this.bankAccountDetails,
        amount: orderData.total,
        currency: orderData.currency,
        instructions: this.paymentInstructions.replace('{orderReference}', orderReference),
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
      };
      
      return {
        success: true,
        paymentId: `manual_${orderData.orderId}`,
        paymentInstructions
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create manual payment'
      };
    }
  }
  
  async verifyPayment(paymentId: string): Promise<PaymentVerificationResult> {
    // For manual payments, verification is always pending until admin confirms
    return {
      success: true,
      status: 'pending',
      paymentId
    };
  }
}

// Payment processor factory
export class PaymentProcessorFactory {
  static create(type: PaymentProcessor, settings: PaymentSettings): PaymentProcessor | null {
    switch (type) {
      case 'manual':
        if (settings.manualPaymentEnabled && settings.bankAccountDetails) {
          return new ManualPaymentProcessor(
            settings.bankAccountDetails,
            settings.paymentInstructions || 'Please transfer the amount to the bank account details provided and include the order reference in your transfer description.'
          );
        }
        return null;
      
      case 'stripe':
        // Future implementation for Stripe
        return null;
      
      case 'paypal':
        // Future implementation for PayPal
        return null;
      
      default:
        return null;
    }
  }
}
