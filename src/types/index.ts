// Core entity types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  compareAtPrice?: number;
  images: string[];
  category: string;
  categoryId: string;
  variants?: ProductVariant[];
  stock?: number;
  status: 'active' | 'inactive' | 'draft' | 'archived';
  createdAt?: Date;
  updatedAt?: Date;
  slug?: string;
  featured?: boolean;
  ratingAvg?: number;
  ratingCount?: number;
  features?: string[];
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  weight?: number;
  sku?: string;
  barcode?: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  price?: number;
  compareAtPrice?: number;
  stock?: number;
  images?: string[];
  attributes: {
    [key: string]: string; // e.g., { size: "large", color: "red" }
  };
}

export interface Order {
  id: string;
  userId: string;
  email?: string;
  items: OrderItem[];
  status: OrderStatus;
  statusHistory?: Array<{
    status: OrderStatus;
    timestamp: Date;
  }>;
  shippingAddress: Address;
  billingAddress: Address;
  shippingMethodDetails?: {
    id: string;
    name: string;
    price: number;
  };
  paymentMethod: PaymentMethod;
  paymentStatus?: PaymentStatus;
  subtotal: number;
  orderSubtotal?: number; // Alternative name for subtotal
  shippingCost?: number;
  shipping?: number; // Alternative name for shippingCost
  tax?: number;
  discount?: number;
  total: number;
  currency?: string;
  notes?: string;
  noteCount?: number;
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
  trackingNumber?: string;
  estimatedDelivery?: Date;
  customerNotes?: string; // Notes from customer
  adminNotes?: string; // Internal notes for admin
  stripePaymentIntentId?: string; // Stripe payment intent ID
  stripeChargeId?: string; // Stripe charge ID
  receiptUrl?: string; // URL to payment receipt
  paymentError?: string; // Error message if payment failed
}

export type OrderStatus =
  | 'pending'
  | 'pending_payment'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled';

export interface OrderItem extends CartItem {
  subtotal: number;
  slug?: string;
}

export interface Address {
  // New format
  fullName?: string;
  streetAddress: string;
  city: string;
  country: string;
  postalCode?: string;
  phone: string;
  email?: string;

  // Legacy format
  firstName?: string;
  lastName?: string;
  apartment?: string;
  state?: string;
  zipCode?: string;
}

export type PaymentMethod = 'credit_card' | 'paynow' | 'bank_transfer' | 'cash_on_delivery';

export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// Cart Types
export interface CartItem {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variantId?: string;
  variantName?: string;
}

// User Types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  addresses?: Address[];
  isAdmin?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Review Types
export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  rating: number;
  title: string;
  comment: string;
  createdAt: {
    toDate: () => Date;
  };
  updatedAt?: {
    toDate: () => Date;
  };
}

export interface Category {
  id: string;
  name: string;
}
