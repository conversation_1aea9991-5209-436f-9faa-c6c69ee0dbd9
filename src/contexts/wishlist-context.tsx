'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  addToWishlist as addToWishlistService,
  removeFromWishlist as removeFromWishlistService,
  getWishlist as getWishlistService,
  isProductInWishlist as checkWishlistService
} from '@/lib/firebase/services/wishlist-service';
import { useAuth } from './auth-context';
import { Product } from '@/types';
import { useToast } from '@/components/ui/use-toast';

interface WishlistContextType {
  wishlist: Product[];
  isLoading: boolean;
  isInWishlist: (productId: string) => boolean;
  addToWishlist: (product: Product) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({ children }: { children: ReactNode }) {
  const [wishlist, setWishlist] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch wishlist when user changes
  useEffect(() => {
    const fetchWishlist = async () => {
      if (!user) {
        setWishlist([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const wishlistItems = await getWishlistService(user.uid);
        setWishlist(wishlistItems);
      } catch (error) {
        console.error('Error fetching wishlist:', error);
        toast({
          title: "Error",
          description: "Failed to load your wishlist. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchWishlist();
  }, [user, toast]);

  // Check if a product is in the wishlist
  const isInWishlist = (productId: string): boolean => {
    return wishlist.some(item => item.id === productId);
  };

  // Add a product to the wishlist
  const addToWishlist = async (product: Product): Promise<void> => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to add items to your wishlist.",
        variant: "destructive",
      });
      return;
    }

    try {
      await addToWishlistService(user.uid, product.id);
      
      // Update local state
      setWishlist(prev => [...prev, product]);
      
      toast({
        title: "Added to Wishlist",
        description: `${product.name} has been added to your wishlist.`,
        variant: "default",
      });
    } catch (error) {
      // If the error is because the product is already in the wishlist, don't show an error
      if ((error as Error).message === 'Product already in wishlist') {
        toast({
          title: "Already in Wishlist",
          description: `${product.name} is already in your wishlist.`,
          variant: "default",
        });
        return;
      }
      
      console.error('Error adding to wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to add item to wishlist. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Remove a product from the wishlist
  const removeFromWishlist = async (productId: string): Promise<void> => {
    if (!user) return;

    try {
      await removeFromWishlistService(user.uid, productId);
      
      // Update local state
      setWishlist(prev => prev.filter(item => item.id !== productId));
      
      toast({
        title: "Removed from Wishlist",
        description: "Item has been removed from your wishlist.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from wishlist. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <WishlistContext.Provider
      value={{
        wishlist,
        isLoading,
        isInWishlist,
        addToWishlist,
        removeFromWishlist,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
}

export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
