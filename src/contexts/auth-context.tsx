'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { auth } from '@/lib/firebase/config';
import {
  User,
  onAuthStateChange,
  signInUser,
  signOutUser,
  registerUser,
  resetPassword,
  signInWithGoogle,
  signInWithFacebook,
  sendVerificationEmail,
  isEmailVerified
} from '@/lib/firebase/services/auth-service';
import { useToast } from '@/components/ui/use-toast';

interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithFacebook: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  isEmailVerified: () => boolean;
  getIdToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const unsubscribe = onAuthStateChange((user) => {
      setLoading(true);
      if (user) {
        // The user object from onAuthStateChange already has isAdmin property
        // from the Firestore data that was fetched in auth-service.ts
        setUser(user);
        setIsAdmin(user.isAdmin || false);
      } else {
        setUser(null);
        setIsAdmin(false);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleSignIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const user = await signInUser(email, password);
      setUser(user);
      toast({
        title: "Success",
        description: "You have been signed in successfully.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Sign in error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to sign in. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (email: string, password: string, displayName: string) => {
    try {
      setLoading(true);
      const user = await registerUser(email, password, displayName);
      setUser(user);
      toast({
        title: "Success",
        description: "Your account has been created successfully.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Sign up error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create account. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);
      await signOutUser();
      setUser(null);
      toast({
        title: "Success",
        description: "You have been signed out successfully.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Sign out error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to sign out. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (email: string) => {
    try {
      setLoading(true);
      await resetPassword(email);
      toast({
        title: "Success",
        description: "Password reset email has been sent.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Reset password error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to send reset email. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSignInWithGoogle = async () => {
    try {
      setLoading(true);
      const user = await signInWithGoogle();
      setUser(user);
      toast({
        title: "Success",
        description: "You have been signed in with Google successfully.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Google sign in error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to sign in with Google. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSignInWithFacebook = async () => {
    try {
      setLoading(true);
      const user = await signInWithFacebook();
      setUser(user);
      toast({
        title: "Success",
        description: "You have been signed in with Facebook successfully.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Facebook sign in error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to sign in with Facebook. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleSendVerificationEmail = async () => {
    try {
      setLoading(true);
      await sendVerificationEmail();
      toast({
        title: "Success",
        description: "Verification email has been sent. Please check your inbox.",
        variant: "default",
      });
    } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
      console.error('Send verification email error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to send verification email. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleIsEmailVerified = () => {
    return isEmailVerified();
  };

  const handleGetIdToken = async () => {
    const firebaseUser = auth.currentUser;
    return firebaseUser ? await firebaseUser.getIdToken() : null;
  };

  const value = {
    user,
    isAdmin,
    loading,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    signInWithGoogle: handleSignInWithGoogle,
    signInWithFacebook: handleSignInWithFacebook,
    sendVerificationEmail: handleSendVerificationEmail,
    isEmailVerified: handleIsEmailVerified,
    getIdToken: handleGetIdToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
