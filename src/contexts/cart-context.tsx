'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { CartItem, Product, ProductVariant } from '@/types';
import { 
  getCart, 
  saveCart, 
  addToCart as addItemToCart,
  updateCartItemQuantity as updateItemQuantity,
  removeFromCart as removeItemFromCart,
  clearCart as clearCartItems,
  calculateCartTotals
} from '@/lib/cart';
import { useToast } from '@/components/ui/use-toast';

interface CartContextType {
  cart: CartItem[];
  itemCount: number;
  subtotal: number;
  addToCart: (product: Product, quantity: number, variant?: ProductVariant) => void;
  updateQuantity: (productId: string, variantId: string | undefined, quantity: number) => void;
  removeItem: (productId: string, variantId: string | undefined) => void;
  clearCart: () => void;
  isCartOpen: boolean;
  openCart: () => void;
  closeCart: () => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { toast } = useToast();
  
  // Calculate totals
  const { subtotal, itemCount } = calculateCartTotals(cart);
  
  // Initialize cart from local storage
  useEffect(() => {
    setCart(getCart());
  }, []);
  
  // Add item to cart
  const addToCart = (product: Product, quantity: number, variant?: ProductVariant) => {
    const item: CartItem = {
      productId: product.id,
      name: product.name,
      price: variant?.price || product.price,
      quantity,
      image: product.images[0] || '',
      variantId: variant?.id,
      variantName: variant?.name,
    };
    
    const updatedCart = addItemToCart(item);
    setCart(updatedCart);
    
    toast({
      title: "Added to cart",
      description: `${quantity} × ${product.name}${variant ? ` (${variant.name})` : ''} added to your cart.`,
      variant: "default",
    });
    
    // Open cart drawer
    setIsCartOpen(true);
  };
  
  // Update item quantity
  const updateQuantity = (productId: string, variantId: string | undefined, quantity: number) => {
    const updatedCart = updateItemQuantity(productId, variantId, quantity);
    setCart(updatedCart);
  };
  
  // Remove item from cart
  const removeItem = (productId: string, variantId: string | undefined) => {
    const updatedCart = removeItemFromCart(productId, variantId);
    setCart(updatedCart);
    
    toast({
      title: "Item removed",
      description: "Item has been removed from your cart.",
      variant: "default",
    });
  };
  
  // Clear cart
  const clearCart = () => {
    clearCartItems();
    setCart([]);
  };
  
  // Open cart drawer
  const openCart = () => {
    setIsCartOpen(true);
  };
  
  // Close cart drawer
  const closeCart = () => {
    setIsCartOpen(false);
  };
  
  return (
    <CartContext.Provider
      value={{
        cart,
        itemCount,
        subtotal,
        addToCart,
        updateQuantity,
        removeItem,
        clearCart,
        isCartOpen,
        openCart,
        closeCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
