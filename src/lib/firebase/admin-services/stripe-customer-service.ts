import { getAdminFirestore } from '@/lib/firebase/admin';
import { getPaymentSettingsAdmin } from './settings-service';
import Stripe from 'stripe';

const STRIPE_CUSTOMERS_COLLECTION = 'stripe_customers';

export interface StripeCustomerData {
  userId: string;
  stripeCustomerId: string;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Get or create a Stripe customer for a user
 * This is the core function that ensures every user has a Stripe customer
 */
export async function getOrCreateStripeCustomer(
  userId: string,
  email: string,
  name?: string
): Promise<string> {
  try {
    const db = getAdminFirestore();
    
    // First, check if we already have a Stripe customer for this user
    const existingCustomerDoc = await db
      .collection(STRIPE_CUSTOMERS_COLLECTION)
      .where('userId', '==', userId)
      .limit(1)
      .get();

    if (!existingCustomerDoc.empty) {
      const customerData = existingCustomerDoc.docs[0].data() as StripeCustomerData;
      return customerData.stripeCustomerId;
    }

    // Get Stripe configuration
    const paymentSettings = await getPaymentSettingsAdmin();
    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      throw new Error('Stripe is not configured');
    }

    // Initialize Stripe
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil',
    });

    // Create new Stripe customer
    const stripeCustomer = await stripe.customers.create({
      email,
      name,
      metadata: {
        userId, // CRITICAL: Always include userId in metadata
        source: 'belinda-ecommerce',
      },
    });

    // Store the customer mapping in Firestore
    const customerData: StripeCustomerData = {
      userId,
      stripeCustomerId: stripeCustomer.id,
      email,
      name,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        stripeCreatedAt: stripeCustomer.created,
      },
    };

    await db.collection(STRIPE_CUSTOMERS_COLLECTION).add(customerData);

    console.log(`Created Stripe customer ${stripeCustomer.id} for user ${userId}`);
    return stripeCustomer.id;
  } catch (error) {
    console.error('Error getting or creating Stripe customer:', error);
    throw error;
  }
}

/**
 * Get Stripe customer ID for a user
 */
export async function getStripeCustomerId(userId: string): Promise<string | null> {
  try {
    const db = getAdminFirestore();
    
    const customerDoc = await db
      .collection(STRIPE_CUSTOMERS_COLLECTION)
      .where('userId', '==', userId)
      .limit(1)
      .get();

    if (customerDoc.empty) {
      return null;
    }

    const customerData = customerDoc.docs[0].data() as StripeCustomerData;
    return customerData.stripeCustomerId;
  } catch (error) {
    console.error('Error getting Stripe customer ID:', error);
    return null;
  }
}

/**
 * Update customer information in both Stripe and Firestore
 */
export async function updateStripeCustomer(
  userId: string,
  updates: { email?: string; name?: string; metadata?: Record<string, any> }
): Promise<void> {
  try {
    const stripeCustomerId = await getStripeCustomerId(userId);
    if (!stripeCustomerId) {
      throw new Error('Stripe customer not found for user');
    }

    // Get Stripe configuration
    const paymentSettings = await getPaymentSettingsAdmin();
    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      throw new Error('Stripe is not configured');
    }

    // Initialize Stripe
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil',
    });

    // Update in Stripe
    const updateData: Stripe.CustomerUpdateParams = {};
    if (updates.email) updateData.email = updates.email;
    if (updates.name) updateData.name = updates.name;
    if (updates.metadata) {
      updateData.metadata = {
        ...updates.metadata,
        userId, // Always preserve userId
      };
    }

    await stripe.customers.update(stripeCustomerId, updateData);

    // Update in Firestore
    const db = getAdminFirestore();
    const customerQuery = await db
      .collection(STRIPE_CUSTOMERS_COLLECTION)
      .where('userId', '==', userId)
      .limit(1)
      .get();

    if (!customerQuery.empty) {
      const docRef = customerQuery.docs[0].ref;
      await docRef.update({
        ...updates,
        updatedAt: new Date(),
      });
    }

    console.log(`Updated Stripe customer ${stripeCustomerId} for user ${userId}`);
  } catch (error) {
    console.error('Error updating Stripe customer:', error);
    throw error;
  }
}

/**
 * Get user ID from Stripe customer ID
 */
export async function getUserIdFromStripeCustomer(stripeCustomerId: string): Promise<string | null> {
  try {
    const db = getAdminFirestore();
    
    const customerDoc = await db
      .collection(STRIPE_CUSTOMERS_COLLECTION)
      .where('stripeCustomerId', '==', stripeCustomerId)
      .limit(1)
      .get();

    if (customerDoc.empty) {
      return null;
    }

    const customerData = customerDoc.docs[0].data() as StripeCustomerData;
    return customerData.userId;
  } catch (error) {
    console.error('Error getting user ID from Stripe customer:', error);
    return null;
  }
}
