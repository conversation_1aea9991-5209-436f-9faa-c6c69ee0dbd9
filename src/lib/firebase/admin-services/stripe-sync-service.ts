import { getAdminFirestore } from '@/lib/firebase/admin';
import { getPaymentSettingsAdmin } from './settings-service';
import { getUserIdFromStripeCustomer } from './stripe-customer-service';
import Stripe from 'stripe';
import { FieldValue } from 'firebase-admin/firestore';

const STRIPE_SYNC_COLLECTION = 'stripe_sync_data';

export interface StripeCustomerSyncData {
  userId: string;
  stripeCustomerId: string;
  // Payment Methods
  defaultPaymentMethod?: {
    id: string;
    type: string;
    card?: {
      brand: string;
      last4: string;
      exp_month: number;
      exp_year: number;
    };
  };
  // Payment History Summary
  totalSpent: number;
  totalOrders: number;
  lastPaymentAt?: Date;
  // Subscription Data (if applicable)
  subscriptions: Array<{
    id: string;
    status: string;
    priceId?: string;
    currentPeriodStart?: Date;
    currentPeriodEnd?: Date;
    cancelAtPeriodEnd?: boolean;
  }>;
  // Sync metadata
  lastSyncAt: Date;
  syncVersion: number;
}

/**
 * Central function to sync all Stripe data for a customer
 * This is the core sync function recommended in the best practices
 */
export async function syncStripeDataToFirestore(stripeCustomerId: string): Promise<StripeCustomerSyncData | null> {
  try {
    // Get Stripe configuration
    const paymentSettings = await getPaymentSettingsAdmin();
    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      throw new Error('Stripe is not configured');
    }

    // Initialize Stripe
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil',
    });

    // Get user ID from customer mapping
    const userId = await getUserIdFromStripeCustomer(stripeCustomerId);
    if (!userId) {
      console.error(`No user found for Stripe customer ${stripeCustomerId}`);
      return null;
    }

    // Fetch customer data from Stripe
    const customer = await stripe.customers.retrieve(stripeCustomerId, {
      expand: ['default_source', 'invoice_settings.default_payment_method'],
    });

    if (customer.deleted) {
      console.warn(`Stripe customer ${stripeCustomerId} has been deleted`);
      return null;
    }

    // Fetch payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: stripeCustomerId,
      limit: 10,
    });

    // Fetch recent payment intents for spending calculation
    const paymentIntents = await stripe.paymentIntents.list({
      customer: stripeCustomerId,
      limit: 100,
    });

    // Calculate totals
    const successfulPayments = paymentIntents.data.filter(pi => pi.status === 'succeeded');
    const totalSpent = successfulPayments.reduce((sum, pi) => sum + pi.amount, 0) / 100; // Convert from cents
    const totalOrders = successfulPayments.length;
    const lastPaymentAt = successfulPayments.length > 0 
      ? new Date(successfulPayments[0].created * 1000) 
      : undefined;

    // Fetch subscriptions (if any)
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      limit: 10,
      expand: ['data.default_payment_method'],
    });

    // Prepare default payment method data
    let defaultPaymentMethod;
    if (paymentMethods.data.length > 0) {
      const pm = paymentMethods.data[0];
      defaultPaymentMethod = {
        id: pm.id,
        type: pm.type,
        card: pm.card ? {
          brand: pm.card.brand,
          last4: pm.card.last4,
          exp_month: pm.card.exp_month,
          exp_year: pm.card.exp_year,
        } : undefined,
      };
    }

    // Prepare subscription data
    const subscriptionData = subscriptions.data.map(sub => ({
      id: sub.id,
      status: sub.status,
      priceId: sub.items.data[0]?.price.id,
      currentPeriodStart: new Date(sub.current_period_start * 1000),
      currentPeriodEnd: new Date(sub.current_period_end * 1000),
      cancelAtPeriodEnd: sub.cancel_at_period_end,
    }));

    // Create sync data object
    const syncData: StripeCustomerSyncData = {
      userId,
      stripeCustomerId,
      defaultPaymentMethod,
      totalSpent,
      totalOrders,
      lastPaymentAt,
      subscriptions: subscriptionData,
      lastSyncAt: new Date(),
      syncVersion: 1,
    };

    // Store in Firestore
    const db = getAdminFirestore();
    const docRef = db.collection(STRIPE_SYNC_COLLECTION).doc(userId);
    await docRef.set(syncData, { merge: true });

    console.log(`Synced Stripe data for customer ${stripeCustomerId} (user ${userId})`);
    return syncData;

  } catch (error) {
    console.error('Error syncing Stripe data:', error);
    throw error;
  }
}

/**
 * Get synced Stripe data for a user
 */
export async function getStripeDataForUser(userId: string): Promise<StripeCustomerSyncData | null> {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(STRIPE_SYNC_COLLECTION).doc(userId);
    const doc = await docRef.get();

    if (!doc.exists) {
      return null;
    }

    const data = doc.data() as StripeCustomerSyncData;
    
    // Convert Firestore timestamps back to Date objects
    if (data.lastPaymentAt && typeof data.lastPaymentAt !== 'object') {
      data.lastPaymentAt = new Date(data.lastPaymentAt);
    }
    if (data.lastSyncAt && typeof data.lastSyncAt !== 'object') {
      data.lastSyncAt = new Date(data.lastSyncAt);
    }

    return data;
  } catch (error) {
    console.error('Error getting Stripe data for user:', error);
    return null;
  }
}

/**
 * Check if sync data is stale and needs refresh
 */
export function isSyncDataStale(syncData: StripeCustomerSyncData, maxAgeMinutes: number = 30): boolean {
  const now = new Date();
  const syncAge = now.getTime() - syncData.lastSyncAt.getTime();
  const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
  
  return syncAge > maxAge;
}

/**
 * Sync data if stale, otherwise return cached data
 */
export async function getOrSyncStripeData(userId: string, forceSync: boolean = false): Promise<StripeCustomerSyncData | null> {
  try {
    // Get existing data
    const existingData = await getStripeDataForUser(userId);
    
    // If no data exists or force sync is requested, sync now
    if (!existingData || forceSync) {
      // We need the Stripe customer ID to sync
      const db = getAdminFirestore();
      const customerDoc = await db
        .collection('stripe_customers')
        .where('userId', '==', userId)
        .limit(1)
        .get();

      if (customerDoc.empty) {
        console.warn(`No Stripe customer found for user ${userId}`);
        return null;
      }

      const customerData = customerDoc.docs[0].data();
      return await syncStripeDataToFirestore(customerData.stripeCustomerId);
    }

    // If data exists but is stale, sync in background and return existing data
    if (isSyncDataStale(existingData)) {
      // Trigger background sync (don't await)
      const db = getAdminFirestore();
      const customerDoc = await db
        .collection('stripe_customers')
        .where('userId', '==', userId)
        .limit(1)
        .get();

      if (!customerDoc.empty) {
        const customerData = customerDoc.docs[0].data();
        syncStripeDataToFirestore(customerData.stripeCustomerId).catch(error => {
          console.error('Background sync failed:', error);
        });
      }
    }

    return existingData;
  } catch (error) {
    console.error('Error getting or syncing Stripe data:', error);
    return null;
  }
}
