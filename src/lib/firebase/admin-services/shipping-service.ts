import { getAdminFirestore } from '@/lib/firebase/admin';
import { FieldValue } from 'firebase-admin/firestore';

const SHIPPING_METHODS_COLLECTION = 'shippingMethods';

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDeliveryDays: {
    min: number;
    max: number;
  };
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Convert Firestore data to ShippingMethod
const convertShippingMethod = (id: string, data: any): ShippingMethod => {
  return {
    id,
    name: data.name,
    description: data.description,
    price: data.price,
    estimatedDeliveryDays: data.estimatedDeliveryDays || { min: 3, max: 7 },
    isActive: data.isActive !== undefined ? data.isActive : true,
    createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
    updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt
  };
};

// Get all shipping methods (admin version)
export const getAllShippingMethodsAdmin = async (includeInactive = false): Promise<ShippingMethod[]> => {
  try {
    const db = getAdminFirestore();
    let query = db.collection(SHIPPING_METHODS_COLLECTION).orderBy('price', 'asc');
    
    if (!includeInactive) {
      query = db.collection(SHIPPING_METHODS_COLLECTION)
        .where('isActive', '==', true)
        .orderBy('price', 'asc');
    }
    
    const querySnapshot = await query.get();
    
    return querySnapshot.docs.map(doc => 
      convertShippingMethod(doc.id, doc.data())
    );
  } catch (error) {
    console.error('Error getting shipping methods (admin):', error);
    throw error;
  }
};

// Get a shipping method by ID (admin version)
export const getShippingMethodByIdAdmin = async (id: string): Promise<ShippingMethod | null> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SHIPPING_METHODS_COLLECTION).doc(id);
    const docSnap = await docRef.get();
    
    if (docSnap.exists) {
      return convertShippingMethod(docSnap.id, docSnap.data());
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting shipping method ${id} (admin):`, error);
    throw error;
  }
};

// Create a shipping method (admin version)
export const createShippingMethodAdmin = async (shippingMethod: Omit<ShippingMethod, 'id'>): Promise<string> => {
  try {
    const db = getAdminFirestore();
    const docRef = await db.collection(SHIPPING_METHODS_COLLECTION).add({
      ...shippingMethod,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp()
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating shipping method (admin):', error);
    throw error;
  }
};

// Update a shipping method (admin version)
export const updateShippingMethodAdmin = async (id: string, shippingMethod: Partial<ShippingMethod>): Promise<void> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SHIPPING_METHODS_COLLECTION).doc(id);
    
    await docRef.update({
      ...shippingMethod,
      updatedAt: FieldValue.serverTimestamp()
    });
  } catch (error) {
    console.error(`Error updating shipping method ${id} (admin):`, error);
    throw error;
  }
};

// Delete a shipping method (admin version)
export const deleteShippingMethodAdmin = async (id: string): Promise<void> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SHIPPING_METHODS_COLLECTION).doc(id);
    await docRef.delete();
  } catch (error) {
    console.error(`Error deleting shipping method ${id} (admin):`, error);
    throw error;
  }
};

// Initialize default shipping methods if none exist (admin version)
export const initializeDefaultShippingMethodsAdmin = async (): Promise<void> => {
  try {
    const methods = await getAllShippingMethodsAdmin(true);
    
    if (methods.length === 0) {
      // Create standard shipping
      await createShippingMethodAdmin({
        name: 'Standard Shipping',
        description: '5-7 business days',
        price: 5.99,
        estimatedDeliveryDays: { min: 5, max: 7 },
        isActive: true
      });
      
      // Create express shipping
      await createShippingMethodAdmin({
        name: 'Express Shipping',
        description: '2-3 business days',
        price: 15.99,
        estimatedDeliveryDays: { min: 2, max: 3 },
        isActive: true
      });
      
      console.log('Default shipping methods initialized (admin)');
    }
  } catch (error) {
    console.error('Error initializing default shipping methods (admin):', error);
    throw error;
  }
};
