import { getAdminStorage } from '../admin';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

/**
 * Upload image with compression (server-side)
 * @param buffer The image buffer
 * @param originalName Original file name
 * @param path Storage path (e.g., 'products', 'categories')
 * @param mimeType MIME type of the image
 * @returns Promise with the download URL
 */
export async function uploadImageAdmin(
  buffer: Buffer,
  originalName: string,
  path: string,
  mimeType: string
): Promise<string> {
  try {
    const storage = getAdminStorage();
    const bucket = storage.bucket();
    
    // Clean the original name to create a safe filename
    const cleanName = originalName.replace(/[^a-zA-Z0-9.]/g, '_');
    
    // Generate a unique filename
    const fileName = `${path}/${uuidv4()}-${cleanName}`;
    const fileRef = bucket.file(fileName);
    
    // Determine output format based on mime type
    let outputOptions = {};
    let outputFormat: keyof sharp.FormatEnum = 'jpeg';
    
    if (mimeType === 'image/png') {
      outputFormat = 'png';
      outputOptions = { quality: 90 };
    } else if (mimeType === 'image/webp') {
      outputFormat = 'webp';
      outputOptions = { quality: 80 };
    } else if (mimeType === 'image/gif') {
      // For GIFs, don't use sharp as it will convert to static image
      await fileRef.save(buffer, {
        metadata: {
          contentType: mimeType,
        },
      });
      
      // Make the file publicly accessible
      await fileRef.makePublic();
      
      // Get the public URL
      return `https://storage.googleapis.com/${bucket.name}/${fileName}`;
    } else {
      // Default to JPEG
      outputOptions = { quality: 80 };
    }
    
    // Compress image with sharp
    const compressedBuffer = await sharp(buffer)
      .resize(1920, 1920, { fit: 'inside', withoutEnlargement: true })
      [outputFormat](outputOptions)
      .toBuffer();
    
    // Upload the file
    await fileRef.save(compressedBuffer, {
      metadata: {
        contentType: mimeType,
      },
    });
    
    // Make the file publicly accessible
    await fileRef.makePublic();
    
    // Get the public URL
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${fileName}`;
    
    return publicUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
}

/**
 * Delete image from Firebase Storage (server-side)
 * @param url The download URL of the image to delete
 */
export async function deleteImageAdmin(url: string): Promise<void> {
  try {
    const storage = getAdminStorage();
    const bucket = storage.bucket();
    
    // Extract the file path from the URL
    const urlObj = new URL(url);
    const filePath = urlObj.pathname.substring(1); // Remove leading slash
    
    // Delete the file
    await bucket.file(filePath).delete();
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
}

/**
 * Upload multiple images with compression (server-side)
 * @param files Array of { buffer, originalName, mimeType }
 * @param path Storage path
 * @returns Promise with array of download URLs
 */
export async function uploadMultipleImagesAdmin(
  files: Array<{ buffer: Buffer; originalName: string; mimeType: string }>,
  path: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => 
      uploadImageAdmin(file.buffer, file.originalName, path, file.mimeType)
    );
    return Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
}
