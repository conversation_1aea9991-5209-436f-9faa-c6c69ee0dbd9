import { getAdminFirestore } from '../admin';
import { FieldValue, CollectionReference, Query } from 'firebase-admin/firestore';

// Define special offer type
export interface SpecialOffer {
  id: string;
  title: string;
  description: string;
  image: string;
  linkUrl: string;
  linkText: string;
  isActive: boolean;
  startDate: Date;
  endDate: Date | null; // null means no end date
  createdAt: Date;
  updatedAt: Date;
}

// Define special offer input type (for creating/updating)
export interface SpecialOfferInput {
  title: string;
  description: string;
  image: string;
  linkUrl: string;
  linkText: string;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date | null;
}

/**
 * Get all special offers
 * @param activeOnly Whether to return only active offers
 * @returns Array of special offers
 */
export async function getSpecialOffers(activeOnly = false): Promise<SpecialOffer[]> {
  try {
    const db = getAdminFirestore();
    let query: CollectionReference | Query = db.collection('specialOffers');
    
    if (activeOnly) {
      const now = new Date();
      query = query
        .where('isActive', '==', true)
        .where('startDate', '<=', now);
    }
    
    const snapshot = await query.get();
    
    if (snapshot.empty) {
      return [];
    }
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        image: data.image,
        linkUrl: data.linkUrl,
        linkText: data.linkText,
        isActive: data.isActive,
        startDate: data.startDate?.toDate(),
        endDate: data.endDate?.toDate() || null,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as SpecialOffer;
    });
  } catch (error) {
    console.error('Error getting special offers:', error);
    throw error;
  }
}

/**
 * Get a special offer by ID
 * @param id Offer ID
 * @returns Special offer or null if not found
 */
export async function getSpecialOfferById(id: string): Promise<SpecialOffer | null> {
  try {
    const db = getAdminFirestore();
    const doc = await db.collection('specialOffers').doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    const data = doc.data();
    return {
      id: doc.id,
      title: data?.title,
      description: data?.description,
      image: data?.image,
      linkUrl: data?.linkUrl,
      linkText: data?.linkText,
      isActive: data?.isActive,
      startDate: data?.startDate?.toDate(),
      endDate: data?.endDate?.toDate() || null,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate(),
    } as SpecialOffer;
  } catch (error) {
    console.error(`Error getting special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new special offer
 * @param offerData Special offer data
 * @returns ID of the created special offer
 */
export async function createSpecialOffer(offerData: SpecialOfferInput): Promise<string> {
  try {
    const db = getAdminFirestore();
    
    // Set default values if not provided
    const now = new Date();
    const startDate = offerData.startDate || now;
    
    // Create the new offer
    const offerRef = await db.collection('specialOffers').add({
      ...offerData,
      isActive: offerData.isActive !== undefined ? offerData.isActive : true,
      startDate: startDate,
      endDate: offerData.endDate || null,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });
    
    return offerRef.id;
  } catch (error) {
    console.error('Error creating special offer:', error);
    throw error;
  }
}

/**
 * Update a special offer
 * @param id Offer ID
 * @param offerData Updated special offer data
 */
export async function updateSpecialOffer(id: string, offerData: Partial<SpecialOfferInput>): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    // Update the offer
    await db.collection('specialOffers').doc(id).update({
      ...offerData,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a special offer
 * @param id Offer ID
 */
export async function deleteSpecialOffer(id: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    // Delete the offer
    await db.collection('specialOffers').doc(id).delete();
  } catch (error) {
    console.error(`Error deleting special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Toggle special offer active status
 * @param id Offer ID
 * @param isActive Whether the offer should be active
 */
export async function toggleSpecialOfferActive(id: string, isActive: boolean): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    await db.collection('specialOffers').doc(id).update({
      isActive,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error toggling special offer ${id} active status:`, error);
    throw error;
  }
}
