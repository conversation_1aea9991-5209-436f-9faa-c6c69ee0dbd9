import { getAdminFirestore } from '../admin';
import { FieldValue } from 'firebase-admin/firestore';

// Define new arrival type
export interface NewArrival {
  id: string;
  title: string;
  description: string;
  image: string;
  productIds: string[];
  isActive: boolean;
  startDate: Date;
  endDate: Date | null; // null means no end date
  createdAt: Date;
  updatedAt: Date;
}

// Define new arrival input type (for creating/updating)
export interface NewArrivalInput {
  title: string;
  description: string;
  image: string;
  productIds: string[];
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date | null;
}

/**
 * Get all new arrivals
 * @param activeOnly Whether to return only active new arrivals
 * @returns Array of new arrivals
 */
export async function getNewArrivals(activeOnly = false): Promise<NewArrival[]> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('newArrivals').orderBy('createdAt', 'desc');
    
    if (activeOnly) {
      const now = new Date();
      query = query
        .where('isActive', '==', true)
        .where('startDate', '<=', now);
    }
    
    const snapshot = await query.get();
    
    if (snapshot.empty) {
      return [];
    }
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        image: data.image,
        productIds: data.productIds || [],
        isActive: data.isActive,
        startDate: data.startDate?.toDate(),
        endDate: data.endDate?.toDate() || null,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as NewArrival;
    });
  } catch (error) {
    console.error('Error getting new arrivals:', error);
    throw error;
  }
}

/**
 * Get a new arrival by ID
 * @param id New arrival ID
 * @returns New arrival or null if not found
 */
export async function getNewArrivalById(id: string): Promise<NewArrival | null> {
  try {
    const db = getAdminFirestore();
    const doc = await db.collection('newArrivals').doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    const data = doc.data();
    return {
      id: doc.id,
      title: data?.title,
      description: data?.description,
      image: data?.image,
      productIds: data?.productIds || [],
      isActive: data?.isActive,
      startDate: data?.startDate?.toDate(),
      endDate: data?.endDate?.toDate() || null,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate(),
    } as NewArrival;
  } catch (error) {
    console.error(`Error getting new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new arrival
 * @param newArrivalData New arrival data
 * @returns ID of the created new arrival
 */
export async function createNewArrival(newArrivalData: NewArrivalInput): Promise<string> {
  try {
    const db = getAdminFirestore();
    
    // Set default values if not provided
    const now = new Date();
    const startDate = newArrivalData.startDate || now;
    
    // Create the new arrival
    const newArrivalRef = await db.collection('newArrivals').add({
      ...newArrivalData,
      isActive: newArrivalData.isActive !== undefined ? newArrivalData.isActive : true,
      startDate: startDate,
      endDate: newArrivalData.endDate || null,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });
    
    return newArrivalRef.id;
  } catch (error) {
    console.error('Error creating new arrival:', error);
    throw error;
  }
}

/**
 * Update a new arrival
 * @param id New arrival ID
 * @param newArrivalData Updated new arrival data
 */
export async function updateNewArrival(id: string, newArrivalData: Partial<NewArrivalInput>): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    // Update the new arrival
    await db.collection('newArrivals').doc(id).update({
      ...newArrivalData,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a new arrival
 * @param id New arrival ID
 */
export async function deleteNewArrival(id: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    // Delete the new arrival
    await db.collection('newArrivals').doc(id).delete();
  } catch (error) {
    console.error(`Error deleting new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Toggle new arrival active status
 * @param id New arrival ID
 * @param isActive Whether the new arrival should be active
 */
export async function toggleNewArrivalActive(id: string, isActive: boolean): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    await db.collection('newArrivals').doc(id).update({
      isActive,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error toggling new arrival ${id} active status:`, error);
    throw error;
  }
}
