import { getAdminFirestore } from '../admin';
import { FieldValue, WhereFilterOp, OrderByDirection, DocumentData, QueryDocumentSnapshot, Query } from 'firebase-admin/firestore';
import { Order, OrderStatus, OrderItem, Address, PaymentMethod, PaymentStatus } from '@/types';

// Order note interface
export interface OrderNote {
  id: string;
  orderId: string;
  userId: string;
  userName: string;
  content: string;
  isInternal: boolean;
  createdAt: Date;
}

const ORDERS_COLLECTION = 'orders';

// Order filter options interface
export interface OrderFilterOptions {
  status?: OrderStatus;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  minTotal?: number;
  maxTotal?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: OrderByDirection;
  limit?: number;
  startAfter?: DocumentData | QueryDocumentSnapshot<DocumentData>;
}

// Get orders with filtering and pagination
export async function getOrders(options: OrderFilterOptions = {}) {
  try {
    const db = getAdminFirestore();
    const ordersRef = db.collection(ORDERS_COLLECTION);

    // Start building the query
    let query = ordersRef as unknown as Query<DocumentData>;

    // Apply filters
    if (options.status) {
      query = query.where('status', '==', options.status);
    }

    if (options.userId) {
      query = query.where('userId', '==', options.userId);
    }

    if (options.startDate) {
      query = query.where('createdAt', '>=', options.startDate);
    }

    if (options.endDate) {
      query = query.where('createdAt', '<=', options.endDate);
    }

    if (options.minTotal !== undefined) {
      query = query.where('total', '>=', options.minTotal);
    }

    if (options.maxTotal !== undefined) {
      query = query.where('total', '<=', options.maxTotal);
    }

    // Apply sorting
    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder || 'desc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply cursor-based pagination
    if (options.startAfter) {
      query = query.startAfter(options.startAfter);
    }

    // Apply limit
    const limit = options.limit || 10;
    query = query.limit(limit + 1); // Get one extra to check if there are more results

    // Execute query
    const snapshot = await query.get();

    // Process results
    const orders = snapshot.docs.slice(0, limit).map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Order));

    // Check if there are more results
    const hasMore = snapshot.docs.length > limit;

    // Get the last document for next pagination
    const lastDoc = orders.length > 0 ? snapshot.docs[orders.length - 1] : null;

    // If search is provided, filter results client-side
    // This is a simple implementation - for production, consider using a search service
    let filteredOrders = orders;
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredOrders = orders.filter(order => {
        // Search in customer name
        const customerName = `${order.shippingAddress?.firstName || ''} ${order.shippingAddress?.lastName || ''}`;
        if (customerName.toLowerCase().includes(searchLower)) return true;

        // Search in customer email
        const email = order.shippingAddress?.email || '';
        if (email.toLowerCase().includes(searchLower)) return true;

        // Search in order ID
        if (order.id.toLowerCase().includes(searchLower)) return true;

        // Search in tracking number
        if (order.trackingNumber && order.trackingNumber.toLowerCase().includes(searchLower)) return true;

        return false;
      });
    }

    // Get total count (for initial load only)
    let total = 0;
    if (!options.startAfter) {
      // Create a simplified query for counting
      let countQuery = ordersRef as unknown as Query<DocumentData>;
      if (options.status) {
        countQuery = countQuery.where('status', '==', options.status);
      }
      if (options.userId) {
        countQuery = countQuery.where('userId', '==', options.userId);
      }

      const countSnapshot = await countQuery.count().get();
      total = countSnapshot.data().count;
    }

    return {
      orders: filteredOrders,
      cursor: lastDoc,
      hasMore,
      total
    };
  } catch (error) {
    console.error('Error getting orders:', error);
    throw error;
  }
}

// Get all orders (legacy method)
export async function getAllOrders(): Promise<Order[]> {
  try {
    const result = await getOrders({ limit: 100 });
    return result.orders;
  } catch (error) {
    console.error('Error getting all orders:', error);
    throw error;
  }
}

// Get an order by ID (alias for getOrderById)
export async function getOrderAdmin(orderId: string): Promise<Order | null> {
  return getOrderById(orderId);
}

// Get an order by ID
export async function getOrderById(orderId: string): Promise<Order | null> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);
    const doc = await orderRef.get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as Order;
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    throw error;
  }
}

// Update order status
export async function updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);

    // Check if order exists
    const orderDoc = await orderRef.get();
    if (!orderDoc.exists) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Update the status
    await orderRef.update({
      status,
      statusHistory: FieldValue.arrayUnion({
        status,
        timestamp: FieldValue.serverTimestamp(),
      }),
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating order status ${orderId}:`, error);
    throw error;
  }
}

// Update order
export async function updateOrder(orderId: string, orderData: Partial<Order>): Promise<void> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);

    // Check if order exists
    const orderDoc = await orderRef.get();
    if (!orderDoc.exists) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // If status is being updated, add to status history
    if (orderData.status) {
      const currentOrder = orderDoc.data() as Order;
      if (currentOrder.status !== orderData.status) {
        // Add to status history using FieldValue for consistent server timestamps
        await orderRef.update({
          statusHistory: FieldValue.arrayUnion({
            status: orderData.status,
            timestamp: FieldValue.serverTimestamp(),
          })
        });
      }
    }

    // Update the order
    await orderRef.update({
      ...orderData,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating order ${orderId}:`, error);
    throw error;
  }
}

// Get orders by status
export async function getOrdersByStatus(status: OrderStatus): Promise<Order[]> {
  try {
    const db = getAdminFirestore();
    const ordersRef = db.collection(ORDERS_COLLECTION);
    const query = ordersRef as unknown as Query<DocumentData>;
    const snapshot = await query
      .where('status', '==', status)
      .orderBy('createdAt', 'desc')
      .get();

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Order));
  } catch (error) {
    console.error(`Error getting orders with status ${status}:`, error);
    throw error;
  }
}

// Get orders for admin dashboard (legacy method)
export async function getAdminOrders() {
  try {
    const result = await getOrders({ limit: 50 });

    return result.orders.map(order => {
      return {
        id: order.id,
        customer: {
          name: order.shippingAddress?.firstName + ' ' + order.shippingAddress?.lastName || 'Unknown',
          email: order.shippingAddress?.email || '<EMAIL>'
        },
        date: order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt),
        total: order.total || 0,
        status: order.status,
        paymentStatus: order.paymentStatus || 'pending',
        items: order.items?.length || 0
      };
    });
  } catch (error) {
    console.error('Error getting admin orders:', error);
    throw error;
  }
}

// Order notes functionality

// Add note to order
export async function addOrderNote(orderId: string, userId: string, content: string, isInternal: boolean = true): Promise<string> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);

    // Check if order exists
    const orderDoc = await orderRef.get();
    if (!orderDoc.exists) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Get user info from users collection
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();
    let userName = 'Unknown User';

    if (userDoc.exists) {
      const userData = userDoc.data();
      if (userData) {
        userName = userData.displayName ?? userData.email ?? 'Unknown User';
      }
    }

    // Create notes collection if it doesn't exist
    const notesRef = orderRef.collection('notes');

    // Create note
    const noteData = {
      orderId,
      userId,
      userName,
      content,
      isInternal,
      createdAt: FieldValue.serverTimestamp(),
    };

    const docRef = await notesRef.add(noteData);

    // Update order with note count
    await orderRef.update({
      noteCount: FieldValue.increment(1),
      updatedAt: FieldValue.serverTimestamp(),
    });

    return docRef.id;
  } catch (error) {
    console.error(`Error adding note to order ${orderId}:`, error);
    throw error;
  }
}

// Get notes for order
export async function getOrderNotes(orderId: string): Promise<OrderNote[]> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);

    // Check if order exists
    const orderDoc = await orderRef.get();
    if (!orderDoc.exists) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    const notesRef = orderRef.collection('notes');
    const snapshot = await notesRef.orderBy('createdAt', 'desc').get();

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as OrderNote));
  } catch (error) {
    console.error(`Error getting notes for order ${orderId}:`, error);
    throw error;
  }
}

// Delete note
export async function deleteOrderNote(orderId: string, noteId: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    const orderRef = db.collection(ORDERS_COLLECTION).doc(orderId);

    // Check if order exists
    const orderDoc = await orderRef.get();
    if (!orderDoc.exists) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    const noteRef = orderRef.collection('notes').doc(noteId);

    // Check if note exists
    const noteDoc = await noteRef.get();
    if (!noteDoc.exists) {
      throw new Error(`Note with ID ${noteId} not found`);
    }

    // Delete the note
    await noteRef.delete();

    // Update order with note count
    await orderRef.update({
      noteCount: FieldValue.increment(-1),
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error deleting note ${noteId} from order ${orderId}:`, error);
    throw error;
  }
}

// Create a pending order for payment processing (admin version)
export async function createPendingOrderAdmin(
  userId: string,
  email: string,
  items: OrderItem[],
  shippingAddress: Address,
  billingAddress: Address,
  shippingMethodDetails: {
    id: string;
    name: string;
    price: number;
  },
  orderSubtotal: number,
  shippingCostTotal: number,
  grandTotal: number,
  currency: string
): Promise<string> {
  try {
    const db = getAdminFirestore();

    const orderData = {
      userId,
      email,
      items,
      status: 'pending_payment' as OrderStatus,
      shippingAddress,
      billingAddress,
      shippingMethodDetails,
      orderSubtotal,
      shippingCost: shippingCostTotal,
      total: grandTotal,
      currency: currency.toLowerCase(),
      paymentStatus: 'pending' as PaymentStatus,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp()
    };

    const docRef = await db.collection(ORDERS_COLLECTION).add(orderData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating pending order (admin):', error);
    throw error;
  }
}

// Get order by Stripe payment intent ID (admin version)
export async function getOrderByPaymentIntentIdAdmin(paymentIntentId: string): Promise<Order | null> {
  try {
    const db = getAdminFirestore();
    const ordersRef = db.collection(ORDERS_COLLECTION);
    const query = (ordersRef as unknown as Query<DocumentData>).where('stripePaymentIntentId', '==', paymentIntentId);
    const querySnapshot = await query.get();

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();

    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
      updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
    } as Order;
  } catch (error) {
    console.error(`Error getting order by payment intent ID ${paymentIntentId} (admin):`, error);
    throw error;
  }
}
