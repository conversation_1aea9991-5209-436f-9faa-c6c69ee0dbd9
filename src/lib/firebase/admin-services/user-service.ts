import { getAdminAuth, getAdminFirestore } from '../admin';
import { UserRecord } from 'firebase-admin/auth';
import { FieldValue } from 'firebase-admin/firestore';
import { Permission } from './role-service';

const USERS_COLLECTION = 'users';

// Interface for user data with custom fields
interface UserData {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: 'admin' | 'customer' | 'employee' | 'editor' | 'viewer';
  permissions?: Permission[];
  isDisabled: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
  metadata?: Record<string, any>;
}

// Get all users
export async function getAllUsers(): Promise<UserData[]> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // List all users from Firebase Auth
    const listUsersResult = await auth.listUsers();
    const users = listUsersResult.users;

    // Get additional user data from Firestore
    const usersData: UserData[] = await Promise.all(
      users.map(async (user) => {
        const userDoc = await db.collection(USERS_COLLECTION).doc(user.uid).get();
        const userData = userDoc.exists ? userDoc.data() : {};

        return {
          uid: user.uid,
          email: user.email || '',
          displayName: user.displayName || '',
          photoURL: user.photoURL || '',
          role: userData?.role || 'customer',
          isDisabled: user.disabled,
          createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
          lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : undefined,
          metadata: userData?.metadata || {},
        };
      })
    );

    return usersData;
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
}

// Get a user by ID
export async function getUserById(uid: string): Promise<UserData | null> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Get user from Firebase Auth
    const user = await auth.getUser(uid);

    // Get additional user data from Firestore
    const userDoc = await db.collection(USERS_COLLECTION).doc(uid).get();
    const userData = userDoc.exists ? userDoc.data() : {};

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || '',
      role: userData?.role || 'customer',
      isDisabled: user.disabled,
      createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
      lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : undefined,
      metadata: userData?.metadata || {},
    };
  } catch (error) {
    console.error(`Error getting user ${uid}:`, error);
    return null;
  }
}

// Update a user's role
export async function updateUserRole(uid: string, role: 'admin' | 'customer' | 'employee' | 'editor' | 'viewer'): Promise<void> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Get current custom claims
    const user = await auth.getUser(uid);
    const currentClaims = user.customClaims || {};

    // Update custom claims in Firebase Auth
    await auth.setCustomUserClaims(uid, {
      ...currentClaims,
      role
    });

    // Update role in Firestore
    await db.collection(USERS_COLLECTION).doc(uid).set({
      role,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });
  } catch (error) {
    console.error(`Error updating user role ${uid}:`, error);
    throw error;
  }
}

// Disable a user
export async function disableUser(uid: string): Promise<void> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Disable user in Firebase Auth
    await auth.updateUser(uid, { disabled: true });

    // Update status in Firestore
    await db.collection(USERS_COLLECTION).doc(uid).set({
      isDisabled: true,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });
  } catch (error) {
    console.error(`Error disabling user ${uid}:`, error);
    throw error;
  }
}

// Enable a user
export async function enableUser(uid: string): Promise<void> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Enable user in Firebase Auth
    await auth.updateUser(uid, { disabled: false });

    // Update status in Firestore
    await db.collection(USERS_COLLECTION).doc(uid).set({
      isDisabled: false,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });
  } catch (error) {
    console.error(`Error enabling user ${uid}:`, error);
    throw error;
  }
}

// Update user metadata
export async function updateUserMetadata(uid: string, metadata: Record<string, any>): Promise<void> {
  try {
    const db = getAdminFirestore();

    // Update metadata in Firestore
    await db.collection(USERS_COLLECTION).doc(uid).set({
      metadata,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });
  } catch (error) {
    console.error(`Error updating user metadata ${uid}:`, error);
    throw error;
  }
}

// Get user permissions
export async function getUserPermissions(uid: string): Promise<Permission[]> {
  try {
    const auth = getAdminAuth();

    // Get user from Firebase Auth
    const user = await auth.getUser(uid);

    // Get custom claims
    const customClaims = user.customClaims || {};

    // Return permissions from custom claims or empty array
    return customClaims.permissions as Permission[] || [];
  } catch (error) {
    console.error(`Error getting user permissions for ${uid}:`, error);
    return [];
  }
}

// Update user permissions
export async function updateUserPermissions(uid: string, permissions: Permission[]): Promise<void> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Get current custom claims
    const user = await auth.getUser(uid);
    const currentClaims = user.customClaims || {};

    // Determine if user should have admin role based on permissions
    const hasAdminPermission = permissions.includes('admin');

    // Update custom claims in Firebase Auth
    await auth.setCustomUserClaims(uid, {
      ...currentClaims,
      permissions,
      // Set role-specific claims for backward compatibility
      admin: hasAdminPermission,
    });

    // Update permissions in Firestore
    await db.collection(USERS_COLLECTION).doc(uid).set({
      permissions,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });

    console.log(`Updated permissions for user ${uid}:`, permissions);
  } catch (error) {
    console.error(`Error updating user permissions for ${uid}:`, error);
    throw error;
  }
}

// Create a new user (for admin purposes)
export async function createUser(email: string, password: string, displayName?: string, role: 'admin' | 'customer' | 'employee' | 'editor' | 'viewer' = 'customer'): Promise<string> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Create user in Firebase Auth
    const userRecord = await auth.createUser({
      email,
      password,
      displayName,
      disabled: false,
    });

    // Get default permissions for the role from role-service
    const permissions: Permission[] = [];

    // Set custom claims
    await auth.setCustomUserClaims(userRecord.uid, {
      role,
      permissions,
      admin: role === 'admin'
    });

    // Create user document in Firestore
    await db.collection(USERS_COLLECTION).doc(userRecord.uid).set({
      email,
      displayName: displayName || '',
      role,
      permissions,
      isDisabled: false,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });

    return userRecord.uid;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

// Get users by role
export async function getUsersByRole(role: string): Promise<UserData[]> {
  try {
    const db = getAdminFirestore();

    // Query users with the specified role
    const usersSnapshot = await db.collection(USERS_COLLECTION)
      .where('role', '==', role)
      .get();

    // Map the documents to UserData objects
    const users: UserData[] = [];
    for (const doc of usersSnapshot.docs) {
      const data = doc.data();
      users.push({
        uid: doc.id,
        email: data.email || '',
        displayName: data.displayName || '',
        photoURL: data.photoURL || '',
        role: data.role || 'customer',
        permissions: data.permissions || [],
        isDisabled: data.isDisabled || false,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        lastLoginAt: data.lastLoginAt ? new Date(data.lastLoginAt.toDate()) : undefined,
        metadata: data.metadata || {},
      });
    }

    return users;
  } catch (error) {
    console.error(`Error getting users by role ${role}:`, error);
    throw error;
  }
}

// Search users by name or email
export async function searchUsers(query: string): Promise<UserData[]> {
  try {
    // Get all users first (in a real app, you'd use a more efficient approach)
    const allUsers = await getAllUsers();

    // Filter users by the search query
    const lowercaseQuery = query.toLowerCase();
    const filteredUsers = allUsers.filter(user =>
      user.email.toLowerCase().includes(lowercaseQuery) ||
      (user.displayName && user.displayName.toLowerCase().includes(lowercaseQuery))
    );

    return filteredUsers;
  } catch (error) {
    console.error(`Error searching users with query ${query}:`, error);
    throw error;
  }
}

// Get user activities
export async function getUserActivities(userId: string): Promise<any[]> {
  try {
    const db = getAdminFirestore();

    // Query the activities collection for the user
    const activitiesSnapshot = await db.collection('userActivities')
      .where('userId', '==', userId)
      .orderBy('timestamp', 'desc')
      .limit(50)
      .get();

    // Map the documents to activity objects
    return activitiesSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        action: data.action,
        details: data.details,
        timestamp: data.timestamp ? new Date(data.timestamp.toDate()) : new Date(),
      };
    });
  } catch (error) {
    console.error(`Error getting activities for user ${userId}:`, error);
    return [];
  }
}
