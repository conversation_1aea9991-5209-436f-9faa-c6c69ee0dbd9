import { getAdminFirestore } from '../admin';
import { Product } from '@/types';
import { FieldValue, WhereFilterOp, OrderByDirection, DocumentData, QueryDocumentSnapshot } from 'firebase-admin/firestore';

const PRODUCTS_COLLECTION = 'products';

// Product filter options interface
export interface ProductFilterOptions {
  category?: string;
  status?: 'active' | 'draft' | 'archived';
  minPrice?: number;
  maxPrice?: number;
  minStock?: number;
  maxStock?: number;
  search?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  sortBy?: string;
  sortOrder?: OrderByDirection;
  limit?: number;
  startAfter?: DocumentData | QueryDocumentSnapshot<DocumentData>;
}

// Get products with pagination and filtering
export async function getProducts(options: ProductFilterOptions = {}) {
  try {
    const db = getAdminFirestore();
    const productsRef = db.collection(PRODUCTS_COLLECTION);

    // Start building the query
    let query: any = productsRef; // Use 'any' for flexibility during chaining

    // Apply filters
    if (options.status) {
      query = query.where('status', '==', options.status);
    }

    if (options.category) {
      query = query.where('category', '==', options.category);
    }

    if (options.minPrice !== undefined) {
      query = query.where('price', '>=', options.minPrice);
    }

    if (options.maxPrice !== undefined) {
      query = query.where('price', '<=', options.maxPrice);
    }

    if (options.minStock !== undefined) {
      query = query.where('stock', '>=', options.minStock);
    }

    if (options.maxStock !== undefined) {
      query = query.where('stock', '<=', options.maxStock);
    }

    if (options.createdAfter) {
      query = query.where('createdAt', '>=', options.createdAfter);
    }

    if (options.createdBefore) {
      query = query.where('createdAt', '<=', options.createdBefore);
    }

    // Apply sorting
    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder || 'desc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply cursor-based pagination
    if (options.startAfter) {
      query = query.startAfter(options.startAfter);
    }

    // Apply limit
    const limit = options.limit || 10;
    query = query.limit(limit + 1); // Get one extra to check if there are more results

    // Execute query
    const snapshot = await query.get();

    // Process results
    const products = snapshot.docs.slice(0, limit).map((doc: QueryDocumentSnapshot<DocumentData>) => ({
      id: doc.id,
      ...doc.data(),
    } as Product));

    // Check if there are more results
    const hasMore = snapshot.docs.length > limit;

    // Get the last document for next pagination
    const lastDoc = products.length > 0 ? snapshot.docs[products.length - 1] : null;

    // If search is provided, filter results client-side
    // Note: For production, consider using a search service like Algolia
    let filteredProducts = products;
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredProducts = products.filter((product: Product) =>
        product.name.toLowerCase().includes(searchLower) ||
        (product.description && product.description.toLowerCase().includes(searchLower))
      );
    }

    // Get total count (for initial load only)
    let total = 0;
    if (!options.startAfter) {
      // Create a simplified query for counting
      let countQuery: any = productsRef; // Use 'any' for flexibility during chaining
      if (options.status) {
        countQuery = countQuery.where('status', '==', options.status);
      }
      if (options.category) {
        countQuery = countQuery.where('category', '==', options.category);
      }

      const countSnapshot = await countQuery.count().get();
      total = countSnapshot.data().count;
    }

    return {
      products: filteredProducts,
      cursor: lastDoc,
      hasMore,
      total
    };
  } catch (error) {
    console.error('Error getting products:', error);
    throw error;
  }
}

// Get all products (legacy method)
export async function getAllProducts(): Promise<Product[]> {
  try {
    const result = await getProducts({ limit: 100 });
    return result.products;
  } catch (error) {
    console.error('Error getting all products:', error);
    throw error;
  }
}

// Get a product by ID
export async function getProductById(productId: string): Promise<Product | null> {
  try {
    const db = getAdminFirestore();
    const productRef = db.collection(PRODUCTS_COLLECTION).doc(productId);
    const doc = await productRef.get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as Product;
  } catch (error) {
    console.error(`Error getting product ${productId}:`, error);
    throw error;
  }
}

// Create a new product
export async function createProduct(productData: Omit<Product, 'id'>): Promise<string> {
  try {
    const db = getAdminFirestore();
    const productsRef = db.collection(PRODUCTS_COLLECTION);

    // Add timestamps
    const data = {
      ...productData,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    };

    const docRef = await productsRef.add(data);
    return docRef.id;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}

// Update a product
export async function updateProduct(productId: string, productData: Partial<Product>): Promise<void> {
  try {
    const db = getAdminFirestore();
    const productRef = db.collection(PRODUCTS_COLLECTION).doc(productId);

    // Add updated timestamp
    const data = {
      ...productData,
      updatedAt: FieldValue.serverTimestamp(),
    };

    await productRef.update(data);
  } catch (error) {
    console.error(`Error updating product ${productId}:`, error);
    throw error;
  }
}

// Delete a product
export async function deleteProduct(productId: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    const productRef = db.collection(PRODUCTS_COLLECTION).doc(productId);

    await productRef.delete();
  } catch (error) {
    console.error(`Error deleting product ${productId}:`, error);
    throw error;
  }
}

// Verify server items for order processing
export async function verifyServerItems(items: any[]): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const verifiedItems = [];

    for (const item of items) {
      // Get the product from the database
      const productRef = db.collection(PRODUCTS_COLLECTION).doc(item.productId);
      const productDoc = await productRef.get();

      if (!productDoc.exists) {
        throw new Error(`Product ${item.productId} not found`);
      }

      const product = productDoc.data() as Product;

      // Verify the price matches
      if (product.price !== item.price) {
        console.warn(`Price mismatch for product ${item.productId}: expected ${product.price}, got ${item.price}`);
        // Use the server price for security
        item.price = product.price;
      }

      // Verify stock availability
      if (product.stock < item.quantity) {
        throw new Error(`Insufficient stock for product ${product.name}. Available: ${product.stock}, requested: ${item.quantity}`);
      }

      verifiedItems.push({
        ...item,
        price: product.price, // Always use server price
        name: product.name,   // Ensure name is from server
      });
    }

    return verifiedItems;
  } catch (error) {
    console.error('Error verifying server items:', error);
    throw error;
  }
}
