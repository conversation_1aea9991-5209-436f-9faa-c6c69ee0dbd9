import { getAdminFirestore } from '../admin';
import { FieldValue } from 'firebase-admin/firestore';

const SETTINGS_COLLECTION = 'settings';

// General settings document ID
const GENERAL_SETTINGS_DOC = 'general';
const PAYMENT_SETTINGS_DOC = 'payment';
const EMAIL_SETTINGS_DOC = 'email';

// Types for settings
export interface GeneralSettings {
  storeName: string;
  storeEmail: string;
  storePhone: string;
  storeAddress: string;
  storeCity: string;
  storeState: string;
  storeZip: string;
  storeCountry: string;
  updatedAt?: Date | FieldValue;
}

export interface BankAccountDetails {
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber?: string;
  swiftCode?: string;
  iban?: string;
  bsb?: string;
  sortCode?: string;
  branchCode?: string;
  currency: string;
  instructions?: string;
}

export interface PaymentSettings {
  currencyCode: string;
  currencySymbol: string;
  stripeEnabled: boolean;
  stripePublicKey?: string;
  stripeSecretKey?: string;
  paypalEnabled: boolean;
  paypalClientId?: string;
  paypalSecretKey?: string;
  codEnabled: boolean;
  manualPaymentEnabled: boolean;
  bankAccountDetails?: BankAccountDetails;
  paymentInstructions?: string;
  updatedAt?: Date | FieldValue;
}

export interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPassword: string;
  smtpFromEmail: string;
  smtpFromName: string;
  sendOrderConfirmation: boolean;
  sendShippingNotification: boolean;
  sendDeliveryNotification: boolean;
  updatedAt?: Date | FieldValue;
}

// Get general settings (admin version)
export const getGeneralSettingsAdmin = async (): Promise<GeneralSettings | null> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(GENERAL_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    if (docSnap.exists) {
      return docSnap.data() as GeneralSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting general settings (admin):', error);
    throw error;
  }
};

// Update general settings (admin version)
export const updateGeneralSettingsAdmin = async (settings: GeneralSettings): Promise<void> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(GENERAL_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    const updatedSettings = {
      ...settings,
      updatedAt: FieldValue.serverTimestamp()
    };
    
    if (docSnap.exists) {
      await docRef.update(updatedSettings);
    } else {
      await docRef.set(updatedSettings);
    }
  } catch (error) {
    console.error('Error updating general settings (admin):', error);
    throw error;
  }
};

// Get payment settings (admin version)
export const getPaymentSettingsAdmin = async (): Promise<PaymentSettings | null> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(PAYMENT_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    if (docSnap.exists) {
      return docSnap.data() as PaymentSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting payment settings (admin):', error);
    throw error;
  }
};

// Update payment settings (admin version)
export const updatePaymentSettingsAdmin = async (settings: PaymentSettings): Promise<void> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(PAYMENT_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    const updatedSettings = {
      ...settings,
      updatedAt: FieldValue.serverTimestamp()
    };
    
    if (docSnap.exists) {
      await docRef.update(updatedSettings);
    } else {
      await docRef.set(updatedSettings);
    }
  } catch (error) {
    console.error('Error updating payment settings (admin):', error);
    throw error;
  }
};

// Get email settings (admin version)
export const getEmailSettingsAdmin = async (): Promise<EmailSettings | null> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(EMAIL_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    if (docSnap.exists) {
      return docSnap.data() as EmailSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting email settings (admin):', error);
    throw error;
  }
};

// Update email settings (admin version)
export const updateEmailSettingsAdmin = async (settings: EmailSettings): Promise<void> => {
  try {
    const db = getAdminFirestore();
    const docRef = db.collection(SETTINGS_COLLECTION).doc(EMAIL_SETTINGS_DOC);
    const docSnap = await docRef.get();
    
    const updatedSettings = {
      ...settings,
      updatedAt: FieldValue.serverTimestamp()
    };
    
    if (docSnap.exists) {
      await docRef.update(updatedSettings);
    } else {
      await docRef.set(updatedSettings);
    }
  } catch (error) {
    console.error('Error updating email settings (admin):', error);
    throw error;
  }
};
