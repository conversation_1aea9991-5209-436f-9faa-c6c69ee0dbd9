import { getAdminAuth, getAdminFirestore } from '../admin';
import { FieldValue } from 'firebase-admin/firestore';

// Define role types
export type UserRole = 'admin' | 'editor' | 'viewer' | 'customer';

// Define permission types
export type Permission =
  | 'read:products'
  | 'write:products'
  | 'delete:products'
  | 'read:categories'
  | 'write:categories'
  | 'delete:categories'
  | 'read:orders'
  | 'write:orders'
  | 'delete:orders'
  | 'read:users'
  | 'write:users'
  | 'delete:users'
  | 'read:settings'
  | 'write:settings'
  | 'read:dashboard'
  | 'read:reports'
  | 'read:api-docs'
  | 'developer'
  | 'admin';

// Define permissions for each role
export const rolePermissions: Record<UserRole, Permission[]> = {
  admin: [
    'read:products', 'write:products', 'delete:products',
    'read:categories', 'write:categories', 'delete:categories',
    'read:orders', 'write:orders', 'delete:orders',
    'read:users', 'write:users', 'delete:users',
    'read:settings', 'write:settings',
    'read:dashboard', 'read:reports',
    'read:api-docs', 'developer', 'admin'
  ],
  editor: [
    'read:products', 'write:products',
    'read:categories', 'write:categories',
    'read:orders', 'write:orders',
    'read:users',
    'read:settings',
    'read:dashboard', 'read:reports',
    'read:api-docs'
  ],
  viewer: [
    'read:products',
    'read:categories',
    'read:orders',
    'read:dashboard', 'read:reports'
  ],
  customer: []
};

// Check if user has permission
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return rolePermissions[userRole]?.includes(permission) || false;
}

// Get all permissions for a role
export function getPermissionsForRole(role: UserRole): Permission[] {
  return rolePermissions[role] || [];
}

// Set user role with custom claims
export async function setUserRole(uid: string, role: UserRole): Promise<void> {
  try {
    const auth = getAdminAuth();
    const db = getAdminFirestore();

    // Get permissions for the role
    const permissions = getPermissionsForRole(role);

    // Set custom claims in Firebase Auth
    await auth.setCustomUserClaims(uid, {
      role,
      permissions,
      // Set role-specific claims for backward compatibility
      admin: role === 'admin',
      editor: role === 'editor' || role === 'admin',
      viewer: role === 'viewer' || role === 'editor' || role === 'admin'
    });

    // Update role in Firestore
    await db.collection('users').doc(uid).set({
      role,
      permissions,
      updatedAt: FieldValue.serverTimestamp(),
    }, { merge: true });

    console.log(`User ${uid} role set to ${role} with permissions:`, permissions);
  } catch (error) {
    console.error(`Error setting user role for ${uid}:`, error);
    throw error;
  }
}

// Get user role and permissions
export async function getUserRoleAndPermissions(uid: string): Promise<{ role: UserRole; permissions: Permission[] } | null> {
  try {
    const auth = getAdminAuth();

    // Get user from Firebase Auth
    const user = await auth.getUser(uid);

    // Get custom claims
    const customClaims = user.customClaims || {};
    const role = customClaims.role as UserRole || 'customer';
    const permissions = customClaims.permissions as Permission[] || [];

    return { role, permissions };
  } catch (error) {
    console.error(`Error getting user role for ${uid}:`, error);
    return null;
  }
}

// Check if user has specific permission
export async function checkUserPermission(uid: string, permission: Permission): Promise<boolean> {
  try {
    const roleData = await getUserRoleAndPermissions(uid);

    if (!roleData) {
      return false;
    }

    return roleData.permissions.includes(permission);
  } catch (error) {
    console.error(`Error checking permission for user ${uid}:`, error);
    return false;
  }
}
