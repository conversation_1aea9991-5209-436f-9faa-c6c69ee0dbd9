import { getAdminFirestore } from '../admin';
import { FieldValue, WhereFilterOp, OrderByDirection, DocumentData, QueryDocumentSnapshot, Query } from 'firebase-admin/firestore';
import { Category } from '@/lib/firebase/services/category-service';

// Extended Category interface with hierarchical structure
export interface CategoryWithChildren extends Category {
  children?: CategoryWithChildren[];
  parentId?: string;
}

const CATEGORIES_COLLECTION = 'categories';

// Category filter options interface
export interface CategoryFilterOptions {
  isActive?: boolean;
  parentId?: string | null; // null means root categories
  search?: string;
  sortBy?: string;
  sortOrder?: OrderByDirection;
  limit?: number;
  startAfter?: DocumentData | QueryDocumentSnapshot<DocumentData>;
}

// Get categories with filtering and pagination
export async function getCategories(options: CategoryFilterOptions = {}) {
  try {
    const db = getAdminFirestore();
    const categoriesRef = db.collection(CATEGORIES_COLLECTION);

    // Start building the query
    let query: Query = categoriesRef;

    // Apply filters
    if (options.isActive !== undefined) {
      query = query.where('isActive', '==', options.isActive);
    }

    if (options.parentId === null) {
      // Get root categories (no parentId or parentId is empty string)
      query = query.where('parentId', 'in', [null, '']);
    } else if (options.parentId !== undefined) {
      // Get categories with specific parentId
      query = query.where('parentId', '==', options.parentId);
    }

    // Apply sorting
    const sortBy = options.sortBy || 'order';
    const sortOrder = options.sortOrder || 'asc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply cursor-based pagination
    if (options.startAfter) {
      query = query.startAfter(options.startAfter);
    }

    // Apply limit
    const limit = options.limit || 100; // Default to a higher limit for categories
    query = query.limit(limit + 1); // Get one extra to check if there are more results

    // Execute query
    const snapshot = await query.get();

    // Process results
    const categories = snapshot.docs.slice(0, limit).map((doc: QueryDocumentSnapshot<DocumentData>) => ({
      id: doc.id,
      ...doc.data(),
    } as Category));

    // Check if there are more results
    const hasMore = snapshot.docs.length > limit;

    // Get the last document for next pagination
    const lastDoc = categories.length > 0 ? snapshot.docs[categories.length - 1] : null;

    // If search is provided, filter results client-side
    let filteredCategories = categories;
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredCategories = categories.filter((category: Category) =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      );
    }

    // Get total count (for initial load only)
    let total = 0;
    if (!options.startAfter) {
      // Create a simplified query for counting
      let countQuery: Query = categoriesRef as Query;
      if (options.isActive !== undefined) {
        countQuery = countQuery.where('isActive', '==', options.isActive);
      }

      const countSnapshot = await countQuery.count().get();
      total = countSnapshot.data().count;
    }

    return {
      categories: filteredCategories,
      cursor: lastDoc,
      hasMore,
      total
    };
  } catch (error) {
    console.error('Error getting categories:', error);
    throw error;
  }
}

// Get all categories (legacy method)
export async function getAllCategories(): Promise<Category[]> {
  try {
    const result = await getCategories({ isActive: true });
    return result.categories;
  } catch (error) {
    console.error('Error getting all categories:', error);
    throw error;
  }
}

// Get categories with hierarchical structure
export async function getCategoriesWithHierarchy(): Promise<CategoryWithChildren[]> {
  try {
    // Get all categories
    const allCategories = await getAllCategories();

    // Create a map of categories by ID
    const categoriesMap = new Map<string, CategoryWithChildren>();
    allCategories.forEach(category => {
      categoriesMap.set(category.id, {
        ...category,
        children: [],
      });
    });

    // Build the hierarchy
    const rootCategories: CategoryWithChildren[] = [];
    allCategories.forEach(category => {
      if (category.parentId && categoriesMap.has(category.parentId)) {
        // Add as child to parent
        categoriesMap.get(category.parentId)!.children!.push(categoriesMap.get(category.id)!);
      } else {
        // Add to root categories
        rootCategories.push(categoriesMap.get(category.id)!);
      }
    });

    return rootCategories;
  } catch (error) {
    console.error('Error getting categories with hierarchy:', error);
    throw error;
  }
}

// Get a category by ID
export async function getCategoryById(categoryId: string): Promise<Category | null> {
  try {
    const db = getAdminFirestore();
    const categoryRef = db.collection(CATEGORIES_COLLECTION).doc(categoryId);
    const doc = await categoryRef.get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as Category;
  } catch (error) {
    console.error(`Error getting category ${categoryId}:`, error);
    throw error;
  }
}

// Check if slug is unique
export async function isSlugUnique(slug: string, excludeCategoryId?: string): Promise<boolean> {
  try {
    const db = getAdminFirestore();
    const categoriesRef = db.collection(CATEGORIES_COLLECTION);

    // Query for categories with the same slug
    const query = categoriesRef.where('slug', '==', slug);
    const snapshot = await query.get();

    // If no categories found, slug is unique
    if (snapshot.empty) {
      return true;
    }

    // If excluding a category (for updates), check if the only match is the excluded category
    if (excludeCategoryId) {
      return snapshot.size === 1 && snapshot.docs[0].id === excludeCategoryId;
    }

    // Slug is not unique
    return false;
  } catch (error) {
    console.error(`Error checking slug uniqueness for ${slug}:`, error);
    throw error;
  }
}

// Generate a unique slug
export async function generateUniqueSlug(name: string, excludeCategoryId?: string): Promise<string> {
  try {
    // Generate base slug
    let baseSlug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Remove leading and trailing hyphens
    baseSlug = baseSlug.replace(/^-+|-+$/g, '');

    // Check if base slug is unique
    let slug = baseSlug;
    let isUnique = await isSlugUnique(slug, excludeCategoryId);
    let counter = 1;

    // If not unique, append a number until it is
    while (!isUnique) {
      slug = `${baseSlug}-${counter}`;
      isUnique = await isSlugUnique(slug, excludeCategoryId);
      counter++;
    }

    return slug;
  } catch (error) {
    console.error(`Error generating unique slug for ${name}:`, error);
    throw error;
  }
}

// Create a new category
export async function createCategory(categoryData: Omit<Category, 'id'>): Promise<string> {
  try {
    const db = getAdminFirestore();
    const categoriesRef = db.collection(CATEGORIES_COLLECTION);

    // Generate a unique slug if not provided
    if (!categoryData.slug) {
      categoryData.slug = await generateUniqueSlug(categoryData.name);
    } else {
      // Verify slug uniqueness
      const isUnique = await isSlugUnique(categoryData.slug);
      if (!isUnique) {
        throw new Error(`Slug '${categoryData.slug}' is already in use`);
      }
    }

    // Set default values
    const data = {
      ...categoryData,
      isActive: categoryData.isActive !== undefined ? categoryData.isActive : true,
      order: categoryData.order || 0,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    };

    const docRef = await categoriesRef.add(data);
    return docRef.id;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
}

// Update a category
export async function updateCategory(categoryId: string, categoryData: Partial<Category>): Promise<void> {
  try {
    const db = getAdminFirestore();
    const categoryRef = db.collection(CATEGORIES_COLLECTION).doc(categoryId);

    // Check if category exists
    const categoryDoc = await categoryRef.get();
    if (!categoryDoc.exists) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    // If slug is being updated, verify uniqueness
    if (categoryData.slug) {
      const isUnique = await isSlugUnique(categoryData.slug, categoryId);
      if (!isUnique) {
        throw new Error(`Slug '${categoryData.slug}' is already in use`);
      }
    } else if (categoryData.name && !categoryData.slug) {
      // If name is updated but slug is not provided, generate a new slug
      categoryData.slug = await generateUniqueSlug(categoryData.name, categoryId);
    }

    // Add updated timestamp
    const data = {
      ...categoryData,
      updatedAt: FieldValue.serverTimestamp(),
    };

    await categoryRef.update(data);
  } catch (error) {
    console.error(`Error updating category ${categoryId}:`, error);
    throw error;
  }
}

// Delete a category
export async function deleteCategory(categoryId: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    const categoryRef = db.collection(CATEGORIES_COLLECTION).doc(categoryId);

    // Check if category exists
    const categoryDoc = await categoryRef.get();
    if (!categoryDoc.exists) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    // Check if category has children
    const childrenQuery = db.collection(CATEGORIES_COLLECTION).where('parentId', '==', categoryId);
    const childrenSnapshot = await childrenQuery.get();

    if (!childrenSnapshot.empty) {
      throw new Error(`Cannot delete category with ID ${categoryId} because it has child categories`);
    }

    // Check if category is used by products
    const productsQuery = db.collection('products').where('category', '==', categoryId);
    const productsSnapshot = await productsQuery.get();

    if (!productsSnapshot.empty) {
      throw new Error(`Cannot delete category with ID ${categoryId} because it is used by products`);
    }

    // Delete the category
    await categoryRef.delete();
  } catch (error) {
    console.error(`Error deleting category ${categoryId}:`, error);
    throw error;
  }
}

// Reorder categories
export async function reorderCategories(categoryIds: string[]): Promise<void> {
  try {
    const db = getAdminFirestore();
    const batch = db.batch();

    // Update order for each category
    categoryIds.forEach((categoryId, index) => {
      const categoryRef = db.collection(CATEGORIES_COLLECTION).doc(categoryId);
      batch.update(categoryRef, {
        order: index,
        updatedAt: FieldValue.serverTimestamp()
      });
    });

    // Commit the batch
    await batch.commit();
  } catch (error) {
    console.error('Error reordering categories:', error);
    throw error;
  }
}
