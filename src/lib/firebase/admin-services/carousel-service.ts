import { getAdminFirestore } from '../admin';
import { FieldValue } from 'firebase-admin/firestore';

// Define carousel slide type
export interface CarouselSlide {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  cta: string;
  link: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Define carousel slide input type (for creating/updating)
export interface CarouselSlideInput {
  image: string;
  title: string;
  subtitle: string;
  cta: string;
  link: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Get all carousel slides
 * @param activeOnly Whether to return only active slides
 * @returns Array of carousel slides
 */
export async function getCarouselSlides(activeOnly = false): Promise<CarouselSlide[]> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('carouselSlides').orderBy('order', 'asc');
    
    if (activeOnly) {
      query = query.where('isActive', '==', true);
    }
    
    const snapshot = await query.get();
    
    if (snapshot.empty) {
      return [];
    }
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        image: data.image,
        title: data.title,
        subtitle: data.subtitle,
        cta: data.cta,
        link: data.link,
        order: data.order,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as CarouselSlide;
    });
  } catch (error) {
    console.error('Error getting carousel slides:', error);
    throw error;
  }
}

/**
 * Get a carousel slide by ID
 * @param id Slide ID
 * @returns Carousel slide or null if not found
 */
export async function getCarouselSlideById(id: string): Promise<CarouselSlide | null> {
  try {
    const db = getAdminFirestore();
    const doc = await db.collection('carouselSlides').doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    const data = doc.data();
    return {
      id: doc.id,
      image: data?.image,
      title: data?.title,
      subtitle: data?.subtitle,
      cta: data?.cta,
      link: data?.link,
      order: data?.order,
      isActive: data?.isActive,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate(),
    } as CarouselSlide;
  } catch (error) {
    console.error(`Error getting carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new carousel slide
 * @param slideData Slide data
 * @returns Created slide ID
 */
export async function createCarouselSlide(slideData: CarouselSlideInput): Promise<string> {
  try {
    const db = getAdminFirestore();
    
    // Get the highest order value
    let maxOrder = 0;
    const orderSnapshot = await db.collection('carouselSlides')
      .orderBy('order', 'desc')
      .limit(1)
      .get();
    
    if (!orderSnapshot.empty) {
      maxOrder = orderSnapshot.docs[0].data().order || 0;
    }
    
    // Create the new slide
    const slideRef = await db.collection('carouselSlides').add({
      ...slideData,
      order: slideData.order !== undefined ? slideData.order : maxOrder + 1,
      isActive: slideData.isActive !== undefined ? slideData.isActive : true,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });
    
    return slideRef.id;
  } catch (error) {
    console.error('Error creating carousel slide:', error);
    throw error;
  }
}

/**
 * Update a carousel slide
 * @param id Slide ID
 * @param slideData Updated slide data
 */
export async function updateCarouselSlide(id: string, slideData: Partial<CarouselSlideInput>): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    await db.collection('carouselSlides').doc(id).update({
      ...slideData,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a carousel slide
 * @param id Slide ID
 */
export async function deleteCarouselSlide(id: string): Promise<void> {
  try {
    const db = getAdminFirestore();
    await db.collection('carouselSlides').doc(id).delete();
  } catch (error) {
    console.error(`Error deleting carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Reorder carousel slides
 * @param slideIds Array of slide IDs in the desired order
 */
export async function reorderCarouselSlides(slideIds: string[]): Promise<void> {
  try {
    const db = getAdminFirestore();
    const batch = db.batch();
    
    slideIds.forEach((id, index) => {
      const slideRef = db.collection('carouselSlides').doc(id);
      batch.update(slideRef, { 
        order: index + 1,
        updatedAt: FieldValue.serverTimestamp()
      });
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error reordering carousel slides:', error);
    throw error;
  }
}

/**
 * Toggle carousel slide active status
 * @param id Slide ID
 * @param isActive Whether the slide should be active
 */
export async function toggleCarouselSlideActive(id: string, isActive: boolean): Promise<void> {
  try {
    const db = getAdminFirestore();
    
    await db.collection('carouselSlides').doc(id).update({
      isActive,
      updatedAt: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error toggling carousel slide ${id} active status:`, error);
    throw error;
  }
}
