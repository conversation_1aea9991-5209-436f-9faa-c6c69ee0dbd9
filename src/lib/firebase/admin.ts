import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { getAuth } from 'firebase-admin/auth';

// Initialize Firebase Admin
export function initAdmin() {
  const apps = getApps();

  if (!apps.length) {
    // For local development, we can use a service account key
    // In production, we should use environment variables
    try {
      // Check if we have the required environment variables
      if (process.env.FIREBASE_PROJECT_ID &&
          process.env.FIREBASE_CLIENT_EMAIL &&
          process.env.FIREBASE_PRIVATE_KEY) {

        initializeApp({
          credential: cert({
            projectId: process.env.FIREBASE_PROJECT_ID,
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            // The private key needs to be properly formatted as it comes from env vars
            privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
          }),
        });
      } else {
        // For development, we can initialize without credentials
        // This will use the default credentials from the Firebase CLI
        initializeApp();
      }
      console.log('Firebase Admin initialized successfully');
    } catch (error) {
      console.error('Error initializing Firebase Admin:', error);
    }
  }
}

// Get Firestore instance
export function getAdminFirestore() {
  initAdmin();
  return getFirestore();
}

// Get Storage instance
export function getAdminStorage() {
  initAdmin();
  return getStorage();
}

// Get Auth instance
export function getAdminAuth() {
  initAdmin();
  return getAuth();
}

// We'll avoid direct exports to prevent initialization at build time
// Instead, use the getter functions: getAdminAuth(), getAdminFirestore(), getAdminStorage()
