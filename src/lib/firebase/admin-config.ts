import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
// Check if the app has already been initialized to prevent multiple initializations
function getFirebaseAdmin() {
  if (!admin.apps.length) {
    // Use service account credentials or application default credentials
    const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY
      ? JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)
      : undefined;

    admin.initializeApp({
      credential: serviceAccount
        ? admin.credential.cert(serviceAccount)
        : admin.credential.applicationDefault(),
      databaseURL: process.env.FIREBASE_DATABASE_URL,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
    });
  }

  return admin;
}

// Get Firebase Admin instance
const firebaseAdmin = getFirebaseAdmin();

// Export Firebase Admin services
export const auth = firebaseAdmin.auth();
export const firestore = firebaseAdmin.firestore();
export const storage = firebaseAdmin.storage();
export const messaging = firebaseAdmin.messaging();

// Export the admin SDK
export default firebaseAdmin;
