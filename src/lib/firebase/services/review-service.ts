import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  limit,
  addDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Review } from '@/types';

const REVIEWS_COLLECTION = 'reviews';

// Convert Firestore document to Review type
const convertReview = (doc: any): Review => {
  const data = doc.data();
  return {
    id: doc.id,
    productId: data.productId,
    userId: data.userId,
    userName: data.userName,
    rating: data.rating,
    title: data.title,
    comment: data.comment,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt
  };
};

// Get reviews for a product
export const getProductReviews = async (productId: string): Promise<Review[]> => {
  try {
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const q = query(
      reviewsRef,
      where('productId', '==', productId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(convertReview);
  } catch (error) {
    console.error(`Error getting reviews for product ${productId}:`, error);
    throw error;
  }
};

// Get reviews by a user
export const getUserReviews = async (userId: string): Promise<Review[]> => {
  try {
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const q = query(
      reviewsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(convertReview);
  } catch (error) {
    console.error(`Error getting reviews for user ${userId}:`, error);
    throw error;
  }
};

// Create a new review
export const createReview = async (
  productId: string,
  userId: string,
  userName: string,
  rating: number,
  title: string,
  comment: string
): Promise<string> => {
  try {
    const reviewData = {
      productId,
      userId,
      userName,
      rating,
      title,
      comment,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, REVIEWS_COLLECTION), reviewData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating review:', error);
    throw error;
  }
};

// Update a review
export const updateReview = async (
  reviewId: string,
  rating: number,
  title: string,
  comment: string
): Promise<void> => {
  try {
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    await updateDoc(reviewRef, {
      rating,
      title,
      comment,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error(`Error updating review ${reviewId}:`, error);
    throw error;
  }
};

// Delete a review
export const deleteReview = async (reviewId: string): Promise<void> => {
  try {
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    await deleteDoc(reviewRef);
  } catch (error) {
    console.error(`Error deleting review ${reviewId}:`, error);
    throw error;
  }
};

// Get a review by ID
export const getReviewById = async (reviewId: string): Promise<Review | null> => {
  try {
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    const reviewSnap = await getDoc(reviewRef);
    
    if (reviewSnap.exists()) {
      return convertReview(reviewSnap);
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting review ${reviewId}:`, error);
    throw error;
  }
};

// Check if user has already reviewed a product
export const hasUserReviewedProduct = async (userId: string, productId: string): Promise<boolean> => {
  try {
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const q = query(
      reviewsRef,
      where('userId', '==', userId),
      where('productId', '==', productId),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    
    return !querySnapshot.empty;
  } catch (error) {
    console.error(`Error checking if user ${userId} has reviewed product ${productId}:`, error);
    throw error;
  }
};
