import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User as FirebaseUser,
  onAuthStateChanged,
  GoogleAuthProvider,
  FacebookAuthProvider,
  signInWithPopup,
  sendEmailVerification,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updateEmail
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  serverTimestamp,
  getDocs,
  collection
} from 'firebase/firestore';
import { auth, db } from '../config';

export interface User {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  phoneNumber?: string;
  isAdmin?: boolean;
  emailVerified?: boolean;
  providerData?: { providerId: string }[];
  createdAt?: Date;
  role?: string;
}

const USERS_COLLECTION = 'users';

// Register a new user
export const registerUser = async (
  email: string,
  password: string,
  displayName: string,
  sendVerification = true
): Promise<User> => {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Update profile with display name
    await updateProfile(user, { displayName });

    // Send email verification if requested
    if (sendVerification) {
      await sendEmailVerification(user);
    }

    // Create user document in Firestore
    const userData = {
      email: user.email,
      displayName,
      photoURL: user.photoURL,
      phoneNumber: user.phoneNumber,
      isAdmin: false,
      emailVerified: user.emailVerified,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    await setDoc(doc(db, USERS_COLLECTION, user.uid), userData);

    return {
      uid: user.uid,
      email: user.email || '',
      displayName,
      photoURL: user.photoURL || undefined,
      phoneNumber: user.phoneNumber || undefined,
      isAdmin: false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

// Sign in a user
export const signInUser = async (
  email: string,
  password: string
): Promise<User> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, user.uid));
    const userData = userDoc.data();

    // Update emailVerified status in Firestore if it has changed
    if (userData && userData.emailVerified !== user.emailVerified) {
      await updateDoc(doc(db, USERS_COLLECTION, user.uid), {
        emailVerified: user.emailVerified,
        updatedAt: serverTimestamp()
      });
    }

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || undefined,
      phoneNumber: user.phoneNumber || undefined,
      isAdmin: userData?.isAdmin || false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error signing in user:', error);
    throw error;
  }
};

// Sign out the current user
export const signOutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out user:', error);
    throw error;
  }
};

// Send password reset email
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

// Get current user data
export const getCurrentUser = async (): Promise<User | null> => {
  const user = auth.currentUser;

  if (!user) {
    return null;
  }

  try {
    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, user.uid));
    const userData = userDoc.data();

    // Update emailVerified status in Firestore if it has changed
    if (userData && userData.emailVerified !== user.emailVerified) {
      await updateDoc(doc(db, USERS_COLLECTION, user.uid), {
        emailVerified: user.emailVerified,
        updatedAt: serverTimestamp()
      });
    }

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || undefined,
      phoneNumber: user.phoneNumber || undefined,
      isAdmin: userData?.isAdmin || false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error getting current user data:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (
  uid: string,
  data: {
    displayName?: string;
    photoURL?: string;
    phoneNumber?: string;
    email?: string;
    currentPassword?: string;
  }
): Promise<User> => {
  try {
    const user = auth.currentUser;

    if (!user) {
      throw new Error('No user is currently signed in');
    }

    // Update auth profile if displayName or photoURL is provided
    if (data.displayName || data.photoURL) {
      await updateProfile(user, {
        displayName: data.displayName || user.displayName,
        photoURL: data.photoURL || user.photoURL
      });
    }

    // Update email if provided
    if (data.email && data.email !== user.email && data.currentPassword) {
      // Re-authenticate user before changing email
      const credential = EmailAuthProvider.credential(
        user.email || '',
        data.currentPassword
      );
      await reauthenticateWithCredential(user, credential);
      await updateEmail(user, data.email);
      await sendEmailVerification(user);
    }

    // Update user document in Firestore
    const updateData: any = {
      ...data,
      emailVerified: user.emailVerified,
      updatedAt: serverTimestamp()
    };

    // Remove sensitive data before storing in Firestore
    delete updateData.currentPassword;
    delete updateData.email; // Email is stored in auth, not Firestore

    const userRef = doc(db, USERS_COLLECTION, uid);
    await updateDoc(userRef, updateData);

    // Get updated user data
    const userDoc = await getDoc(userRef);
    const userData = userDoc.data();

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || undefined,
      phoneNumber: data.phoneNumber || user.phoneNumber || undefined,
      isAdmin: userData?.isAdmin || false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Sign in with Google
export const signInWithGoogle = async (): Promise<User> => {
  try {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);
    const user = userCredential.user;

    // Check if user exists in Firestore
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, user.uid));

    if (!userDoc.exists()) {
      // Create user document in Firestore if it doesn't exist
      const userData = {
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        phoneNumber: user.phoneNumber,
        isAdmin: false,
        emailVerified: user.emailVerified,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await setDoc(doc(db, USERS_COLLECTION, user.uid), userData);
    } else {
      // Update user data if it exists
      await updateDoc(doc(db, USERS_COLLECTION, user.uid), {
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        updatedAt: serverTimestamp()
      });
    }

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || undefined,
      phoneNumber: user.phoneNumber || undefined,
      isAdmin: userDoc.exists() ? userDoc.data()?.isAdmin || false : false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

// Sign in with Facebook
export const signInWithFacebook = async (): Promise<User> => {
  try {
    const provider = new FacebookAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);
    const user = userCredential.user;

    // Check if user exists in Firestore
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, user.uid));

    if (!userDoc.exists()) {
      // Create user document in Firestore if it doesn't exist
      const userData = {
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        phoneNumber: user.phoneNumber,
        isAdmin: false,
        emailVerified: user.emailVerified,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await setDoc(doc(db, USERS_COLLECTION, user.uid), userData);
    } else {
      // Update user data if it exists
      await updateDoc(doc(db, USERS_COLLECTION, user.uid), {
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        updatedAt: serverTimestamp()
      });
    }

    return {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      photoURL: user.photoURL || undefined,
      phoneNumber: user.phoneNumber || undefined,
      isAdmin: userDoc.exists() ? userDoc.data()?.isAdmin || false : false,
      emailVerified: user.emailVerified,
      providerData: user.providerData
    };
  } catch (error) {
    console.error('Error signing in with Facebook:', error);
    throw error;
  }
};

// Send email verification
export const sendVerificationEmail = async (): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (user) {
      await sendEmailVerification(user);
    } else {
      throw new Error('No user is currently signed in');
    }
  } catch (error) {
    console.error('Error sending verification email:', error);
    throw error;
  }
};

// Check if email is verified
export const isEmailVerified = (): boolean => {
  const user = auth.currentUser;
  return user ? user.emailVerified : false;
};

// Listen to auth state changes
export const onAuthStateChange = (callback: (user: User | null) => void): (() => void) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        // Get user data from Firestore
        const userDoc = await getDoc(doc(db, USERS_COLLECTION, firebaseUser.uid));
        const userData = userDoc.data();

        // Get ID token result to check custom claims
        const idTokenResult = await firebaseUser.getIdTokenResult();
        const isAdmin = !!idTokenResult.claims.admin;

        // Update emailVerified status in Firestore if it has changed
        if (userData && userData.emailVerified !== firebaseUser.emailVerified) {
          await updateDoc(doc(db, USERS_COLLECTION, firebaseUser.uid), {
            emailVerified: firebaseUser.emailVerified,
            updatedAt: serverTimestamp()
          });
        }

        const user: User = {
          uid: firebaseUser.uid,
          email: firebaseUser.email || '',
          displayName: firebaseUser.displayName || '',
          photoURL: firebaseUser.photoURL || undefined,
          phoneNumber: firebaseUser.phoneNumber || undefined,
          isAdmin: isAdmin, // Set from token claims
          emailVerified: firebaseUser.emailVerified,
          providerData: firebaseUser.providerData
        };

        callback(user);
      } catch (error) {
        console.error('Error getting user data in auth state change:', error);
        callback(null);
      }
    } else {
      callback(null);
    }
  });
};

// Check if user is admin
export const isAdmin = async (uid: string): Promise<boolean> => {
  try {
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, uid));
    return userDoc.exists() && userDoc.data()?.isAdmin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

// Set user as admin
export const setUserAsAdmin = async (uid: string, isAdmin: boolean): Promise<void> => {
  try {
    // Call the API endpoint to update both Firestore and Firebase Auth
    const response = await fetch('/api/users/role', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uid,
        role: isAdmin ? 'admin' : 'customer',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to update user role: ${errorData.error || response.statusText}`);
    }

    // Also update local Firestore document for immediate UI update
    const userRef = doc(db, USERS_COLLECTION, uid);
    await updateDoc(userRef, {
      isAdmin,
      role: isAdmin ? 'admin' : 'customer',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error setting admin status:', error);
    throw error;
  }
};

// Get all users
export const getAllUsers = async (): Promise<User[]> => {
  try {
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);
    
    const users: User[] = [];
    
    usersSnapshot.forEach((doc) => {
      const userData = doc.data();
      
      // Convert Firestore timestamp to Date
      const createdAt = userData.createdAt ? 
        (userData.createdAt.toDate ? userData.createdAt.toDate() : new Date(userData.createdAt)) : 
        new Date();
      
      users.push({
        uid: doc.id,
        email: userData.email || '',
        displayName: userData.displayName || '',
        photoURL: userData.photoURL,
        phoneNumber: userData.phoneNumber,
        isAdmin: userData.isAdmin || false,
        emailVerified: userData.emailVerified || false,
        createdAt: createdAt,
        role: userData.role || (userData.isAdmin ? 'admin' : 'customer'),
        providerData: []
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};
