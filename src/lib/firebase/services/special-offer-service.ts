import { db } from '../config';
import {
  collection,
  query,
  where,
  getDocs,
  getDoc,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';

// Define special offer type (same as admin service)
export interface SpecialOffer {
  id: string;
  title: string;
  description: string;
  image: string;
  linkUrl: string;
  linkText: string;
  isActive: boolean;
  startDate: Date;
  endDate: Date | null; // null means no end date
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get active special offer for the home page
 * @returns Active special offer or null if none exists
 */
export async function getActiveSpecialOffer(): Promise<SpecialOffer | null> {
  try {
    const now = new Date();

    // Query for active offers that have started
    const offersQuery = query(
      collection(db, 'specialOffers'),
      where('isActive', '==', true),
      where('startDate', '<=', now)
    );

    const snapshot = await getDocs(offersQuery);

    if (snapshot.empty) {
      // Return default offer if none exist in the database
      return getDefaultSpecialOffer();
    }

    // Filter offers that haven't ended yet
    const validOffers = snapshot.docs
      .map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          description: data.description,
          image: data.image,
          linkUrl: data.linkUrl,
          linkText: data.linkText,
          isActive: data.isActive,
          startDate: data.startDate?.toDate(),
          endDate: data.endDate?.toDate() || null,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate(),
        } as SpecialOffer;
      })
      .filter(offer => !offer.endDate || offer.endDate > now);

    // Return the most recently updated offer if multiple are active
    if (validOffers.length > 0) {
      return validOffers.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())[0];
    }

    // Return default offer if no valid offers exist
    return getDefaultSpecialOffer();
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error('Error getting active special offer:', error);
    // Return default offer on error
    return getDefaultSpecialOffer();
  }
}

/**
 * Get default special offer (fallback if none exist in the database)
 * @returns Default special offer
 */
function getDefaultSpecialOffer(): SpecialOffer {
  const now = new Date();
  return {
    id: 'default-offer',
    title: 'Special Offer',
    description: 'Get 15% off on all curtains this month!',
    image: '/images/promo-banner.jpg',
    linkUrl: '/categories/curtains',
    linkText: 'Shop Now',
    isActive: true,
    startDate: now,
    endDate: null,
    createdAt: now,
    updatedAt: now,
  };
}

// Define special offer input type (for creating/updating)
export interface SpecialOfferInput {
  title: string;
  description: string;
  image: string;
  linkUrl: string;
  linkText: string;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date | null;
}

/**
 * Get all special offers for admin dashboard
 * @param activeOnly Whether to return only active offers
 * @returns Array of special offers
 */
export async function getAllSpecialOffers(activeOnly = false): Promise<SpecialOffer[]> {
  try {
    const specialOffersQuery = collection(db, 'specialOffers');
    let querySnapshot;

    if (activeOnly) {
      const now = new Date();
      querySnapshot = await getDocs(
        query(
          specialOffersQuery,
          where('isActive', '==', true),
          where('startDate', '<=', now)
        )
      );
    } else {
      querySnapshot = await getDocs(specialOffersQuery);
    }

    if (querySnapshot.empty) {
      return [];
    }

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        image: data.image,
        linkUrl: data.linkUrl,
        linkText: data.linkText,
        isActive: data.isActive,
        startDate: data.startDate?.toDate(),
        endDate: data.endDate?.toDate() ?? null,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as SpecialOffer;
    });
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error('Error getting special offers:', error);
    throw error;
  }
}

/**
 * Get a special offer by ID
 * @param id Offer ID
 * @returns Special offer or null if not found
 */
export async function getSpecialOfferById(id: string): Promise<SpecialOffer | null> {
  try {
    const docRef = doc(db, 'specialOffers', id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      title: data.title,
      description: data.description,
      image: data.image,
      linkUrl: data.linkUrl,
      linkText: data.linkText,
      isActive: data.isActive,
      startDate: data.startDate?.toDate(),
      endDate: data.endDate?.toDate() ?? null,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
    } as SpecialOffer;
  } catch (error) {
    console.error(`Error getting special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new special offer
 * @param offerData Special offer data
 * @returns ID of the created special offer
 */
export async function createSpecialOffer(offerData: SpecialOfferInput): Promise<string> {
  try {
    // Set default values if not provided
    const now = new Date();
    const startDate = offerData.startDate || now;

    // Create the new offer
    const offerRef = await addDoc(collection(db, 'specialOffers'), {
      ...offerData,
      isActive: offerData.isActive !== undefined ? offerData.isActive : true,
      startDate: startDate,
      endDate: offerData.endDate ?? null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return offerRef.id;
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error('Error creating special offer:', error);
    throw error;
  }
}

/**
 * Update a special offer
 * @param id Offer ID
 * @param offerData Updated special offer data
 */
export async function updateSpecialOffer(id: string, offerData: Partial<SpecialOfferInput>): Promise<void> {
  try {
    const offerRef = doc(db, 'specialOffers', id);

    // Update the offer
    await updateDoc(offerRef, {
      ...offerData,
      updatedAt: serverTimestamp(),
    });
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error(`Error updating special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a special offer
 * @param id Offer ID
 */
export async function deleteSpecialOffer(id: string): Promise<void> {
  try {
    const offerRef = doc(db, 'specialOffers', id);

    // Delete the offer
    await deleteDoc(offerRef);
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error(`Error deleting special offer ${id}:`, error);
    throw error;
  }
}

/**
 * Toggle special offer active status
 * @param id Offer ID
 * @param isActive Whether the offer should be active
 */
export async function toggleSpecialOfferActive(id: string, isActive: boolean): Promise<void> {
  try {
    const offerRef = doc(db, 'specialOffers', id);

    await updateDoc(offerRef, {
      isActive,
      updatedAt: serverTimestamp(),
    });
  } catch (error: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
    console.error(`Error toggling special offer ${id} active status:`, error);
    throw error;
  }
}
