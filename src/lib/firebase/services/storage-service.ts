import { getStorage, ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { app } from '../config';

// Initialize Firebase Storage
const storage = getStorage(app);

// Export storage for use in other files
export { storage };


/**
 * Upload image to Firebase Storage
 * @param file The file to upload
 * @param path The storage path (e.g., 'products', 'categories')
 * @returns Promise with the download URL
 */
export async function uploadImage(file: File, path: string): Promise<string> {
  try {
    // Create a unique filename
    const filename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;

    // Create storage reference
    const storageRef = ref(storage, `${path}/${filename}`);

    // Upload file
    const uploadTask = uploadBytesResumable(storageRef, file);

    // Wait for upload to complete
    return new Promise((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Track upload progress if needed
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          console.log(`Upload progress: ${progress}%`);
        },
        (error) => {
          // Handle upload error
          console.error('Error uploading image:', error);
          reject(error);
        },
        async () => {
          // Upload completed successfully, get download URL
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          resolve(downloadURL);
        }
      );
    });
  } catch (error) {
    console.error('Error in uploadImage:', error);
    throw error;
  }
}

/**
 * Delete image from Firebase Storage
 * @param url The download URL of the image to delete
 */
export async function deleteImage(url: string): Promise<void> {
  try {
    // Extract the path from the URL
    // Firebase Storage URLs are in the format: https://firebasestorage.googleapis.com/v0/b/[bucket]/o/[path]?[token]
    const urlPath = url.split('?')[0];
    const path = urlPath.split('/o/')[1];

    if (!path) {
      throw new Error('Invalid image URL');
    }

    // Decode the path (it's URL encoded)
    const decodedPath = decodeURIComponent(path);

    // Create storage reference
    const storageRef = ref(storage, decodedPath);

    // Delete the file
    await deleteObject(storageRef);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
}

/**
 * Upload multiple images to Firebase Storage
 * @param files Array of files to upload
 * @param path The storage path
 * @returns Promise with array of download URLs
 */
export async function uploadMultipleImages(files: File[], path: string): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadImage(file, path));
    return Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
}

/**
 * Get file extension from file name
 * @param filename The file name
 * @returns The file extension
 */
export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

/**
 * Check if file is an image
 * @param file The file to check
 * @returns Boolean indicating if the file is an image
 */
export function isImageFile(file: File): boolean {
  const acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  return file && acceptedImageTypes.includes(file.type);
}

/**
 * Validate image file (type and size)
 * @param file The file to validate
 * @param maxSizeMB Maximum file size in MB
 * @returns Object with validation result and error message
 */
export function validateImageFile(file: File, maxSizeMB: number = 5): { valid: boolean; error?: string } {
  // Check if file is an image
  if (!isImageFile(file)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
    };
  }

  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `File size exceeds the limit of ${maxSizeMB}MB.`
    };
  }

  return { valid: true };
}
