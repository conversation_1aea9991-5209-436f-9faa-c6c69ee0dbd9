import {
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  getCountFromServer
} from 'firebase/firestore';
import { db } from '../config';
import { Order, OrderStatus } from '@/types';

const PRODUCTS_COLLECTION = 'products';
const ORDERS_COLLECTION = 'orders';
const USERS_COLLECTION = 'users';

// Get dashboard stats
export const getDashboardStats = async () => {
  try {
    // Get product count
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const productsSnapshot = await getCountFromServer(productsRef);
    const totalProducts = productsSnapshot.data().count;

    // Get order count
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const ordersSnapshot = await getCountFromServer(ordersRef);
    const totalOrders = ordersSnapshot.data().count;

    // Get user count
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getCountFromServer(usersRef);
    const totalUsers = usersSnapshot.data().count;

    // Get total revenue
    const ordersQuery = query(ordersRef);
    const ordersQuerySnapshot = await getDocs(ordersQuery);
    let totalRevenue = 0;

    ordersQuerySnapshot.forEach((doc) => {
      const orderData = doc.data();
      totalRevenue += orderData.total || 0;
    });

    // Get recent orders
    const recentOrdersQuery = query(
      ordersRef,
      orderBy('createdAt', 'desc'),
      limit(5)
    );
    const recentOrdersSnapshot = await getDocs(recentOrdersQuery);

    const recentOrders = recentOrdersSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        customer: data.shippingAddress?.firstName + ' ' + data.shippingAddress?.lastName || 'Unknown',
        date: data.createdAt ? (data.createdAt as Timestamp).toDate().toISOString().split('T')[0] : '',
        total: data.total || 0,
        status: data.status as OrderStatus
      };
    });

    // Get product performance
    const productsQuery = query(
      productsRef,
      orderBy('createdAt', 'desc'),
      limit(4)
    );
    const productsQuerySnapshot = await getDocs(productsQuery);

    const productPerformance = productsQuerySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        name: data.name,
        sales: data.salesCount || 0,
        stock: data.stock || 0,
        trend: (data.salesTrend || 'up') as 'up' | 'down'
      };
    });

    return {
      totalProducts,
      totalOrders,
      totalUsers,
      totalRevenue,
      recentOrders,
      productPerformance
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    throw error;
  }
};

// Get all products for admin
export const getAdminProducts = async () => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const productsQuery = query(productsRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(productsQuery);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        category: data.category,
        price: data.price,
        stock: data.stock || 0,
        image: data.images && data.images.length > 0 ? data.images[0] : '/images/placeholder.png',
        status: data.status || 'active',
        // Generate a slug from the name if one doesn't exist
        slug: data.slug || data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
      };
    });
  } catch (error) {
    console.error('Error getting admin products:', error);
    throw error;
  }
};

// Define AdminOrder type for the admin dashboard
export interface AdminOrder {
  id: string;
  customer: {
    name: string;
    email: string;
  };
  date: Date;
  total: number;
  status: OrderStatus;
  paymentStatus: string;
  items: number;
}

// Get all orders for admin
export const getAdminOrders = async (): Promise<AdminOrder[]> => {
  try {
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const ordersQuery = query(ordersRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(ordersQuery);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        customer: {
          name: data.shippingAddress?.firstName + ' ' + data.shippingAddress?.lastName || 'Unknown',
          email: data.shippingAddress?.email || '<EMAIL>'
        },
        date: data.createdAt ? (data.createdAt as Timestamp).toDate() : new Date(),
        total: data.total || 0,
        status: data.status as OrderStatus,
        paymentStatus: data.paymentStatus || 'pending',
        items: data.items?.length || 0
      };
    });
  } catch (error) {
    console.error('Error getting admin orders:', error);
    throw error;
  }
};

// Get all categories for admin
export const getAdminCategories = async () => {
  try {
    const categoriesRef = collection(db, 'categories');

    const categoriesQuery = query(categoriesRef);

    const querySnapshot = await getDocs(categoriesQuery);
   
    // Log the first document if available
    if (querySnapshot.docs.length > 0) {
      console.log('getAdminCategories: First category data:', querySnapshot.docs[0].data());
    }

    const categories = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        image: data.image || '/images/placeholder.png',
        slug: data.slug,
        isActive: data.isActive !== false, // Default to true if not specified
        productCount: 0 // We'll update this below
      };
    });
    // Get product counts for each category
    for (const category of categories) {
      const productsRef = collection(db, PRODUCTS_COLLECTION);
      const productsQuery = query(
        productsRef,
        where('category', '==', category.id)
      );
      const productsSnapshot = await getCountFromServer(productsQuery);
      category.productCount = productsSnapshot.data().count;
    }
    return categories;
  } catch (error) {
    console.error('Error getting admin categories:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};
