import {
  collection,
  doc,
  getDocs,
  query,
  where,
  addDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Product } from '@/types';
import { getProductById } from './product-service';

const WISHLIST_COLLECTION = 'wishlists';

// Add a product to wishlist
export const addToWishlist = async (userId: string, productId: string): Promise<string> => {
  try {
    // Check if product already exists in wishlist
    const exists = await isProductInWishlist(userId, productId);
    if (exists) {
      throw new Error('Product already in wishlist');
    }
    
    const wishlistData = {
      userId,
      productId,
      createdAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, WISHLIST_COLLECTION), wishlistData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    throw error;
  }
};

// Remove a product from wishlist
export const removeFromWishlist = async (userId: string, productId: string): Promise<void> => {
  try {
    const wishlistRef = collection(db, WISHLIST_COLLECTION);
    const q = query(
      wishlistRef,
      where('userId', '==', userId),
      where('productId', '==', productId)
    );
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      throw new Error('Product not found in wishlist');
    }
    
    // Delete the wishlist item
    const wishlistItemId = querySnapshot.docs[0].id;
    await deleteDoc(doc(db, WISHLIST_COLLECTION, wishlistItemId));
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    throw error;
  }
};

// Check if a product is in the user's wishlist
export const isProductInWishlist = async (userId: string, productId: string): Promise<boolean> => {
  try {
    const wishlistRef = collection(db, WISHLIST_COLLECTION);
    const q = query(
      wishlistRef,
      where('userId', '==', userId),
      where('productId', '==', productId)
    );
    const querySnapshot = await getDocs(q);
    
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking wishlist:', error);
    throw error;
  }
};

// Get all products in a user's wishlist
export const getWishlist = async (userId: string): Promise<Product[]> => {
  try {
    const wishlistRef = collection(db, WISHLIST_COLLECTION);
    const q = query(
      wishlistRef,
      where('userId', '==', userId)
    );
    const querySnapshot = await getDocs(q);
    
    // Get product details for each wishlist item
    const productPromises = querySnapshot.docs.map(async (doc) => {
      const data = doc.data();
      const product = await getProductById(data.productId);
      return product;
    });
    
    const products = await Promise.all(productPromises);
    
    // Filter out any null products (in case a product was deleted)
    return products.filter(product => product !== null) as Product[];
  } catch (error) {
    console.error('Error getting wishlist:', error);
    throw error;
  }
};

// Clear the entire wishlist
export const clearWishlist = async (userId: string): Promise<void> => {
  try {
    const wishlistRef = collection(db, WISHLIST_COLLECTION);
    const q = query(
      wishlistRef,
      where('userId', '==', userId)
    );
    const querySnapshot = await getDocs(q);
    
    // Delete each wishlist item
    const deletePromises = querySnapshot.docs.map(doc => 
      deleteDoc(doc.ref)
    );
    
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('Error clearing wishlist:', error);
    throw error;
  }
};
