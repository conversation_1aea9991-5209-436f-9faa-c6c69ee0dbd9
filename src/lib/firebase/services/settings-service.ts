import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc,
  serverTimestamp,
  Timestamp,
  FieldValue
} from 'firebase/firestore';
import { db } from '../config';

const SETTINGS_COLLECTION = 'settings';

// General settings document ID
const GENERAL_SETTINGS_DOC = 'general';
const PAYMENT_SETTINGS_DOC = 'payment';
const EMAIL_SETTINGS_DOC = 'email';

// Types for settings
export interface GeneralSettings {
  storeName: string;
  storeEmail: string;
  storePhone: string;
  storeAddress: string;
  storeCity: string;
  storeState: string;
  storeZip: string;
  storeCountry: string;
  updatedAt?: Timestamp | FieldValue;
}

export interface BankAccountDetails {
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber?: string;
  swiftCode?: string;
  iban?: string;
  bsb?: string;
  sortCode?: string;
  branchCode?: string;
  currency: string;
  instructions?: string;
}

export interface PaymentSettings {
  currencyCode: string;
  currencySymbol: string;
  stripeEnabled: boolean;
  stripePublicKey?: string;
  stripeSecretKey?: string;
  paypalEnabled: boolean;
  paypalClientId?: string;
  paypalSecretKey?: string;
  codEnabled: boolean;
  manualPaymentEnabled: boolean;
  bankAccountDetails?: BankAccountDetails;
  paymentInstructions?: string;
  updatedAt?: Timestamp | FieldValue;
}

export interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPassword: string;
  smtpFromEmail: string;
  smtpFromName: string;
  sendOrderConfirmation: boolean;
  sendShippingNotification: boolean;
  sendDeliveryNotification: boolean;
  updatedAt?: Timestamp | FieldValue;
}

// Get general settings
export const getGeneralSettings = async (): Promise<GeneralSettings | null> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, GENERAL_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as GeneralSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting general settings:', error);
    throw error;
  }
};

// Update general settings
export const updateGeneralSettings = async (settings: GeneralSettings): Promise<void> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, GENERAL_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    const updatedSettings = {
      ...settings,
      updatedAt: serverTimestamp()
    };
    
    if (docSnap.exists()) {
      await updateDoc(docRef, updatedSettings);
    } else {
      await setDoc(docRef, updatedSettings);
    }
  } catch (error) {
    console.error('Error updating general settings:', error);
    throw error;
  }
};

// Get payment settings
export const getPaymentSettings = async (): Promise<PaymentSettings | null> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, PAYMENT_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as PaymentSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting payment settings:', error);
    throw error;
  }
};

// Update payment settings
export const updatePaymentSettings = async (settings: PaymentSettings): Promise<void> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, PAYMENT_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    const updatedSettings = {
      ...settings,
      updatedAt: serverTimestamp()
    };
    
    if (docSnap.exists()) {
      await updateDoc(docRef, updatedSettings);
    } else {
      await setDoc(docRef, updatedSettings);
    }
  } catch (error) {
    console.error('Error updating payment settings:', error);
    throw error;
  }
};

// Get email settings
export const getEmailSettings = async (): Promise<EmailSettings | null> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, EMAIL_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as EmailSettings;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting email settings:', error);
    throw error;
  }
};

// Update email settings
export const updateEmailSettings = async (settings: EmailSettings): Promise<void> => {
  try {
    const docRef = doc(db, SETTINGS_COLLECTION, EMAIL_SETTINGS_DOC);
    const docSnap = await getDoc(docRef);
    
    const updatedSettings = {
      ...settings,
      updatedAt: serverTimestamp()
    };
    
    if (docSnap.exists()) {
      await updateDoc(docRef, updatedSettings);
    } else {
      await setDoc(docRef, updatedSettings);
    }
  } catch (error) {
    console.error('Error updating email settings:', error);
    throw error;
  }
};
