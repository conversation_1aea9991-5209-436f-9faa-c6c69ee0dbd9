import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  limit,
  addDoc,
  deleteDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Product } from '@/types';
import { getProductById } from './product-service';

const RECENTLY_VIEWED_COLLECTION = 'recentlyViewed';
const MAX_RECENTLY_VIEWED = 20; // Maximum number of recently viewed items to store

// Add a product to recently viewed
export const addToRecentlyViewed = async (userId: string, productId: string): Promise<void> => {
  try {
    // Check if product already exists in recently viewed
    const recentlyViewedRef = collection(db, RECENTLY_VIEWED_COLLECTION);
    const q = query(
      recentlyViewedRef,
      where('userId', '==', userId),
      where('productId', '==', productId)
    );
    const querySnapshot = await getDocs(q);
    
    // If product already exists, delete it (we'll add it again to update the timestamp)
    if (!querySnapshot.empty) {
      await deleteDoc(querySnapshot.docs[0].ref);
    }
    
    // Add the product to recently viewed
    await addDoc(collection(db, RECENTLY_VIEWED_COLLECTION), {
      userId,
      productId,
      viewedAt: serverTimestamp()
    });
    
    // Ensure we don't exceed the maximum number of recently viewed items
    await pruneRecentlyViewed(userId);
  } catch (error) {
    console.error('Error adding to recently viewed:', error);
    throw error;
  }
};

// Get recently viewed products for a user
export const getRecentlyViewed = async (userId: string, limitCount = 10): Promise<Product[]> => {
  try {
    const recentlyViewedRef = collection(db, RECENTLY_VIEWED_COLLECTION);
    const q = query(
      recentlyViewedRef,
      where('userId', '==', userId),
      orderBy('viewedAt', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    
    // Get product details for each recently viewed item
    const productPromises = querySnapshot.docs.map(async (doc) => {
      const data = doc.data();
      const product = await getProductById(data.productId);
      return product;
    });
    
    const products = await Promise.all(productPromises);
    
    // Filter out any null products (in case a product was deleted)
    return products.filter(product => product !== null) as Product[];
  } catch (error) {
    console.error('Error getting recently viewed products:', error);
    throw error;
  }
};

// Clear recently viewed products for a user
export const clearRecentlyViewed = async (userId: string): Promise<void> => {
  try {
    const recentlyViewedRef = collection(db, RECENTLY_VIEWED_COLLECTION);
    const q = query(
      recentlyViewedRef,
      where('userId', '==', userId)
    );
    const querySnapshot = await getDocs(q);
    
    // Delete each recently viewed item
    const deletePromises = querySnapshot.docs.map(doc => 
      deleteDoc(doc.ref)
    );
    
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('Error clearing recently viewed:', error);
    throw error;
  }
};

// Remove a specific product from recently viewed
export const removeFromRecentlyViewed = async (userId: string, productId: string): Promise<void> => {
  try {
    const recentlyViewedRef = collection(db, RECENTLY_VIEWED_COLLECTION);
    const q = query(
      recentlyViewedRef,
      where('userId', '==', userId),
      where('productId', '==', productId)
    );
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      await deleteDoc(querySnapshot.docs[0].ref);
    }
  } catch (error) {
    console.error('Error removing from recently viewed:', error);
    throw error;
  }
};

// Helper function to ensure we don't exceed the maximum number of recently viewed items
const pruneRecentlyViewed = async (userId: string): Promise<void> => {
  try {
    const recentlyViewedRef = collection(db, RECENTLY_VIEWED_COLLECTION);
    const q = query(
      recentlyViewedRef,
      where('userId', '==', userId),
      orderBy('viewedAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    // If we have more than the maximum, delete the oldest ones
    if (querySnapshot.docs.length > MAX_RECENTLY_VIEWED) {
      const itemsToDelete = querySnapshot.docs.slice(MAX_RECENTLY_VIEWED);
      
      const deletePromises = itemsToDelete.map(doc => 
        deleteDoc(doc.ref)
      );
      
      await Promise.all(deletePromises);
    }
  } catch (error) {
    console.error('Error pruning recently viewed:', error);
    throw error;
  }
};
