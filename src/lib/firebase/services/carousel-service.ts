import { db } from '../config';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  getDoc,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';

// Define carousel slide type (same as admin service)
export interface CarouselSlide {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  cta: string;
  link: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get active carousel slides for the home page
 * @returns Array of active carousel slides
 */
export async function getActiveCarouselSlides(): Promise<CarouselSlide[]> {
  try {
    const slidesQuery = query(
      collection(db, 'carouselSlides'),
      where('isActive', '==', true),
      orderBy('order', 'asc')
    );

    const snapshot = await getDocs(slidesQuery);

    if (snapshot.empty) {
      // Return default slides if none exist in the database
      return getDefaultCarouselSlides();
    }

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        image: data.image,
        title: data.title,
        subtitle: data.subtitle,
        cta: data.cta,
        link: data.link,
        order: data.order,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as CarouselSlide;
    });
  } catch (error) {
    console.error('Error getting carousel slides:', error);
    // Return default slides on error
    return getDefaultCarouselSlides();
  }
}

/**
 * Get default carousel slides (fallback if none exist in the database)
 * @returns Array of default carousel slides
 */
function getDefaultCarouselSlides(): CarouselSlide[] {
  const now = new Date();
  return [
    {
      id: 'default-1',
      image: '/images/hero-1.jpg',
      title: 'Transform Your Space',
      subtitle: 'Elegant interior solutions for modern homes',
      cta: 'Shop Now',
      link: '/products',
      order: 1,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: 'default-2',
      image: '/images/hero-2.jpg',
      title: 'Premium Curtain Collection',
      subtitle: 'Discover our exclusive range of curtains',
      cta: 'Explore Curtains',
      link: '/categories/curtains',
      order: 2,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    },
    {
      id: 'default-3',
      image: '/images/hero-3.jpg',
      title: 'Wall Design Solutions',
      subtitle: 'Elevate your interior with our wall designs',
      cta: 'View Wall Designs',
      link: '/categories/wall-designs',
      order: 3,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    },
  ];
}

// Define carousel slide input type (for creating/updating)
export interface CarouselSlideInput {
  image: string;
  title: string;
  subtitle: string;
  cta: string;
  link: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Get all carousel slides for admin dashboard
 * @param activeOnly Whether to return only active slides
 * @returns Array of carousel slides
 */
export async function getAllCarouselSlides(activeOnly = false): Promise<CarouselSlide[]> {
  try {
    let slidesQuery;

    if (activeOnly) {
      slidesQuery = query(
        collection(db, 'carouselSlides'),
        where('isActive', '==', true),
        orderBy('order', 'asc')
      );
    } else {
      slidesQuery = query(
        collection(db, 'carouselSlides'),
        orderBy('order', 'asc')
      );
    }

    const snapshot = await getDocs(slidesQuery);

    if (snapshot.empty) {
      return [];
    }

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        image: data.image,
        title: data.title,
        subtitle: data.subtitle,
        cta: data.cta,
        link: data.link,
        order: data.order,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as CarouselSlide;
    });
  } catch (error) {
    console.error('Error getting carousel slides:', error);
    throw error;
  }
}

/**
 * Get a carousel slide by ID
 * @param id Slide ID
 * @returns Carousel slide or null if not found
 */
export async function getCarouselSlideById(id: string): Promise<CarouselSlide | null> {
  try {
    const docRef = doc(db, 'carouselSlides', id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      image: data.image,
      title: data.title,
      subtitle: data.subtitle,
      cta: data.cta,
      link: data.link,
      order: data.order,
      isActive: data.isActive,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
    } as CarouselSlide;
  } catch (error) {
    console.error(`Error getting carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new carousel slide
 * @param slideData Carousel slide data
 * @returns ID of the created carousel slide
 */
export async function createCarouselSlide(slideData: CarouselSlideInput): Promise<string> {
  try {
    // Get the highest order value to place the new slide at the end
    let maxOrder = 0;

    try {
      const slidesQuery = query(
        collection(db, 'carouselSlides'),
        orderBy('order', 'desc')
      );

      const snapshot = await getDocs(slidesQuery);

      if (!snapshot.empty) {
        const firstDoc = snapshot.docs[0];
        maxOrder = firstDoc.data().order || 0;
      }
    } catch (error) {
      console.error('Error getting max order:', error);
    }

    // Create the new slide
    const slideRef = await addDoc(collection(db, 'carouselSlides'), {
      ...slideData,
      order: slideData.order ?? maxOrder + 1,
      isActive: slideData.isActive ?? true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return slideRef.id;
  } catch (error) {
    console.error('Error creating carousel slide:', error);
    throw error;
  }
}

/**
 * Update a carousel slide
 * @param id Slide ID
 * @param slideData Updated carousel slide data
 */
export async function updateCarouselSlide(id: string, slideData: Partial<CarouselSlideInput>): Promise<void> {
  try {
    const slideRef = doc(db, 'carouselSlides', id);

    // Update the slide
    await updateDoc(slideRef, {
      ...slideData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a carousel slide
 * @param id Slide ID
 */
export async function deleteCarouselSlide(id: string): Promise<void> {
  try {
    const slideRef = doc(db, 'carouselSlides', id);

    // Delete the slide
    await deleteDoc(slideRef);
  } catch (error) {
    console.error(`Error deleting carousel slide ${id}:`, error);
    throw error;
  }
}

/**
 * Toggle carousel slide active status
 * @param id Slide ID
 * @param isActive Whether the slide should be active
 */
export async function toggleCarouselSlideActive(id: string, isActive: boolean): Promise<void> {
  try {
    const slideRef = doc(db, 'carouselSlides', id);

    await updateDoc(slideRef, {
      isActive,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error toggling carousel slide ${id} active status:`, error);
    throw error;
  }
}

/**
 * Update carousel slide order
 * @param id Slide ID
 * @param newOrder New order value
 */
export async function updateCarouselSlideOrder(id: string, newOrder: number): Promise<void> {
  try {
    const slideRef = doc(db, 'carouselSlides', id);

    await updateDoc(slideRef, {
      order: newOrder,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating carousel slide ${id} order:`, error);
    throw error;
  }
}
