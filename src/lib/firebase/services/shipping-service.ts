import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';

const SHIPPING_METHODS_COLLECTION = 'shippingMethods';

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDeliveryDays: {
    min: number;
    max: number;
  };
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Convert Firestore data to ShippingMethod
const convertShippingMethod = (id: string, data: any): ShippingMethod => {
  return {
    id,
    name: data.name,
    description: data.description,
    price: data.price,
    estimatedDeliveryDays: data.estimatedDeliveryDays || { min: 3, max: 7 },
    isActive: data.isActive !== undefined ? data.isActive : true,
    createdAt: data.createdAt?.toDate(),
    updatedAt: data.updatedAt?.toDate()
  };
};

// Get all shipping methods
export const getAllShippingMethods = async (includeInactive = false): Promise<ShippingMethod[]> => {
  try {
    const shippingRef = collection(db, SHIPPING_METHODS_COLLECTION);
    let q;
    
    if (includeInactive) {
      q = query(shippingRef, orderBy('price', 'asc'));
    } else {
      q = query(shippingRef, where('isActive', '==', true), orderBy('price', 'asc'));
    }
    
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => 
      convertShippingMethod(doc.id, doc.data())
    );
  } catch (error) {
    console.error('Error getting shipping methods:', error);
    throw error;
  }
};

// Get a shipping method by ID
export const getShippingMethodById = async (id: string): Promise<ShippingMethod | null> => {
  try {
    const docRef = doc(db, SHIPPING_METHODS_COLLECTION, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return convertShippingMethod(docSnap.id, docSnap.data());
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting shipping method ${id}:`, error);
    throw error;
  }
};

// Create a shipping method
export const createShippingMethod = async (shippingMethod: Omit<ShippingMethod, 'id'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, SHIPPING_METHODS_COLLECTION), {
      ...shippingMethod,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating shipping method:', error);
    throw error;
  }
};

// Update a shipping method
export const updateShippingMethod = async (id: string, shippingMethod: Partial<ShippingMethod>): Promise<void> => {
  try {
    const docRef = doc(db, SHIPPING_METHODS_COLLECTION, id);
    
    await updateDoc(docRef, {
      ...shippingMethod,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error(`Error updating shipping method ${id}:`, error);
    throw error;
  }
};

// Delete a shipping method
export const deleteShippingMethod = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, SHIPPING_METHODS_COLLECTION, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error(`Error deleting shipping method ${id}:`, error);
    throw error;
  }
};

// Initialize default shipping methods if none exist
export const initializeDefaultShippingMethods = async (): Promise<void> => {
  try {
    const methods = await getAllShippingMethods(true);
    
    if (methods.length === 0) {
      // Create standard shipping
      await createShippingMethod({
        name: 'Standard Shipping',
        description: '5-7 business days',
        price: 5.99,
        estimatedDeliveryDays: { min: 5, max: 7 },
        isActive: true
      });
      
      // Create express shipping
      await createShippingMethod({
        name: 'Express Shipping',
        description: '2-3 business days',
        price: 15.99,
        estimatedDeliveryDays: { min: 2, max: 3 },
        isActive: true
      });
      
      console.log('Default shipping methods initialized');
    }
  } catch (error) {
    console.error('Error initializing default shipping methods:', error);
    throw error;
  }
};
