import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  updateDoc,
  addDoc,
  serverTimestamp,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db, auth } from '../config';
import {
  updateProfile
} from 'firebase/auth';
import { UserRole } from '../admin-services/role-service';

const USERS_COLLECTION = 'users';
const USER_ACTIVITY_COLLECTION = 'activities';

// User interface
export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: UserRole;
  isDisabled: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
  metadata?: Record<string, unknown>;
}

// User activity interface
export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  details?: unknown;
  timestamp: Date;
  ip?: string;
}

// User filter options
export interface UserFilterOptions {
  role?: UserRole;
  isDisabled?: boolean;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  startAfter?: DocumentData | QueryDocumentSnapshot<DocumentData>;
}

// Get current user
export async function getCurrentUser(): Promise<User | null> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) return null;

    const userDoc = await getDoc(doc(db, USERS_COLLECTION, currentUser.uid));
    if (!userDoc.exists()) return null;

    const userData = userDoc.data();

    return {
      uid: currentUser.uid,
      email: currentUser.email || '',
      displayName: currentUser.displayName || '',
      photoURL: currentUser.photoURL || '',
      role: userData.role || 'customer',
      isDisabled: userData.isDisabled || false,
      createdAt: userData.createdAt instanceof Timestamp
        ? userData.createdAt.toDate()
        : new Date(userData.createdAt),
      lastLoginAt: currentUser.metadata.lastSignInTime
        ? new Date(currentUser.metadata.lastSignInTime)
        : undefined,
      metadata: userData.metadata || {},
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Get user by ID
export async function getUserById(uid: string): Promise<User | null> {
  try {
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, uid));
    if (!userDoc.exists()) return null;

    const userData = userDoc.data();

    return {
      uid,
      email: userData.email || '',
      displayName: userData.displayName || '',
      photoURL: userData.photoURL || '',
      role: userData.role || 'customer',
      isDisabled: userData.isDisabled || false,
      createdAt: userData.createdAt instanceof Timestamp
        ? userData.createdAt.toDate()
        : new Date(userData.createdAt),
      lastLoginAt: userData.lastLoginAt instanceof Timestamp
        ? userData.lastLoginAt.toDate()
        : userData.lastLoginAt ? new Date(userData.lastLoginAt) : undefined,
      metadata: userData.metadata || {},
    };
  } catch (error) {
    console.error(`Error getting user ${uid}:`, error);
    return null;
  }
}

// Get users with filtering
export async function getUsers(options: UserFilterOptions = {}) {
  try {
    const usersRef = collection(db, USERS_COLLECTION);

    // Start building the query
    let userQuery = query(usersRef);

    // Apply filters
    if (options.role) {
      userQuery = query(userQuery, where('role', '==', options.role));
    }

    if (options.isDisabled !== undefined) {
      userQuery = query(userQuery, where('isDisabled', '==', options.isDisabled));
    }

    // Apply sorting
    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder || 'desc';
    userQuery = query(userQuery, orderBy(sortBy, sortOrder));

    // Apply cursor-based pagination
    if (options.startAfter) {
      userQuery = query(userQuery, startAfter(options.startAfter));
    }

    // Apply limit
    const limitValue = options.limit || 10;
    userQuery = query(userQuery, limit(limitValue + 1)); // Get one extra to check if there are more results

    // Execute query
    const snapshot = await getDocs(userQuery);

    // Process results
    const users = snapshot.docs.slice(0, limitValue).map(doc => {
      const data = doc.data();
      return {
        uid: doc.id,
        email: data.email || '',
        displayName: data.displayName || '',
        photoURL: data.photoURL || '',
        role: data.role || 'customer',
        isDisabled: data.isDisabled || false,
        createdAt: data.createdAt instanceof Timestamp
          ? data.createdAt.toDate()
          : new Date(data.createdAt),
        lastLoginAt: data.lastLoginAt instanceof Timestamp
          ? data.lastLoginAt.toDate()
          : data.lastLoginAt ? new Date(data.lastLoginAt) : undefined,
        metadata: data.metadata || {},
      } as User;
    });

    // Check if there are more results
    const hasMore = snapshot.docs.length > limitValue;

    // Get the last document for next pagination
    const lastDoc = users.length > 0 ? snapshot.docs[users.length - 1] : null;

    // If search is provided, filter results client-side
    let filteredUsers = users;
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredUsers = users.filter(user =>
        user.email.toLowerCase().includes(searchLower) ||
        (user.displayName && user.displayName.toLowerCase().includes(searchLower))
      );
    }

    // Get total count (for initial load only)
    let total = 0;
    if (!options.startAfter) {
      // Create a simplified query for counting
      let countQuery = query(usersRef); // Start with a query
      if (options.role) {
        countQuery = query(countQuery, where('role', '==', options.role));
      }

      const countSnapshot = await getDocs(countQuery); // Use the query directly
      total = countSnapshot.size;
    }

    return {
      users: filteredUsers,
      cursor: lastDoc,
      hasMore,
      total
    };
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
}

// Update user profile
export async function updateUserProfile(
  uid: string,
  data: { displayName?: string; photoURL?: string }
): Promise<void> {
  try {
    const userRef = doc(db, USERS_COLLECTION, uid);

    // Update in Firestore
    await updateDoc(userRef, {
      ...data,
      updatedAt: serverTimestamp()
    });

    // Update in Firebase Auth if it's the current user
    const currentUser = auth.currentUser;
    if (currentUser && currentUser.uid === uid) {
      await updateProfile(currentUser, data);
    }
  } catch (error) {
    console.error(`Error updating user profile ${uid}:`, error);
    throw error;
  }
}

// Log user activity
export async function logUserActivity(
  userId: string,
  action: string,
  details?: unknown,
  ip?: string
): Promise<string> {
  try {
    const activitiesRef = collection(db, USERS_COLLECTION, userId, USER_ACTIVITY_COLLECTION);

    const activityData = {
      userId,
      action,
      details,
      ip,
      timestamp: serverTimestamp()
    };

    const docRef = await addDoc(activitiesRef, activityData); // Use addDoc
    return docRef.id;
  } catch (error) {
    console.error(`Error logging user activity for ${userId}:`, error);
    throw error;
  }
}

// Get user activities
export async function getUserActivities(
  userId: string,
  limitCount: number = 20 // Renamed parameter
): Promise<UserActivity[]> {
  try {
    const activitiesRef = collection(db, USERS_COLLECTION, userId, USER_ACTIVITY_COLLECTION);
    const activitiesQuery = query(
      activitiesRef,
      orderBy('timestamp', 'desc'),
      limit(limitCount) // Use renamed parameter
    );

    const snapshot = await getDocs(activitiesQuery);

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId,
        action: data.action,
        details: data.details,
        timestamp: data.timestamp instanceof Timestamp
          ? data.timestamp.toDate()
          : new Date(data.timestamp),
        ip: data.ip
      } as UserActivity;
    });
  } catch (error) {
    console.error(`Error getting user activities for ${userId}:`, error);
    throw error;
  }
}
