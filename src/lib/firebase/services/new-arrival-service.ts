import { db } from '../config';
import {
  collection,
  query,
  where,
  getDocs,
  getDoc,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { getProductsByIds } from './product-service';
import { Product } from '@/types';

// Define new arrival type (same as admin service)
export interface NewArrival {
  id: string;
  title: string;
  description: string;
  image: string;
  productIds: string[];
  isActive: boolean;
  startDate: Date;
  endDate: Date | null; // null means no end date
  createdAt: Date;
  updatedAt: Date;
}

// Extended new arrival with products
export interface NewArrivalWithProducts extends NewArrival {
  products: Product[];
}

/**
 * Get active new arrival
 * @returns Active new arrival or null if none exists
 */
export async function getActiveNewArrival(): Promise<NewArrivalWithProducts | null> {
  try {
    const now = new Date();

    // Query for active new arrivals that have started
    const newArrivalsQuery = query(
      collection(db, 'newArrivals'),
      where('isActive', '==', true),
      where('startDate', '<=', now),
      orderBy('startDate', 'desc')
    );

    const snapshot = await getDocs(newArrivalsQuery);

    if (snapshot.empty) {
      // Return default new arrival if none exist in the database
      return getDefaultNewArrival();
    }

    // Filter new arrivals that haven't ended yet
    const validNewArrivals = snapshot.docs
      .map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          description: data.description,
          image: data.image,
          productIds: data.productIds || [],
          isActive: data.isActive,
          startDate: data.startDate?.toDate(),
          endDate: data.endDate?.toDate() || null,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate(),
        } as NewArrival;
      })
      .filter(newArrival => !newArrival.endDate || newArrival.endDate > now);

    // Return the most recently created new arrival if multiple are active
    if (validNewArrivals.length > 0) {
      const mostRecent = validNewArrivals[0];

      // Fetch the products for this new arrival
      const products = await getProductsByIds(mostRecent.productIds);

      return {
        ...mostRecent,
        products
      };
    }

    // Return default new arrival if no valid new arrivals exist
    return getDefaultNewArrival();
  } catch (error) {
    console.error('Error getting active new arrival:', error);
    // Return default new arrival on error
    return getDefaultNewArrival();
  }
}

/**
 * Get default new arrival (fallback if none exist in the database)
 * @returns Default new arrival
 */
async function getDefaultNewArrival(): Promise<NewArrivalWithProducts | null> {
  try {
    // Get some recent products to show as new arrivals
    const productsQuery = query(
      collection(db, 'products'),
      where('status', '==', 'active'),
      orderBy('createdAt', 'desc'),
      // Limit to 8 products
      where('stock', '>', 0)
    );

    const snapshot = await getDocs(productsQuery);

    if (snapshot.empty) {
      return null;
    }

    const products = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        price: data.price,
        compareAtPrice: data.compareAtPrice,
        images: data.images ?? [],
        category: data.category,
        categoryId: data.categoryId,
        status: data.status,
        featured: data.featured ?? false,
        stock: data.stock ?? 0,
        sku: data.sku,
        slug: data.slug ?? doc.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      } as Product;
    }).slice(0, 8);

    const now = new Date();
    return {
      id: 'default-new-arrival',
      title: 'New Arrivals',
      description: 'Check out our latest products that just arrived!',
      image: '/images/new-arrivals-banner.jpg',
      productIds: products.map(p => p.id),
      isActive: true,
      startDate: now,
      endDate: null,
      createdAt: now,
      updatedAt: now,
      products
    };
  } catch (error) {
    console.error('Error getting default new arrival:', error);
    return null;
  }
}

// Define new arrival input type (for creating/updating)
export interface NewArrivalInput {
  title: string;
  description: string;
  image: string;
  productIds: string[];
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date | null;
}

/**
 * Get all new arrivals for admin dashboard
 * @param activeOnly Whether to return only active new arrivals
 * @returns Array of new arrivals
 */
export async function getAllNewArrivals(activeOnly = false): Promise<NewArrival[]> {
  try {
    let newArrivalsQuery = collection(db, 'newArrivals');
    let querySnapshot;

    if (activeOnly) {
      const now = new Date();
      querySnapshot = await getDocs(
        query(
          newArrivalsQuery,
          where('isActive', '==', true),
          where('startDate', '<=', now)
        )
      );
    } else {
      querySnapshot = await getDocs(newArrivalsQuery);
    }

    if (querySnapshot.empty) {
      return [];
    }

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        image: data.image,
        productIds: data.productIds ?? [],
        isActive: data.isActive,
        startDate: data.startDate?.toDate(),
        endDate: data.endDate?.toDate() ?? null,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as NewArrival;
    });
  } catch (error) {
    console.error('Error getting new arrivals:', error);
    throw error;
  }
}

/**
 * Get a new arrival by ID
 * @param id New arrival ID
 * @returns New arrival or null if not found
 */
export async function getNewArrivalById(id: string): Promise<NewArrival | null> {
  try {
    const docRef = doc(db, 'newArrivals', id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      title: data.title,
      description: data.description,
      image: data.image,
      productIds: data.productIds ?? [],
      isActive: data.isActive,
      startDate: data.startDate?.toDate(),
      endDate: data.endDate?.toDate() ?? null,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
    } as NewArrival;
  } catch (error) {
    console.error(`Error getting new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new arrival
 * @param newArrivalData New arrival data
 * @returns ID of the created new arrival
 */
export async function createNewArrival(newArrivalData: NewArrivalInput): Promise<string> {
  try {
    // Set default values if not provided
    const now = new Date();
    const startDate = newArrivalData.startDate || now;

    // Create the new arrival
    const newArrivalRef = await addDoc(collection(db, 'newArrivals'), {
      ...newArrivalData,
      isActive: newArrivalData.isActive ?? true,
      startDate: startDate,
      endDate: newArrivalData.endDate ?? null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return newArrivalRef.id;
  } catch (error) {
    console.error('Error creating new arrival:', error);
    throw error;
  }
}

/**
 * Update a new arrival
 * @param id New arrival ID
 * @param newArrivalData Updated new arrival data
 */
export async function updateNewArrival(id: string, newArrivalData: Partial<NewArrivalInput>): Promise<void> {
  try {
    const newArrivalRef = doc(db, 'newArrivals', id);

    // Update the new arrival
    await updateDoc(newArrivalRef, {
      ...newArrivalData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a new arrival
 * @param id New arrival ID
 */
export async function deleteNewArrival(id: string): Promise<void> {
  try {
    const newArrivalRef = doc(db, 'newArrivals', id);

    // Delete the new arrival
    await deleteDoc(newArrivalRef);
  } catch (error) {
    console.error(`Error deleting new arrival ${id}:`, error);
    throw error;
  }
}

/**
 * Toggle new arrival active status
 * @param id New arrival ID
 * @param isActive Whether the new arrival should be active
 */
export async function toggleNewArrivalActive(id: string, isActive: boolean): Promise<void> {
  try {
    const newArrivalRef = doc(db, 'newArrivals', id);

    await updateDoc(newArrivalRef, {
      isActive,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error toggling new arrival ${id} active status:`, error);
    throw error;
  }
}
