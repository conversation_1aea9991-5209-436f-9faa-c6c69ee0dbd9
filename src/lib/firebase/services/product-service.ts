import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  DocumentData,
  QueryDocumentSnapshot,
  addDoc,
  updateDoc,
  deleteDoc
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../config';
import { Product, Review } from '@/types';

const PRODUCTS_COLLECTION = 'products';

// Convert Firestore document to Product type
const convertProduct = (doc: QueryDocumentSnapshot<DocumentData>): Product => {
  const data = doc.data();
  return {
    id: doc.id,
    name: data.name,
    description: data.description,
    price: data.price,
    compareAtPrice: data.compareAtPrice,
    images: data.images || [],
    category: data.category,
    categoryId: data.categoryId || data.category, // Fallback to category if categoryId is not available
    variants: data.variants || [],
    stock: data.stock,
    status: data.status || 'active',
    slug: data.slug || doc.id,
    featured: data.featured || false,
    ratingAvg: data.ratingAvg,
    ratingCount: data.ratingCount,
    features: data.features,
    dimensions: data.dimensions,
    weight: data.weight,
    sku: data.sku,
    barcode: data.barcode,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt
  };
};

// Get all products
export const getAllProducts = async (): Promise<Product[]> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('status', '==', 'active'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(convertProduct);
  } catch (error) {
    console.error('Error getting products:', error);
    throw error;
  }
};

// Get products by category
export const getProductsByCategory = async (categoryId: string): Promise<Product[]> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(
      productsRef,
      where('category', '==', categoryId),
      where('status', '==', 'active')
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(convertProduct);
  } catch (error) {
    console.error(`Error getting products for category ${categoryId}:`, error);
    throw error;
  }
};

// Get featured products
export const getFeaturedProducts = async (limitCount = 4): Promise<Product[]> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    // Use the imported limit() function here
    const q = query(
      productsRef,
      where('status', '==', 'active'),
      orderBy('createdAt', 'desc'),
      limit(limitCount) // <-- Correct usage: wrap the number with the limit() function
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(convertProduct);
  } catch (error) {
    console.error('Error getting featured products:', error);
    // Consider more specific error handling or re-throwing a custom error
    throw new Error('Failed to fetch featured products.'); // Example of a more user-friendly error
  }
};

// Get a single product by ID
export const getProductById = async (productId: string, adminMode = false): Promise<Product | null> => {
  try {
    const productRef = doc(db, PRODUCTS_COLLECTION, productId);
    const productSnap = await getDoc(productRef);

    if (productSnap.exists()) {
      const product = convertProduct(productSnap as QueryDocumentSnapshot<DocumentData>);

      // In client mode, only return active products
      if (!adminMode && product.status !== 'active') {
        return null;
      }

      return product;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting product ${productId}:`, error);
    throw error;
  }
};

// Get a single product by slug
export const getProductBySlug = async (slug: string): Promise<Product | null> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('slug', '==', slug), where('status', '==', 'active'));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      return convertProduct(querySnapshot.docs[0]);
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting product by slug ${slug}:`, error);
    throw error;
  }
};

// Get related products
export const getRelatedProducts = async (categoryId: string, productId: string, limitCount = 4): Promise<Product[]> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(
      productsRef,
      where('categoryId', '==', categoryId),
      where('status', '==', 'active'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);

    // Filter out the current product after fetching
    return querySnapshot.docs
      .map(convertProduct)
      .filter(product => product.id !== productId);
  } catch (error) {
    console.error(`Error getting related products for ${productId}:`, error);
    throw error;
  }
};

// Get products by IDs
export const getProductsByIds = async (productIds: string[], adminMode = false): Promise<Product[]> => {
  try {
    if (!productIds || productIds.length === 0) {
      return [];
    }

    // Firestore doesn't support a direct "where id in [...]" query
    // So we need to fetch each product individually
    const productPromises = productIds.map(id => getProductById(id, adminMode));
    const products = await Promise.all(productPromises);

    // Filter out any null products (in case a product was deleted or is inactive)
    return products.filter(product => product !== null) as Product[];
  } catch (error) {
    console.error('Error getting products by IDs:', error);
    throw error;
  }
};

// Get product reviews
export const getProductReviews = async (productId: string): Promise<Review[]> => {
  try {
    const reviewsRef = collection(db, 'reviews');
    const q = query(
      reviewsRef,
      where('productId', '==', productId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        productId: data.productId,
        userId: data.userId,
        userName: data.userName,
        rating: data.rating,
        title: data.title,
        comment: data.comment,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      };
    });
  } catch (error) {
    console.error(`Error getting reviews for product ${productId}:`, error);
    throw error;
  }
};

// Search products
export const searchProducts = async (searchTerm: string): Promise<Product[]> => {
  try {
    // Firestore doesn't support full-text search natively
    // This is a simple implementation that searches by name
    // For a more robust solution, consider using Algolia or ElasticSearch
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(
      productsRef,
      where('status', '==', 'active')
    );
    const querySnapshot = await getDocs(q);

    const searchTermLower = searchTerm.toLowerCase();

    return querySnapshot.docs
      .map(convertProduct)
      .filter(product =>
        product.name.toLowerCase().includes(searchTermLower) ||
        product.description.toLowerCase().includes(searchTermLower)
      );
  } catch (error) {
    console.error(`Error searching products for "${searchTerm}":`, error);
    throw error;
  }
};

// Admin functions

// Check if a slug already exists
export const checkSlugExists = async (slug: string, excludeProductId?: string): Promise<boolean> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('slug', '==', slug));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return false;
    }

    // If we're updating a product, we need to exclude the current product from the check
    if (excludeProductId) {
      // Check if the only product with this slug is the one we're updating
      return querySnapshot.docs.some(doc => doc.id !== excludeProductId);
    }

    return true;
  } catch (error) {
    console.error(`Error checking if slug "${slug}" exists:`, error);
    throw error;
  }
};

// Generate a unique slug from a name
export const generateUniqueSlug = async (name: string, productId?: string): Promise<string> => {
  // Generate base slug
  let baseSlug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');

  // Check if the slug already exists
  let slug = baseSlug;
  let counter = 1;
  let slugExists = await checkSlugExists(slug, productId);

  // If the slug exists, append a number until we find a unique slug
  while (slugExists) {
    slug = `${baseSlug}-${counter}`;
    counter++;
    slugExists = await checkSlugExists(slug, productId);
  }

  return slug;
};

// Add a new product
export const addProduct = async (product: Omit<Product, 'id'>, imageFiles?: File[]): Promise<string> => {
  try {
    // Upload images if provided
    const imageUrls: string[] = [];

    if (imageFiles && imageFiles.length > 0) {
      for (const file of imageFiles) {
        const storageRef = ref(storage, `products/${Date.now()}_${file.name}`);
        const snapshot = await uploadBytes(storageRef, file);
        const downloadUrl = await getDownloadURL(snapshot.ref);
        imageUrls.push(downloadUrl);
      }
    }

    // Generate a unique slug if one doesn't exist
    let slug;
    if (product.slug) {
      // If a slug is provided, check if it's unique
      slug = await checkSlugExists(product.slug)
        ? await generateUniqueSlug(product.name) // If exists, generate a new one
        : product.slug; // If unique, use the provided slug
    } else {
      // Generate a new unique slug from the name
      slug = await generateUniqueSlug(product.name);
    }

    // Create product with image URLs and remove undefined values
    const productData = {
      ...product,
      images: imageUrls.length > 0 ? imageUrls : product.images,
      slug,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Remove undefined values (Firestore doesn't accept undefined)
    const cleanedProductData = Object.entries(productData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);

    const docRef = await addDoc(collection(db, PRODUCTS_COLLECTION), cleanedProductData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding product:', error);
    throw error;
  }
};

// Update a product
export const updateProduct = async (
  productId: string,
  productData: Partial<Product>,
  newImageFiles?: File[],
  imagesToDelete?: string[]
): Promise<void> => {
  try {
    const productRef = doc(db, PRODUCTS_COLLECTION, productId);

    // Get current product data
    const productSnap = await getDoc(productRef);
    if (!productSnap.exists()) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    const currentProduct = productSnap.data();
    let updatedImages = [...(currentProduct.images || [])];

    // Delete images if specified
    if (imagesToDelete && imagesToDelete.length > 0) {
      for (const imageUrl of imagesToDelete) {
        try {
          // Extract the path from the URL
          const imagePath = imageUrl.split('?')[0].split('/o/')[1];
          if (imagePath) {
            const decodedPath = decodeURIComponent(imagePath);
            const imageRef = ref(storage, decodedPath);
            await deleteObject(imageRef);
          }
          // Remove from images array
          updatedImages = updatedImages.filter(url => url !== imageUrl);
        } catch (deleteError) {
          console.error(`Error deleting image ${imageUrl}:`, deleteError);
        }
      }
    }

    // Upload new images if provided
    if (newImageFiles && newImageFiles.length > 0) {
      for (const file of newImageFiles) {
        const storageRef = ref(storage, `products/${Date.now()}_${file.name}`);
        const snapshot = await uploadBytes(storageRef, file);
        const downloadUrl = await getDownloadURL(snapshot.ref);
        updatedImages.push(downloadUrl);
      }
    }

    // Check if slug is being updated and ensure it's unique
    let updatedSlug = productData.slug;
    if (updatedSlug && updatedSlug !== currentProduct.slug) {
      // If a new slug is provided, check if it's unique
      if (await checkSlugExists(updatedSlug, productId)) {
        // If the slug exists, generate a new unique slug
        updatedSlug = await generateUniqueSlug(
          productData.name || currentProduct.name,
          productId
        );
      }
    }

    // Prepare update data
    const updateData = {
      ...productData,
      slug: updatedSlug, // Use the validated slug
      images: updatedImages,
      updatedAt: new Date()
    };

    // Remove undefined values (Firestore doesn't accept undefined)
    const cleanedUpdateData = Object.entries(updateData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);

    // Update product data
    await updateDoc(productRef, cleanedUpdateData);
  } catch (error) {
    console.error(`Error updating product ${productId}:`, error);
    throw error;
  }
};

// Delete a product
export const deleteProduct = async (productId: string): Promise<void> => {
  try {
    // Get product to delete its images
    const productRef = doc(db, PRODUCTS_COLLECTION, productId);
    const productSnap = await getDoc(productRef);

    if (productSnap.exists()) {
      const productData = productSnap.data();

      // Delete images from storage
      if (productData.images && productData.images.length > 0) {
        for (const imageUrl of productData.images) {
          try {
            // Extract the path from the URL
            const imagePath = imageUrl.split('?')[0].split('/o/')[1];
            if (imagePath) {
              const decodedPath = decodeURIComponent(imagePath);
              const imageRef = ref(storage, decodedPath);
              await deleteObject(imageRef);
            }
          } catch (deleteError) {
            console.error(`Error deleting image ${imageUrl}:`, deleteError);
          }
        }
      }

      // Delete the product document
      await deleteDoc(productRef);
    }
  } catch (error) {
    console.error(`Error deleting product ${productId}:`, error);
    throw error;
  }
};
