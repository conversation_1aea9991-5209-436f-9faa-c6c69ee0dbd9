import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  DocumentData,
  QueryDocumentSnapshot,
  addDoc,
  updateDoc,
  deleteDoc,
  limit as firestoreLimit,
  startAfter,
  writeBatch,
  getCountFromServer,
  QueryConstraint
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../config';

export interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  slug: string;
  isActive: boolean;
  order?: number;
  parentId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Extended Category interface with hierarchical structure
export interface CategoryWithChildren extends Category {
  children?: CategoryWithChildren[];
}

// Category filter options interface
export interface CategoryFilterOptions {
  isActive?: boolean;
  parentId?: string | null; // null means root categories
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  startAfter?: DocumentData | QueryDocumentSnapshot<DocumentData>;
}

const CATEGORIES_COLLECTION = 'categories';

// Convert Firestore document to Category type
const convertCategory = (doc: QueryDocumentSnapshot<DocumentData>): Category => {
  const data = doc.data();
  return {
    id: doc.id,
    name: data.name,
    description: data.description,
    image: data.image || '',
    slug: data.slug,
    isActive: data.isActive !== false, // Default to true if not specified
    order: data.order || 0,
  };
};

// Get all categories, with option to filter by active status
export const getAllCategories = async (activeOnly: boolean = false): Promise<Category[]> => {
  try {
    console.log(`Getting all categories with activeOnly=${activeOnly}`);
    console.log(`Using collection name: ${CATEGORIES_COLLECTION}`);

    const categoriesRef = collection(db, CATEGORIES_COLLECTION);

    // Build a simple query without constraints first
    const categoriesQuery = query(categoriesRef);

    // Execute the query
    const querySnapshot = await getDocs(categoriesQuery);

    console.log(`Query returned ${querySnapshot.docs.length} categories`);

    // Log the first document if available
    if (querySnapshot.docs.length > 0) {
      console.log('First category data:', querySnapshot.docs[0].data());
    }

    // Convert the documents to Category objects
    const categories = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        image: data.image || '/images/placeholder.png',
        slug: data.slug,
        isActive: data.isActive !== false, // Default to true if not specified
        order: data.order || 0
      };
    });

    // Filter by active status if needed
    return activeOnly ? categories.filter(cat => cat.isActive) : categories;
  } catch (error) {
    console.error('Error getting categories:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};

// Get a single category by slug
export const getCategoryBySlug = async (slug: string): Promise<Category | null> => {
  try {
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);
    const q = query(categoriesRef, where('slug', '==', slug));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    return convertCategory(querySnapshot.docs[0]);
  } catch (error) {
    console.error(`Error getting category with slug ${slug}:`, error);
    throw error;
  }
};

// Get a single category by ID
export const getCategoryById = async (categoryId: string): Promise<Category | null> => {
  try {
    const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);
    const categorySnap = await getDoc(categoryRef);

    if (categorySnap.exists()) {
      return {
        id: categorySnap.id,
        ...categorySnap.data(),
      } as Category;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting category ${categoryId}:`, error);
    throw error;
  }
};

// Admin functions

// Add a new category
export const addCategory = async (
  category: Omit<Category, 'id'>,
  imageFile?: File
): Promise<string> => {
  try {
    let imageUrl = category.image;

    // Upload image if provided
    if (imageFile) {
      const storageRef = ref(storage, `categories/${Date.now()}_${imageFile.name}`);
      const snapshot = await uploadBytes(storageRef, imageFile);
      imageUrl = await getDownloadURL(snapshot.ref);
    }

    // Create category with image URL
    const categoryData = {
      ...category,
      image: imageUrl,
      isActive: category.isActive !== undefined ? category.isActive : true, // Set isActive to true by default
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const docRef = await addDoc(collection(db, CATEGORIES_COLLECTION), categoryData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding category:', error);
    throw error;
  }
};

// Update a category
export const updateCategory = async (
  categoryId: string,
  categoryData: Partial<Category>,
  newImageFile?: File
): Promise<void> => {
  try {
    const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);

    let updatedData = { ...categoryData };

    // Upload new image if provided
    if (newImageFile) {
      // Get current category to delete old image
      const categorySnap = await getDoc(categoryRef);
      if (categorySnap.exists()) {
        const currentCategory = categorySnap.data();

        // Delete old image if it exists
        if (currentCategory.image) {
          try {
            // Extract the path from the URL
            const imagePath = currentCategory.image.split('?')[0].split('/o/')[1];
            if (imagePath) {
              const decodedPath = decodeURIComponent(imagePath);
              const imageRef = ref(storage, decodedPath);
              await deleteObject(imageRef);
            }
          } catch (deleteError) {
            console.error(`Error deleting image ${currentCategory.image}:`, deleteError);
          }
        }
      }

      // Upload new image
      const storageRef = ref(storage, `categories/${Date.now()}_${newImageFile.name}`);
      const snapshot = await uploadBytes(storageRef, newImageFile);
      const imageUrl = await getDownloadURL(snapshot.ref);

      updatedData = {
        ...updatedData,
        image: imageUrl
      };
    }

    // Update category data
    await updateDoc(categoryRef, {
      ...updatedData,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error(`Error updating category ${categoryId}:`, error);
    throw error;
  }
};

// Delete a category
export const deleteCategory = async (categoryId: string): Promise<void> => {
  try {
    // Get category to delete its image
    const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);
    const categorySnap = await getDoc(categoryRef);

    if (categorySnap.exists()) {
      const categoryData = categorySnap.data();

      // Delete image from storage
      if (categoryData.image) {
        try {
          // Extract the path from the URL
          const imagePath = categoryData.image.split('?')[0].split('/o/')[1];
          if (imagePath) {
            const decodedPath = decodeURIComponent(imagePath);
            const imageRef = ref(storage, decodedPath);
            await deleteObject(imageRef);
          }
        } catch (deleteError) {
          console.error(`Error deleting image ${categoryData.image}:`, deleteError);
        }
      }

      // Delete the category document
      await deleteDoc(categoryRef);
    }
  } catch (error) {
    console.error(`Error deleting category ${categoryId}:`, error);
    throw error;
  }
};

// Get categories with filtering and pagination
export const getCategories = async (options: CategoryFilterOptions = {}): Promise<{
  categories: Category[];
  hasMore: boolean;
  cursor: QueryDocumentSnapshot<DocumentData> | null;
  total: number;
}> => {
  try {
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);

    // Build query constraints
    const constraints: QueryConstraint[] = [];

    // Apply filters
    if (options.isActive !== undefined) {
      constraints.push(where('isActive', '==', options.isActive));
    }

    if (options.parentId === null) {
      // Get root categories (no parentId or parentId is empty string)
      constraints.push(where('parentId', 'in', [null, '']));
    } else if (options.parentId !== undefined) {
      // Get categories with specific parentId
      constraints.push(where('parentId', '==', options.parentId));
    }

    // Apply sorting
    const sortBy = options.sortBy || 'order';
    const sortOrder = options.sortOrder || 'asc';
    constraints.push(orderBy(sortBy, sortOrder));

    // Create the base query
    let q = query(categoriesRef, ...constraints);

    // Apply cursor-based pagination
    if (options.startAfter) {
      q = query(q, startAfter(options.startAfter));
    }

    // Apply limit
    const limit = options.limit || 100; // Default to a higher limit for categories
    q = query(q, firestoreLimit(limit + 1)); // Get one extra to check if there are more results

    // Execute query
    const snapshot = await getDocs(q);

    // Process results
    const categories = snapshot.docs.slice(0, limit).map(convertCategory);

    // Check if there are more results
    const hasMore = snapshot.docs.length > limit;

    // Get the last document for next pagination
    const lastDoc = categories.length > 0 ? snapshot.docs[categories.length - 1] : null;

    // If search is provided, filter results client-side
    let filteredCategories = categories;
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredCategories = categories.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      );
    }

    // Get total count (for initial load only)
    let total = 0;
    if (!options.startAfter) {
      // Create a simplified query for counting
      let countConstraints: QueryConstraint[] = [];
      if (options.isActive !== undefined) {
        countConstraints.push(where('isActive', '==', options.isActive));
      }

      const countQuery = query(categoriesRef, ...countConstraints);
      const countSnapshot = await getCountFromServer(countQuery);
      total = countSnapshot.data().count;
    }

    return {
      categories: filteredCategories,
      cursor: lastDoc,
      hasMore,
      total
    };
  } catch (error) {
    console.error('Error getting categories with filtering:', error);
    throw error;
  }
};

// Get categories with hierarchical structure
export const getCategoriesWithHierarchy = async (): Promise<CategoryWithChildren[]> => {
  try {
    // Get all active categories for the hierarchy (for client-side use)
    const allCategories = await getAllCategories(true);

    // Create a map of categories by ID
    const categoriesMap = new Map<string, CategoryWithChildren>();
    allCategories.forEach(category => {
      categoriesMap.set(category.id, {
        ...category,
        children: [],
      });
    });

    // Build the hierarchy
    const rootCategories: CategoryWithChildren[] = [];
    allCategories.forEach(category => {
      if (category.parentId && categoriesMap.has(category.parentId)) {
        // Add as child to parent
        categoriesMap.get(category.parentId)!.children!.push(categoriesMap.get(category.id)!);
      } else {
        // Add to root categories
        rootCategories.push(categoriesMap.get(category.id)!);
      }
    });

    return rootCategories;
  } catch (error) {
    console.error('Error getting categories with hierarchy:', error);
    throw error;
  }
};

// Check if slug is unique
export const isSlugUnique = async (slug: string, excludeCategoryId?: string): Promise<boolean> => {
  try {
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);

    // Query for categories with the same slug
    const q = query(categoriesRef, where('slug', '==', slug));
    const snapshot = await getDocs(q);

    // If no categories found, slug is unique
    if (snapshot.empty) {
      return true;
    }

    // If excluding a category (for updates), check if the only match is the excluded category
    if (excludeCategoryId) {
      return snapshot.size === 1 && snapshot.docs[0].id === excludeCategoryId;
    }

    // Slug is not unique
    return false;
  } catch (error) {
    console.error(`Error checking slug uniqueness for ${slug}:`, error);
    throw error;
  }
};

// Generate a unique slug
export const generateUniqueSlug = async (name: string, excludeCategoryId?: string): Promise<string> => {
  try {
    // Generate base slug
    let baseSlug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Remove leading and trailing hyphens
    baseSlug = baseSlug.replace(/^-+|-+$/g, '');

    // Check if base slug is unique
    let slug = baseSlug;
    let isUnique = await isSlugUnique(slug, excludeCategoryId);
    let counter = 1;

    // If not unique, append a number until it is
    while (!isUnique) {
      slug = `${baseSlug}-${counter}`;
      isUnique = await isSlugUnique(slug, excludeCategoryId);
      counter++;
    }

    return slug;
  } catch (error) {
    console.error(`Error generating unique slug for ${name}:`, error);
    throw error;
  }
};

// Reorder categories
export const reorderCategories = async (categoryIds: string[]): Promise<void> => {
  try {
    const batch = writeBatch(db);

    // Update order for each category
    categoryIds.forEach((categoryId, index) => {
      const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);
      batch.update(categoryRef, {
        order: index,
        updatedAt: new Date()
      });
    });

    // Commit the batch
    await batch.commit();
  } catch (error) {
    console.error('Error reordering categories:', error);
    throw error;
  }
};
