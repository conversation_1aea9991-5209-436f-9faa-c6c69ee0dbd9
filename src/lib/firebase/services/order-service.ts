import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  addDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  serverTimestamp,
  startAfter
} from 'firebase/firestore';
import { db } from '../config';
import { OrderNote } from '@/lib/firebase/admin-services/order-service';
import { Order, OrderStatus, OrderItem, Address, PaymentMethod, PaymentStatus } from '@/types';

const ORDERS_COLLECTION = 'orders';
const NOTES_COLLECTION = 'notes';

// Create a new order
export const createOrder = async (
  userId: string,
  items: OrderItem[],
  shippingAddress: Address,
  billingAddress: Address,
  paymentMethod: PaymentMethod,
  subtotal: number,
  shippingCost: number,
  total: number
): Promise<string> => {
  try {
    const orderData = {
      userId,
      items,
      status: 'pending' as OrderStatus,
      shippingAddress,
      billingAddress,
      paymentMethod,
      subtotal,
      shippingCost,
      total,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, ORDERS_COLLECTION), orderData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Create a pending order for payment processing
export const createPendingOrder = async (
  userId: string,
  email: string,
  items: OrderItem[],
  shippingAddress: Address,
  billingAddress: Address,
  shippingMethodDetails: {
    id: string;
    name: string;
    price: number;
  },
  orderSubtotal: number,
  shippingCostTotal: number,
  grandTotal: number,
  currency: string
): Promise<string> => {
  try {
    const orderData = {
      userId,
      email,
      items,
      status: 'pending_payment' as OrderStatus,
      shippingAddress,
      billingAddress,
      shippingMethodDetails,
      orderSubtotal,
      shippingCost: shippingCostTotal,
      total: grandTotal,
      currency: currency.toLowerCase(),
      paymentStatus: 'pending' as PaymentStatus,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, ORDERS_COLLECTION), orderData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating pending order:', error);
    throw error;
  }
};

// Get order by Stripe payment intent ID
export const getOrderByPaymentIntentId = async (paymentIntentId: string): Promise<Order | null> => {
  try {
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const q = query(ordersRef, where('stripePaymentIntentId', '==', paymentIntentId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();

    return {
      id: doc.id,
      userId: data.userId,
      items: data.items,
      status: data.status,
      shippingAddress: data.shippingAddress,
      billingAddress: data.billingAddress,
      paymentMethod: data.paymentMethod,
      subtotal: data.orderSubtotal || data.subtotal,
      shippingCost: data.shippingCost,
      total: data.total,
      paymentStatus: data.paymentStatus,
      stripePaymentIntentId: data.stripePaymentIntentId,
      createdAt: (data.createdAt as Timestamp).toDate(),
      updatedAt: (data.updatedAt as Timestamp).toDate()
    } as Order;
  } catch (error) {
    console.error(`Error getting order by payment intent ID ${paymentIntentId}:`, error);
    throw error;
  }
};

// Get orders for a specific user
export const getUserOrders = async (userId: string): Promise<Order[]> => {
  try {
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const q = query(
      ordersRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        items: data.items,
        status: data.status,
        shippingAddress: data.shippingAddress,
        billingAddress: data.billingAddress,
        paymentMethod: data.paymentMethod,
        subtotal: data.subtotal,
        total: data.total,
        createdAt: (data.createdAt as Timestamp).toDate(),
        updatedAt: (data.updatedAt as Timestamp).toDate()
      } as Order;
    });
  } catch (error) {
    console.error(`Error getting orders for user ${userId}:`, error);
    throw error;
  }
};

// Get a specific order by ID
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);
    const orderSnap = await getDoc(orderRef);

    if (orderSnap.exists()) {
      const data = orderSnap.data();
      return {
        id: orderSnap.id,
        userId: data.userId,
        items: data.items,
        status: data.status,
        shippingAddress: data.shippingAddress,
        billingAddress: data.billingAddress,
        paymentMethod: data.paymentMethod,
        subtotal: data.subtotal,
        total: data.total,
        createdAt: (data.createdAt as Timestamp).toDate(),
        updatedAt: (data.updatedAt as Timestamp).toDate()
      } as Order;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    throw error;
  }
};

// Update order status
export const updateOrderStatus = async (
  orderId: string,
  status: OrderStatus
): Promise<void> => {
  try {
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);

    // Get current order to check if status is changing
    const orderSnap = await getDoc(orderRef);
    if (orderSnap.exists()) {
      const currentOrder = orderSnap.data();

      // If status is changing, add to status history
      if (currentOrder.status !== status) {
        await updateDoc(orderRef, {
          status,
          statusHistory: [
            ...(currentOrder.statusHistory || []),
            {
              status,
              timestamp: new Date()
            }
          ],
          updatedAt: serverTimestamp()
        });
        return;
      }
    }

    // If we get here, either the order doesn't exist or the status isn't changing
    await updateDoc(orderRef, {
      status,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error(`Error updating status for order ${orderId}:`, error);
    throw error;
  }
};

// Update order
export const updateOrder = async (
  orderId: string,
  orderData: Partial<Order>
): Promise<void> => {
  try {
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);

    // If status is being updated, check if it's changing
    if (orderData.status) {
      const orderSnap = await getDoc(orderRef);
      if (orderSnap.exists()) {
        const currentOrder = orderSnap.data();

        // If status is changing, add to status history
        if (currentOrder.status !== orderData.status) {
          orderData.statusHistory = [
            ...(currentOrder.statusHistory || []),
            {
              status: orderData.status,
              timestamp: new Date()
            }
          ];
        }
      }
    }

    await updateDoc(orderRef, {
      ...orderData,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error(`Error updating order ${orderId}:`, error);
    throw error;
  }
};

// Order notes functions

// Get notes for an order
export const getOrderNotes = async (orderId: string): Promise<OrderNote[]> => {
  try {
    const notesRef = collection(db, ORDERS_COLLECTION, orderId, NOTES_COLLECTION);
    const q = query(notesRef, orderBy('createdAt', 'desc'));

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        orderId,
        userId: data.userId,
        userName: data.userName,
        content: data.content,
        isInternal: data.isInternal,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate() : new Date(data.createdAt)
      } as OrderNote;
    });
  } catch (error) {
    console.error(`Error getting notes for order ${orderId}:`, error);
    throw error;
  }
};

// Add a note to an order
export const addOrderNote = async (
  orderId: string,
  content: string,
  isInternal: boolean = true
): Promise<string> => {
  try {
    // In a real app, you would get the current user from your auth context
    // For now, we'll use placeholder values
    const userId = 'current-user-id';
    const userName = 'Current User';

    const notesRef = collection(db, ORDERS_COLLECTION, orderId, NOTES_COLLECTION);

    const docRef = await addDoc(notesRef, {
      userId,
      userName,
      content,
      isInternal,
      createdAt: serverTimestamp()
    });

    // Update order with note count
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);
    const notes = await getOrderNotes(orderId);

    await updateDoc(orderRef, {
      noteCount: notes.length,
      updatedAt: serverTimestamp()
    });

    return docRef.id;
  } catch (error) {
    console.error(`Error adding note to order ${orderId}:`, error);
    throw error;
  }
};

// Admin functions

// Get all orders (for admin)
export const getAllOrders = async (
  limitCount = 20,
  startAfterDoc?: any
): Promise<{ orders: Order[], lastDoc: any }> => {
  try {
    const ordersRef = collection(db, ORDERS_COLLECTION);
    let q;

    if (startAfterDoc) {
      q = query(
        ordersRef,
        orderBy('createdAt', 'desc'),
        startAfterDoc,
        limit(limitCount)
      );
    } else {
      q = query(
        ordersRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
    }

    const querySnapshot = await getDocs(q);
    const lastVisible = querySnapshot.docs[querySnapshot.docs.length - 1];

    const orders = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        items: data.items,
        status: data.status,
        shippingAddress: data.shippingAddress,
        billingAddress: data.billingAddress,
        paymentMethod: data.paymentMethod,
        subtotal: data.subtotal,
        total: data.total,
        createdAt: (data.createdAt as Timestamp).toDate(),
        updatedAt: (data.updatedAt as Timestamp).toDate()
      } as Order;
    });

    return {
      orders,
      lastDoc: lastVisible
    };
  } catch (error) {
    console.error('Error getting all orders:', error);
    throw error;
  }
};

// Get orders by status (for admin)
export const getOrdersByStatus = async (
  status: OrderStatus,
  limitCount = 20,
  startAfterDoc?: any
): Promise<{ orders: Order[], lastDoc: any }> => {
  try {
    const ordersRef = collection(db, ORDERS_COLLECTION);
    let q;

    if (startAfterDoc) {
      q = query(
        ordersRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc'),
        startAfterDoc,
        limit(limitCount)
      );
    } else {
      q = query(
        ordersRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
    }

    const querySnapshot = await getDocs(q);
    const lastVisible = querySnapshot.docs[querySnapshot.docs.length - 1];

    const orders = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        items: data.items,
        status: data.status,
        shippingAddress: data.shippingAddress,
        billingAddress: data.billingAddress,
        paymentMethod: data.paymentMethod,
        subtotal: data.subtotal,
        total: data.total,
        createdAt: (data.createdAt as Timestamp).toDate(),
        updatedAt: (data.updatedAt as Timestamp).toDate()
      } as Order;
    });

    return {
      orders,
      lastDoc: lastVisible
    };
  } catch (error) {
    console.error(`Error getting orders with status ${status}:`, error);
    throw error;
  }
};
