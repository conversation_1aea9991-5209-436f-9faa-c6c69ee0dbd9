import nodemailer from 'nodemailer';
import { getEmailSettingsAdmin } from '@/lib/firebase/admin-services/settings-service';
import { Order } from '@/types';
import { PaymentInstructions } from '@/types/checkout';

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private emailSettings: any = null;

  private async initializeTransporter() {
    if (this.transporter && this.emailSettings) {
      return;
    }

    this.emailSettings = await getEmailSettingsAdmin();
    
    if (!this.emailSettings) {
      throw new Error('Email settings not configured');
    }

    this.transporter = nodemailer.createTransporter({
      host: this.emailSettings.smtpHost,
      port: this.emailSettings.smtpPort,
      secure: this.emailSettings.smtpPort === 465, // true for 465, false for other ports
      auth: {
        user: this.emailSettings.smtpUser,
        pass: this.emailSettings.smtpPassword,
      },
    });
  }

  async sendEmail(to: string, template: EmailTemplate): Promise<void> {
    await this.initializeTransporter();

    if (!this.transporter || !this.emailSettings) {
      throw new Error('Email service not initialized');
    }

    const mailOptions = {
      from: `"${this.emailSettings.smtpFromName}" <${this.emailSettings.smtpFromEmail}>`,
      to,
      subject: template.subject,
      text: template.text,
      html: template.html,
    };

    await this.transporter.sendMail(mailOptions);
  }

  generatePaymentInstructionsEmail(
    order: Order,
    paymentInstructions: PaymentInstructions
  ): EmailTemplate {
    const formatCurrency = (amount: number, currency: string) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount);
    };

    const subject = `Payment Instructions for Order ${paymentInstructions.orderReference}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
          .payment-details { background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .bank-details { background-color: #f5f5f5; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .important { background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; }
          .order-summary { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          .amount { font-size: 24px; font-weight: bold; color: #1976d2; }
          .reference { font-size: 18px; font-weight: bold; font-family: monospace; color: #d32f2f; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Payment Instructions</h1>
            <p>Thank you for your order! Please follow the instructions below to complete your payment.</p>
          </div>

          <div class="payment-details">
            <h2>Payment Amount</h2>
            <div class="amount">${formatCurrency(paymentInstructions.amount, paymentInstructions.currency)}</div>
          </div>

          <div class="important">
            <h3>⚠️ Important: Order Reference</h3>
            <p>Please include this reference number in your bank transfer:</p>
            <div class="reference">${paymentInstructions.orderReference}</div>
          </div>

          <div class="bank-details">
            <h3>Bank Account Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px; font-weight: bold;">Bank Name:</td>
                <td style="padding: 8px;">${paymentInstructions.bankAccountDetails.bankName}</td>
              </tr>
              <tr>
                <td style="padding: 8px; font-weight: bold;">Account Name:</td>
                <td style="padding: 8px;">${paymentInstructions.bankAccountDetails.accountName}</td>
              </tr>
              <tr>
                <td style="padding: 8px; font-weight: bold;">Account Number:</td>
                <td style="padding: 8px; font-family: monospace;">${paymentInstructions.bankAccountDetails.accountNumber}</td>
              </tr>
              ${paymentInstructions.bankAccountDetails.routingNumber ? `
              <tr>
                <td style="padding: 8px; font-weight: bold;">Routing Number:</td>
                <td style="padding: 8px; font-family: monospace;">${paymentInstructions.bankAccountDetails.routingNumber}</td>
              </tr>
              ` : ''}
              ${paymentInstructions.bankAccountDetails.swiftCode ? `
              <tr>
                <td style="padding: 8px; font-weight: bold;">SWIFT Code:</td>
                <td style="padding: 8px; font-family: monospace;">${paymentInstructions.bankAccountDetails.swiftCode}</td>
              </tr>
              ` : ''}
            </table>
          </div>

          <div class="order-summary">
            <h3>Order Summary</h3>
            <p><strong>Order ID:</strong> ${order.id}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
            <p><strong>Items:</strong></p>
            <ul>
              ${order.items.map(item => `
                <li>${item.name} x ${item.quantity} - ${formatCurrency(item.price * item.quantity, paymentInstructions.currency)}</li>
              `).join('')}
            </ul>
            <p><strong>Subtotal:</strong> ${formatCurrency(order.orderSubtotal || order.subtotal, paymentInstructions.currency)}</p>
            <p><strong>Shipping:</strong> ${formatCurrency(order.shippingCost || 0, paymentInstructions.currency)}</p>
            <p><strong>Total:</strong> ${formatCurrency(order.total, paymentInstructions.currency)}</p>
          </div>

          <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
            <h3>Payment Instructions</h3>
            <p>${paymentInstructions.instructions}</p>
            ${paymentInstructions.dueDate ? `
            <p><strong>Payment Due Date:</strong> ${new Date(paymentInstructions.dueDate).toLocaleDateString()}</p>
            ` : ''}
          </div>

          <div class="footer">
            <p>If you have any questions about your order or payment, please contact our support team.</p>
            <p>Thank you for your business!</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Payment Instructions for Order ${paymentInstructions.orderReference}

Thank you for your order! Please follow the instructions below to complete your payment.

PAYMENT AMOUNT: ${formatCurrency(paymentInstructions.amount, paymentInstructions.currency)}

IMPORTANT: Please include this reference number in your bank transfer:
${paymentInstructions.orderReference}

BANK ACCOUNT DETAILS:
Bank Name: ${paymentInstructions.bankAccountDetails.bankName}
Account Name: ${paymentInstructions.bankAccountDetails.accountName}
Account Number: ${paymentInstructions.bankAccountDetails.accountNumber}
${paymentInstructions.bankAccountDetails.routingNumber ? `Routing Number: ${paymentInstructions.bankAccountDetails.routingNumber}\n` : ''}
${paymentInstructions.bankAccountDetails.swiftCode ? `SWIFT Code: ${paymentInstructions.bankAccountDetails.swiftCode}\n` : ''}

ORDER SUMMARY:
Order ID: ${order.id}
Order Date: ${new Date(order.createdAt).toLocaleDateString()}

Items:
${order.items.map(item => `- ${item.name} x ${item.quantity} - ${formatCurrency(item.price * item.quantity, paymentInstructions.currency)}`).join('\n')}

Subtotal: ${formatCurrency(order.orderSubtotal || order.subtotal, paymentInstructions.currency)}
Shipping: ${formatCurrency(order.shippingCost || 0, paymentInstructions.currency)}
Total: ${formatCurrency(order.total, paymentInstructions.currency)}

PAYMENT INSTRUCTIONS:
${paymentInstructions.instructions}
${paymentInstructions.dueDate ? `Payment Due Date: ${new Date(paymentInstructions.dueDate).toLocaleDateString()}` : ''}

If you have any questions about your order or payment, please contact our support team.

Thank you for your business!
    `;

    return { subject, html, text };
  }

  async sendPaymentInstructions(
    order: Order,
    paymentInstructions: PaymentInstructions
  ): Promise<void> {
    const template = this.generatePaymentInstructionsEmail(order, paymentInstructions);
    await this.sendEmail(order.email || '', template);
  }
}

// Singleton instance
export const emailService = new EmailService();
