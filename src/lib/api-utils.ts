import { NextResponse } from 'next/server';

/**
 * Standard API error response format
 */
export interface ApiError {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

/**
 * Standard API success response format
 */
export interface ApiSuccess<T> {
  success: true;
  data: T;
  message?: string;
}

/**
 * Create a standardized API error response
 * 
 * @param error The error object or message
 * @param message A user-friendly error message
 * @param status HTTP status code
 * @returns NextResponse with standardized error format
 */
export function handleApiError(error: any, message: string, status: number = 500): NextResponse {
  console.error(`${message}:`, error);
  
  // Default error response
  const errorResponse: ApiError = {
    success: false,
    error: message,
  };
  
  // Add error code if available
  if (error.code) {
    errorResponse.code = error.code;
  }
  
  // Add detailed error information in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.details = String(error);
  }
  
  // Handle specific error types
  if (error.code === 'permission-denied' || error.code === 'PERMISSION_DENIED') {
    return NextResponse.json(
      {
        ...errorResponse,
        error: 'Permission denied',
      },
      { status: 403 }
    );
  }
  
  if (error.code === 'not-found' || error.code === 'NOT_FOUND') {
    return NextResponse.json(
      {
        ...errorResponse,
        error: 'Resource not found',
      },
      { status: 404 }
    );
  }
  
  if (error.code === 'invalid-argument' || error.code === 'INVALID_ARGUMENT') {
    return NextResponse.json(
      {
        ...errorResponse,
        error: 'Invalid input data',
      },
      { status: 400 }
    );
  }
  
  // Return generic error response
  return NextResponse.json(errorResponse, { status });
}

/**
 * Create a standardized API success response
 * 
 * @param data The response data
 * @param message Optional success message
 * @param status HTTP status code
 * @returns NextResponse with standardized success format
 */
export function createApiResponse<T>(data: T, message?: string, status: number = 200): NextResponse {
  const response: ApiSuccess<T> = {
    success: true,
    data,
  };
  
  if (message) {
    response.message = message;
  }
  
  return NextResponse.json(response, { status });
}
