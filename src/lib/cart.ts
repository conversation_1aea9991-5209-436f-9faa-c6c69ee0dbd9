import { CartItem } from '@/types';

// Local storage key for cart
const CART_STORAGE_KEY = 'belinda_cart';

// Get cart from local storage
export const getCart = (): CartItem[] => {
  if (typeof window === 'undefined') {
    return [];
  }
  
  const cartJson = localStorage.getItem(CART_STORAGE_KEY);
  return cartJson ? JSON.parse(cartJson) : [];
};

// Save cart to local storage
export const saveCart = (cart: CartItem[]): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
};

// Add item to cart
export const addToCart = (item: CartItem): CartItem[] => {
  const cart = getCart();
  
  // Check if item already exists in cart
  const existingItemIndex = cart.findIndex(
    cartItem => 
      cartItem.productId === item.productId && 
      cartItem.variantId === item.variantId
  );
  
  if (existingItemIndex !== -1) {
    // Update quantity if item exists
    cart[existingItemIndex].quantity += item.quantity;
  } else {
    // Add new item
    cart.push(item);
  }
  
  saveCart(cart);
  return cart;
};

// Update item quantity in cart
export const updateCartItemQuantity = (
  productId: string, 
  variantId: string | undefined, 
  quantity: number
): CartItem[] => {
  const cart = getCart();
  
  const itemIndex = cart.findIndex(
    item => 
      item.productId === productId && 
      item.variantId === variantId
  );
  
  if (itemIndex !== -1) {
    if (quantity <= 0) {
      // Remove item if quantity is 0 or negative
      cart.splice(itemIndex, 1);
    } else {
      // Update quantity
      cart[itemIndex].quantity = quantity;
    }
    
    saveCart(cart);
  }
  
  return cart;
};

// Remove item from cart
export const removeFromCart = (
  productId: string, 
  variantId: string | undefined
): CartItem[] => {
  const cart = getCart();
  
  const updatedCart = cart.filter(
    item => 
      !(item.productId === productId && item.variantId === variantId)
  );
  
  saveCart(updatedCart);
  return updatedCart;
};

// Clear cart
export const clearCart = (): void => {
  saveCart([]);
};

// Calculate cart totals
export const calculateCartTotals = (cart: CartItem[]): {
  subtotal: number;
  itemCount: number;
} => {
  return cart.reduce(
    (totals, item) => {
      return {
        subtotal: totals.subtotal + item.price * item.quantity,
        itemCount: totals.itemCount + item.quantity,
      };
    },
    { subtotal: 0, itemCount: 0 }
  );
};
