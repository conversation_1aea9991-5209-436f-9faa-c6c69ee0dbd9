/**
 * Uploads an image to Firebase Storage directly using the client SDK
 * @param file The file to upload
 * @param folder The folder to upload to (default: 'products')
 * @returns The URL of the uploaded image
 */
export async function uploadImage(file: File, folder: string = 'products'): Promise<string> {
  try {
    // Import the Firebase storage functions dynamically to avoid SSR issues
    const { ref, uploadBytesResumable, getDownloadURL } = await import('firebase/storage');
    const { storage } = await import('@/lib/firebase/services/storage-service');

    // Create a unique filename
    const filename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;

    // Create storage reference
    const storageRef = ref(storage, `${folder}/${filename}`);

    // Upload file
    const uploadTask = uploadBytesResumable(storageRef, file);

    // Wait for upload to complete
    return new Promise((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Track upload progress if needed
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          console.log(`Upload progress: ${progress}%`);
        },
        (error) => {
          // Handle upload error
          console.error('Error uploading image:', error);
          reject(error);
        },
        async () => {
          // Upload completed successfully, get download URL
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          resolve(downloadURL);
        }
      );
    });
  } catch (error) {
    console.error('Error in uploadImage:', error);
    throw error;
  }
}

/**
 * Uploads multiple images to Firebase Storage directly using the client SDK
 * @param files Array of files to upload
 * @param folder The folder to upload to (default: 'products')
 * @returns Array of URLs of the uploaded images
 */
export async function uploadMultipleImages(
  files: File[],
  folder: string = 'products'
): Promise<string[]> {
  try {
    // Upload all images in parallel
    const uploadPromises = files.map(file => uploadImage(file, folder));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
}
