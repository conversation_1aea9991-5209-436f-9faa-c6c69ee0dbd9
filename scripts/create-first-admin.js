/**
 * <PERSON><PERSON><PERSON> to create the first admin user for the application
 * 
 * Usage:
 * node scripts/create-first-admin.js <email> <password>
 * 
 * This script should be run once when setting up the application for the first time.
 * It will create a new user with admin role if the user doesn't exist,
 * or update an existing user to have admin role.
 */

import admin from 'firebase-admin';
import path from 'path';
import fs from 'fs';

// Check for required arguments
if (process.argv.length < 4) {
  console.error('Usage: node scripts/create-first-admin.js <email> <password>');
  process.exit(1);
}

const email = process.argv[2];
const password = process.argv[3];

// Validate email format
if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
  console.error('Error: Please provide a valid email address');
  process.exit(1);
}

// Validate password length
if (password.length < 6) {
  console.error('Error: Password must be at least 6 characters long');
  process.exit(1);
}

// Initialize Firebase Admin SDK
async function initializeFirebaseAdmin() {
  try {
    // Check if service account key file exists
    const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');
    
    if (fs.existsSync(serviceAccountPath)) {
      // Use service account file
      const serviceAccount = await import(serviceAccountPath).then(module => module.default || module);
      
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });
      
      console.log('Firebase Admin initialized with service account file');
    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      // Use service account from environment variable
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });
      
      console.log('Firebase Admin initialized with service account from environment variable');
    } else if (process.env.FIREBASE_PROJECT_ID && 
               process.env.FIREBASE_CLIENT_EMAIL && 
               process.env.FIREBASE_PRIVATE_KEY) {
      // Use individual environment variables
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        })
      });
      
      console.log('Firebase Admin initialized with environment variables');
    } else {
      // Use application default credentials
      admin.initializeApp({
        credential: admin.credential.applicationDefault()
      });
      
      console.log('Firebase Admin initialized with application default credentials');
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    return false;
  }
}

// Create or update user with admin role
async function createOrUpdateAdminUser(email, password) {
  try {
    const auth = admin.auth();
    const firestore = admin.firestore();
    
    let uid;
    
    try {
      // Check if user already exists
      const userRecord = await auth.getUserByEmail(email);
      uid = userRecord.uid;
      console.log(`User ${email} already exists with UID: ${uid}`);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        // Create new user
        const newUser = await auth.createUser({
          email,
          password,
          emailVerified: true,
        });
        
        uid = newUser.uid;
        console.log(`Created new user ${email} with UID: ${uid}`);
      } else {
        throw error;
      }
    }
    
    // Define admin permissions
    const permissions = [
      'read:products', 'write:products', 'delete:products',
      'read:categories', 'write:categories', 'delete:categories',
      'read:orders', 'write:orders', 'delete:orders',
      'read:users', 'write:users', 'delete:users',
      'read:settings', 'write:settings',
      'read:dashboard', 'read:reports'
    ];
    
    // Set custom claims
    await auth.setCustomUserClaims(uid, {
      role: 'admin',
      permissions,
      admin: true,
      editor: true,
      viewer: true
    });
    
    console.log(`Set admin role and permissions for user ${email}`);
    
    // Update user in Firestore
    await firestore.collection('users').doc(uid).set({
      email,
      role: 'admin',
      permissions,
      isAdmin: true,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    }, { merge: true });
    
    console.log(`Updated Firestore document for user ${email}`);
    
    return true;
  } catch (error) {
    console.error('Error creating/updating admin user:', error);
    return false;
  }
}

// Main function
async function main() {
  console.log('Creating first admin user...');
  
  // Initialize Firebase Admin
  const initialized = initializeFirebaseAdmin();
  if (!initialized) {
    console.error('Failed to initialize Firebase Admin SDK');
    process.exit(1);
  }
  
  // Create or update admin user
  const success = await createOrUpdateAdminUser(email, password);
  if (!success) {
    console.error('Failed to create/update admin user');
    process.exit(1);
  }
  
  console.log('✅ First admin user created successfully!');
  console.log(`Email: ${email}`);
  console.log('You can now log in to the admin dashboard with these credentials.');
  
  process.exit(0);
}

// Run the script
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
