import path from 'path';
import fs from 'fs';
import { createCanvas } from 'canvas';

// Create directory if it doesn't exist
const imagesDir = path.join(__dirname, '../public/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to create a placeholder image
function createPlaceholderImage(filename, width, height, bgColor, text) {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Fill background
  ctx.fillStyle = bgColor;
  ctx.fillRect(0, 0, width, height);

  // Add text
  ctx.fillStyle = '#FFFFFF';
  ctx.font = 'bold 40px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, width / 2, height / 2);

  // Save to file
  const buffer = canvas.toBuffer('image/jpeg');
  fs.writeFileSync(path.join(imagesDir, filename), buffer);
  console.log(`Created ${filename}`);
}

// Create hero images
createPlaceholderImage('hero-1.jpg', 1920, 820, '#3B82F6', 'Hero Image 1');
createPlaceholderImage('hero-2.jpg', 1920, 820, '#10B981', 'Hero Image 2');
createPlaceholderImage('hero-3.jpg', 1920, 820, '#8B5CF6', 'Hero Image 3');

// Create category images
createPlaceholderImage('category-curtains.jpg', 600, 600, '#F59E0B', 'Curtains');
createPlaceholderImage('category-walls.jpg', 600, 600, '#EC4899', 'Wall Designs');
createPlaceholderImage('category-new.jpg', 600, 600, '#6366F1', 'New Arrivals');

// Create product images
createPlaceholderImage('curtain-1.jpg', 500, 500, '#3B82F6', 'Curtain 1');
createPlaceholderImage('curtain-2.jpg', 500, 500, '#2563EB', 'Curtain 2');
createPlaceholderImage('wall-1.jpg', 500, 500, '#10B981', 'Wall Design 1');
createPlaceholderImage('wall-2.jpg', 500, 500, '#059669', 'Wall Design 2');

// Create promo banner
createPlaceholderImage('promo-banner.jpg', 800, 450, '#F97316', 'Special Offer');

console.log('All placeholder images created successfully!');
