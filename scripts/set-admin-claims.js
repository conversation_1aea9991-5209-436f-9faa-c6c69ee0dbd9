// Create a new file to set admin claims for an existing user

import admin from 'firebase-admin';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path to service account file
const serviceAccountPath = path.join(__dirname, '../serviceAccount.json');

// Initialize Firebase Admin
function initializeFirebaseAdmin() {
  if (admin.apps.length) return;
  
  if (fs.existsSync(serviceAccountPath)) {
    // Use service account file
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    
    console.log('Firebase Admin initialized with service account file');
  } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    // Use service account from environment variable
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    
    console.log('Firebase Admin initialized with service account from environment variable');
  } else if (process.env.FIREBASE_PROJECT_ID && 
             process.env.FIREBASE_CLIENT_EMAIL && 
             process.env.FIREBASE_PRIVATE_KEY) {
    // Use individual environment variables
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      })
    });
    
    console.log('Firebase Admin initialized with environment variables');
  } else {
    // Use application default credentials
    admin.initializeApp({
      credential: admin.credential.applicationDefault()
    });
    
    console.log('Firebase Admin initialized with application default credentials');
  }
}

async function setAdminClaims(email) {
  try {
    initializeFirebaseAdmin();
    
    const auth = admin.auth();
    const firestore = admin.firestore();
    
    // Try to get user by email
    let userRecord;
    try {
      userRecord = await auth.getUserByEmail(email);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        // If not found, try listing users and finding by email case-insensitive
        console.log(`User not found with exact email. Trying to find with case-insensitive match...`);
        
        // List users (limited to first 1000)
        const listUsersResult = await auth.listUsers(1000);
        
        // Find user with case-insensitive email match
        const matchingUser = listUsersResult.users.find(
          user => user.email && user.email.toLowerCase() === email.toLowerCase()
        );
        
        if (matchingUser) {
          userRecord = matchingUser;
          console.log(`Found user with case-insensitive email match: ${matchingUser.email}`);
        } else {
          throw new Error(`No user found with email ${email} (case-insensitive search)`);
        }
      } else {
        throw error;
      }
    }
    
    const uid = userRecord.uid;
    console.log(`Found user with email ${userRecord.email}, uid: ${uid}`);
    
    // Define admin permissions
    const permissions = [
      'read:products', 'write:products', 'delete:products',
      'read:categories', 'write:categories', 'delete:categories',
      'read:orders', 'write:orders', 'delete:orders',
      'read:users', 'write:users', 'delete:users',
      'read:settings', 'write:settings',
      'read:dashboard', 'read:reports'
    ];
    
    // Set custom claims
    await auth.setCustomUserClaims(uid, {
      role: 'admin',
      permissions,
      admin: true,
      editor: true,
      viewer: true
    });
    
    console.log(`Set admin role and permissions for user ${userRecord.email}`);
    
    // Update user in Firestore
    await firestore.collection('users').doc(uid).set({
      email: userRecord.email,
      role: 'admin',
      permissions,
      isAdmin: true,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    }, { merge: true });
    
    console.log(`Updated user document in Firestore for ${userRecord.email}`);
    console.log('Admin setup complete!');
    
    return { success: true, uid };
  } catch (error) {
    console.error('Error setting admin claims:', error);
    return { success: false, error: error.message };
  }
}

// Get email from command line arguments
const email = process.argv[2];

if (!email) {
  console.error('Please provide an email address as an argument');
  console.log('Usage: node set-admin-claims.js <EMAIL>');
  process.exit(1);
}

setAdminClaims(email)
  .then(result => {
    if (result.success) {
      console.log('Successfully set admin claims');
      process.exit(0);
    } else {
      console.error('Failed to set admin claims:', result.error);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
