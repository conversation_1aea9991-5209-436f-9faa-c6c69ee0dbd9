// Create a new file to set admin claims for an existing user by UID

import admin from 'firebase-admin';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path to service account file
const serviceAccountPath = path.join(__dirname, '../serviceAccount.json');

// Initialize Firebase Admin
function initializeFirebaseAdmin() {
  if (admin.apps.length) return;
  
  if (fs.existsSync(serviceAccountPath)) {
    // Use service account file
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    
    console.log('Firebase Admin initialized with service account file');
  } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    // Use service account from environment variable
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    
    console.log('Firebase Admin initialized with service account from environment variable');
  } else if (process.env.FIREBASE_PROJECT_ID && 
             process.env.FIREBASE_CLIENT_EMAIL && 
             process.env.FIREBASE_PRIVATE_KEY) {
    // Use individual environment variables
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      })
    });
    
    console.log('Firebase Admin initialized with environment variables');
  } else {
    // Use application default credentials
    admin.initializeApp({
      credential: admin.credential.applicationDefault()
    });
    
    console.log('Firebase Admin initialized with application default credentials');
  }
}

async function setAdminClaimsByUid(uid) {
  try {
    initializeFirebaseAdmin();
    
    const auth = admin.auth();
    const firestore = admin.firestore();
    
    // Get user by UID
    const userRecord = await auth.getUser(uid);
    const email = userRecord.email;
    
    console.log(`Found user with uid ${uid}, email: ${email}`);
    
    // Define admin permissions
    const permissions = [
      'read:products', 'write:products', 'delete:products',
      'read:categories', 'write:categories', 'delete:categories',
      'read:orders', 'write:orders', 'delete:orders',
      'read:users', 'write:users', 'delete:users',
      'read:settings', 'write:settings',
      'read:dashboard', 'read:reports'
    ];
    
    // Set custom claims
    await auth.setCustomUserClaims(uid, {
      role: 'admin',
      permissions,
      admin: true,
      editor: true,
      viewer: true
    });
    
    console.log(`Set admin role and permissions for user ${email}`);
    
    // Update user in Firestore
    await firestore.collection('users').doc(uid).set({
      email,
      role: 'admin',
      permissions,
      isAdmin: true,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    }, { merge: true });
    
    console.log(`Updated user document in Firestore for ${email}`);
    console.log('Admin setup complete!');
    
    return { success: true, uid };
  } catch (error) {
    console.error('Error setting admin claims:', error);
    return { success: false, error: error.message };
  }
}

// Get UID from command line arguments
const uid = process.argv[2];

if (!uid) {
  console.error('Please provide a user UID as an argument');
  console.log('Usage: node set-admin-by-uid.js USER_UID');
  process.exit(1);
}

setAdminClaimsByUid(uid)
  .then(result => {
    if (result.success) {
      console.log('Successfully set admin claims');
      process.exit(0);
    } else {
      console.error('Failed to set admin claims:', result.error);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });