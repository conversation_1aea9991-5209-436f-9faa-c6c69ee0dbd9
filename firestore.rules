rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        (request.auth.token.admin == true || request.auth.token.role == 'admin');
    }
    
    function isEditor() {
      return isAuthenticated() && 
        (isAdmin() || request.auth.token.role == 'editor');
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidProduct() {
      let requiredFields = ['name', 'price', 'category', 'status'];
      let hasAllRequired = requiredFields.size() == requiredFields.filter(field => request.resource.data[field] != null).size();
      
      return hasAllRequired && 
        request.resource.data.price is number && 
        request.resource.data.price > 0 &&
        request.resource.data.status in ['active', 'draft', 'archived'];
    }
    
    function isValidCategory() {
      let requiredFields = ['name', 'slug'];
      let hasAllRequired = requiredFields.size() == requiredFields.filter(field => request.resource.data[field] != null).size();
      
      return hasAllRequired;
    }
    
    // Products collection
    match /products/{productId} {
      allow read: if true; // Public read access
      allow create: if isEditor() && isValidProduct();
      allow update: if isEditor() && isValidProduct();
      allow delete: if isAdmin();
    }
    
    // Categories collection
    match /categories/{categoryId} {
      allow read: if true; // Public read access
      allow create: if isEditor() && isValidCategory();
      allow update: if isEditor() && isValidCategory();
      allow delete: if isAdmin();
    }
    
    // Orders collection
    match /orders/{orderId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow create: if isAuthenticated();
      allow update: if isAdmin() || 
        (isOwner(resource.data.userId) && 
         resource.data.status == 'pending');
      allow delete: if isAdmin();
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || isOwner(userId));
      allow create: if isAdmin();
      allow update: if isAdmin() || isOwner(userId);
      allow delete: if isAdmin();
    }
    
    // Settings collection
    match /settings/{settingId} {
      allow read: if true; // Public read access for settings
      allow write: if isAdmin();
    }
  }
}
