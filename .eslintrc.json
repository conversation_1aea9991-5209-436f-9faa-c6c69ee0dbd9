{"extends": "next/core-web-vitals", "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react/no-unescaped-entities": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-empty-object-type": "off", "@next/next/no-html-link-for-pages": "off", "@next/next/no-img-element": "off", "prefer-const": "off"}}