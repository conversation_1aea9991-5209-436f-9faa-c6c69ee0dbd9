rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        (request.auth.token.admin == true || request.auth.token.role == 'admin');
    }
    
    function isEditor() {
      return isAuthenticated() && 
        (isAdmin() || request.auth.token.role == 'editor');
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidImage() {
      // Check if the content type is an image
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidSize() {
      // Limit file size to 5MB
      return request.resource.size <= 5 * 1024 * 1024;
    }
    
    // Default rule - deny all
    match /{allPaths=**} {
      allow read, write: if false;
    }
    
    // Product images
    match /products/{imageId} {
      allow read: if true; // Public read access
      allow write: if isEditor() && isValidImage() && isValidSize();
      allow delete: if isAdmin();
    }
    
    // Category images
    match /categories/{imageId} {
      allow read: if true; // Public read access
      allow write: if isEditor() && isValidImage() && isValidSize();
      allow delete: if isAdmin();
    }
    
    // User profile images
    match /users/{userId}/{imageId} {
      allow read: if true; // Public read access
      allow write: if (isOwner(userId) || isAdmin()) && isValidImage() && isValidSize();
      allow delete: if isOwner(userId) || isAdmin();
    }
    
    // Temporary uploads
    match /uploads/{imageId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidImage() && isValidSize();
      allow delete: if isAuthenticated();
    }
  }
}
