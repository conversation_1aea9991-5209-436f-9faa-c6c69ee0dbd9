# Admin Setup Guide

This guide explains how to set up the first admin user for the application. The first admin user can then create additional admin users through the admin dashboard.

## Prerequisites

Before setting up the first admin user, make sure you have:

1. Set up Firebase project and configured the application to use it
2. Installed all dependencies with `npm install` or `yarn install`
3. Firebase Admin SDK credentials configured (see below)

## Firebase Admin SDK Configuration

The script needs Firebase Admin SDK credentials to create the first admin user. You can provide these credentials in one of the following ways:

### Option 1: Service Account Key File

1. Go to the Firebase Console > Project Settings > Service Accounts
2. Click "Generate new private key"
3. Save the JSON file as `service-account.json` in the root directory of the project

### Option 2: Environment Variables

Set the following environment variables:

```
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"...","private_key":"...","client_email":"..."}
```

Or set individual environment variables:

```
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

### Option 3: Application Default Credentials

If you're running the script in a Google Cloud environment or have set up application default credentials using the Firebase CLI (`firebase login:ci`), the script will use those credentials automatically.

## Creating the First Admin User

Run the following command to create the first admin user:

```bash
npm run create-admin <EMAIL> your-password
```

Or with yarn:

```bash
yarn create-admin <EMAIL> your-password
```

Replace `<EMAIL>` and `your-password` with the desired email and password for the admin user.

## What the Script Does

The script performs the following actions:

1. Checks if a user with the provided email already exists
   - If the user exists, it updates their role to admin
   - If the user doesn't exist, it creates a new user with the provided email and password
2. Sets custom claims in Firebase Auth to mark the user as an admin with all permissions
3. Creates or updates the user document in Firestore with admin role and permissions

## After Setup

After creating the first admin user, you can:

1. Log in to the admin dashboard using the email and password you provided
2. Create additional admin users through the admin dashboard
3. Manage roles and permissions for other users

## Security Considerations

- Keep the `service-account.json` file secure and do not commit it to version control
- Use a strong password for the admin user
- Run this script only once when setting up the application for the first time
- Consider deleting the script or restricting access to it after the first admin user is created

## Troubleshooting

If you encounter any issues:

1. Make sure Firebase Admin SDK credentials are correctly configured
2. Check that the Firebase project has Email/Password authentication enabled
3. Verify that the Firestore database is set up and accessible
4. Check the console output for detailed error messages
